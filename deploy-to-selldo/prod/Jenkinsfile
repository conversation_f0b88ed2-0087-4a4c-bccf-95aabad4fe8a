podTemplate(
    containers: [
        containerTemplate(name: 'helm', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/lachlanevenson/k8s-helm:v3.4.2', command: 'cat', ttyEnabled: true),
        containerTemplate(name: 'curl', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/sling/jenkins/curl', command: 'cat', ttyEnabled: true)
    ],
    imagePullSecrets: ['registry-credentials']) {
  properties([parameters(
      [string(name: 'dockerImageTag', defaultValue: 'latest', description: 'Docker image tag to deploy'),
       string(name: 'branchName', defaultValue: 'master', description: 'Branch being deployed')])])

  currentBuild.description = "branch ${params.branchName}"
  node(POD_LABEL) {
    try {
      stage('Approval for Sell Do Deployment') {
        userInput = input(id: 'confirm', message: 'Do you wish to deploy to PROD environment?',
            parameters: [[$class: 'BooleanParameterDefinition', defaultValue: false, description: 'This will deploy the current build in PROD environment', name: 'confirm']])
      }
      container('helm') {
        withCredentials([[$class       : 'FileBinding',
                          credentialsId: 'selldo-prod-kubeconfig',
                          variable     : 'KUBECONFIG'],
                         [$class       : 'StringBinding',
                          credentialsId: 'sd-charts-github-api-token',
                          variable     : 'API_TOKEN']]) {
          stage('Add Helm repository') {
            sh script: "helm repo add stable 'https://charts.helm.sh/stable'",
                label: 'Add stable helm repo'
            sh script: "helm repo add sd-charts 'https://${API_TOKEN}@raw.githubusercontent.com/amuratech/sd-charts/master/'",
                label: 'Add helm repo'
            sh script: 'helm repo list', label: 'List available helm repos'
          }
          withCredentials([[$class       : 'StringBinding',
                            credentialsId: 'selldo-prod-application-namespace',
                            variable     : 'APP_NAMESPACE'],
                           [$class       : 'StringBinding',
                            credentialsId: 'selldo-prod-env-postgres-username',
                            variable     : 'POSTGRES_USERNAME'],
                           [$class       : 'StringBinding',
                            credentialsId: 'selldo-prod-env-postgres-password',
                            variable     : 'POSTGRES_PASSWORD'],
                           [$class       : 'StringBinding',
                            credentialsId: 'selldo-prod-env-postgres-host',
                            variable     : 'POSTGRES_HOST'],
                           [$class       : 'StringBinding',
                            credentialsId: 'selldo-prod-env-postgres-port',
                            variable     : 'POSTGRES_PORT'],
                           [$class       : 'StringBinding',
                            credentialsId: 'selldo-prod-env-postgres-flags',
                            variable     : 'POSTGRES_FLAGS'],
                           [$class       : 'StringBinding',
                            credentialsId: 'selldo-prod-env-rabbitmq-username',
                            variable     : 'RABBITMQ_USERNAME'],
                           [$class       : 'StringBinding',
                            credentialsId: 'selldo-prod-env-rabbitmq-password',
                            variable     : 'RABBITMQ_PASSWORD']]) {
            stage('Deploy') {
              echo "Deploying docker release -> nexus.sling-dev.com/8023/sling/sd-report:${params.dockerImageTag}"
              sh script: "helm upgrade --install sd-report sd-charts/sd-report " +
                  "--set appConfig.rabbitmq.password=${RABBITMQ_PASSWORD}," +
                  "appConfig.rabbitmq.username=${RABBITMQ_USERNAME}," +
                  "appConfig.postgres.hostname=${POSTGRES_HOST}," +
                  "appConfig.postgres.port=${POSTGRES_PORT}," +
                  "appConfig.postgres.flags='${POSTGRES_FLAGS}'," +
                  "appConfig.postgres.username=${POSTGRES_USERNAME}," +
                  "appConfig.postgres.password=${POSTGRES_PASSWORD}," +
                  "image.tag=${params.dockerImageTag}," +
                  "namespace=${APP_NAMESPACE}," +
                  "deployment.annotations.buildNumber=${currentBuild.number} " +
                  "--wait",
                  label: 'Install helm release'
            }
          }
        }
      }
      container('curl') {
       withCredentials([[$class      : 'StringBinding',
                           credentialsId: 'selldo-prod-api-host',
                           variable     : 'SELL_DO_PROD_API_HOST']]) {
           stage('Refresh Gateway routes') {
              sh script: "curl -X " +
                          "POST https://${SELL_DO_PROD_API_HOST}/actuator/gateway/refresh " +
                          "-H 'Accept: application/json' " +
                          "-H 'Host: ${SELL_DO_PROD_API_HOST}' " +
                          "-H 'cache-control: no-cache'",
                 label: 'Force refresh routes cache'
           }
        }
      }
    }
    catch (err) {
      def user = err.getCauses()[0].getUser()
      userInput = false
      echo "Aborted by: [${user}]"
    }
  }
}
