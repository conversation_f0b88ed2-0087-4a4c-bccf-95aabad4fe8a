package com.sling.sales.report;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;

import com.sling.sales.report.core.api.request.AggregationRequestV3;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.security.jwt.Authentication;
import com.sling.sales.report.setup.TestDatabaseInitializer;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestDatabaseInitializer.class})
@AutoConfigureWireMock(port = 9090)
@TestPropertySource(properties = {
    "client.config.basePath=http://localhost:9090",
    "client.deal.basePath=http://localhost:9090",
    "client.sales.basePath=http://localhost:9090",
    "client.meeting.basePath=http://localhost:9090",
    "client.email.basePath=http://localhost:9090"
})
public class DownloadReportIntegrationTests {

  String accessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dY87hlbBD7CqbVSiosW2W8MGri6x4eYeFANAfkD027s";
  @Autowired
  Environment environment;
  @Autowired
  private ResourceLoader resourceLoader;

  @Test
  @Sql("/test-scripts/insert-deals.sql")
  public void givenOneDimensionalReport_withMultipleMetrics_shouldDownloadIt() throws IOException {
    //given
    SecurityContextHolder.getContext()
        .setAuthentication(Authentication.from(accessToken, "test"));

    GroupByField groupByField = new GroupByField("ownedBy", null, null, null);
    List<Metric> metrics = List.of(new Metric(MetricType.COUNT, "id", "ID"), new Metric(MetricType.SUM, "estimatedValue", "Estimated Value"),
        new Metric(MetricType.AVERAGE, "estimatedValue", "Estimated Value"));
    Filter filter = new Filter("between", "createdAt", "createdAt", "date", List.of("2020-02-01T05:43:45.717000Z",
        "2020-02-03T05:43:45.717000Z"), null, null, "DATE_PICKER", null, null);
    AggregationRequestV3 aggregationRequestV3 = new AggregationRequestV3(Collections.emptyList(), List.of(groupByField), filter, metrics,
        Collections.emptyList());

    stubFor(
        get("/v1/deals/layout/list")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        "{\"pageConfig\": {\"tableConfig\":{\"columns\": [{\"id\": \"ownedBy\", \"header\": \"Owner\"}, {\"id\": \"estimatedValue\", \"header\": \"Estimated Value\"}, {\"id\": \"createdAt\", \"header\": \"Created At\"}]}}}")));

    stubFor(
        get("/v1/entities/label")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"DEAL\": {\"displayName\": \"Deal\", \"displayNamePlural\": \"deals\"}}")));

    //when
    var resourceMono =
        buildWebClient()
            .post()
            .uri("/v3/reports/1111/download?timezone=Asia/Calcutta&baseCurrencyId=413&baseCurrency=INR")
            .bodyValue(aggregationRequestV3)
            .retrieve()
            .bodyToMono(Resource.class);

    //then
    String expectedCsvAsString = getResourceAsString("classpath:contract/it/one-dimensional-report-with-multiple-metrics.csv");
    StepVerifier.create(resourceMono)
        .expectNextMatches(
            resource -> {
              try {
                String actualCsvAsString = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
                Assertions.assertThat(actualCsvAsString).isEqualToIgnoringWhitespace(expectedCsvAsString);
                return true;
              } catch (IOException e) {
                Assertions.fail(e.getMessage());
              }
              return false;
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/insert-deals.sql")
  public void givenMultiDimensionalReport_withMultipleMetrics_shouldDownloadIt() throws IOException {
    //given
    SecurityContextHolder.getContext()
        .setAuthentication(Authentication.from(accessToken, "test"));

    stubFor(
        get("/v1/deals/layout/list")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        "{\"pageConfig\": {\"tableConfig\":{\"columns\": [{\"id\": \"ownedBy\", \"header\": \"Owner\"}, {\"id\": \"estimatedValue\", \"header\": \"Estimated Value\"}, {\"id\": \"company\", \"header\": \"Company\"}, {\"id\": \"products\", \"header\": \"Product and Services\"}, {\"id\": \"createdAt\", \"header\": \"Created At\"}]}}}")));

    stubFor(
        get("/v1/entities/label")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"DEAL\": {\"displayName\": \"Deal\", \"displayNamePlural\": \"multiple deals\"}}")));

    //when
    var resourceMono =
        buildWebClient()
            .get()
            .uri("/v2/reports/1112/download?timezone=Asia/Calcutta&baseCurrencyId=43&baseCurrency=INR")
            .retrieve()
            .bodyToMono(Resource.class);

    //then
    String expectedCsvAsString = getResourceAsString("classpath:contract/it/multi-dimensional-report-with-multiple-metrics.csv");
    StepVerifier.create(resourceMono)
        .expectNextMatches(
            resource -> {
              try {
                String actualCsvAsString = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
                Assertions.assertThat(actualCsvAsString).isEqualToIgnoringWhitespace(expectedCsvAsString);
                return true;
              } catch (IOException e) {
                Assertions.fail(e.getMessage());
              }
              return false;
            })
        .verifyComplete();
  }


  @Test
  @Sql("/test-scripts/insert-meeting-records-for-invitee-dimension.sql")
  public void givenMeetingOneDimensionalReport_withInviteeDimension_shouldDownloadItWithRecordDetails() throws IOException {
    //given
    SecurityContextHolder.getContext()
        .setAuthentication(Authentication.from(accessToken, "test"));

    stubFor(
        get("/v1/meetings/layout/list")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        "{\"pageConfig\": {\"tableConfig\":{\"columns\": [{\"id\": \"participants\", \"header\": \"Invitees\"}, {\"id\": \"createdAt\", \"header\": \"Created At\"}]}}}")));

    stubFor(
        get("/v1/entities/label")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"MEETING\": {\"displayName\": \"Meeting\", \"displayNamePlural\": \"Meetings\"}}")));

    //when
    var resourceMono =
        buildWebClient()
            .get()
            .uri("/v3/reports/9876/download?timezone=Asia/Calcutta&baseCurrencyId=43&baseCurrency=INR")
            .retrieve()
            .bodyToMono(Resource.class);

    //then
    String expectedCsvAsString = getResourceAsString("classpath:contract/it/meeting-one-dimensional-report-with-invitee-dimension.csv");
    StepVerifier.create(resourceMono)
        .expectNextMatches(
            resource -> {
              try {
                String actualCsvAsString = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
                Assertions.assertThat(actualCsvAsString).isEqualToIgnoringWhitespace(expectedCsvAsString);
                return true;
              } catch (IOException e) {
                Assertions.fail(e.getMessage());
              }
              return false;
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/insert_emails_aggrgation_1.sql")
  public void givenEmailOneDimensionalReport_shouldDownloadItWithRecordDetails() throws IOException {
    //given
    SecurityContextHolder.getContext()
        .setAuthentication(Authentication.from(accessToken, "test"));

    stubFor(
        get("/v1/emails/layout/list")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(getResourceAsString("classpath:contract/config/response/email-layout.json"))));

    stubFor(
        get("/v1/entities/label")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"EMAIL\": {\"displayName\": \"Email\", \"displayNamePlural\": \"Emails\"}}")));

    //when
    var resourceMono =
        buildWebClient()
            .get()
            .uri("/v3/reports/9876/download?timezone=Asia/Calcutta&baseCurrencyId=43&baseCurrency=INR")
            .retrieve()
            .bodyToMono(Resource.class);

    //then
    String expectedCsvAsString = getResourceAsString("classpath:contract/it/email-one-dimensional-report-with-sentby-dimension.csv");
    StepVerifier.create(resourceMono)
        .expectNextMatches(
            resource -> {
              try {
                String actualCsvAsString = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
                Assertions.assertThat(actualCsvAsString).isEqualToIgnoringWhitespace(expectedCsvAsString);
                return true;
              } catch (IOException e) {
                Assertions.fail(e.getMessage());
              }
              return false;
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/meeting-with-goal-and-report.sql")
  public void givenMeetingGoalReport_shouldDownloadIt() throws IOException {
    //given
    stubFor(
        get("/v1/meetings/layout/list")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        "{\"pageConfig\": {\"tableConfig\":{\"columns\": [{\"id\": \"participants\", \"header\": \"Invitees\"}, {\"id\": \"createdAt\", \"header\": \"Created At\"}]}}}")));

    stubFor(
        get("/v1/entities/label")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"MEETING\": {\"displayName\": \"Meeting\", \"displayNamePlural\": \"Meetings\"}}")));

    //when
    var port = environment.getProperty("local.server.port");
    WebClient webClient = WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(
            HttpHeaders.AUTHORIZATION,
            "Bearer eyJhbGciOiJIUzI1NiJ9.eyJkYXRhIjp7ImFjY2Vzc1Rva2VuIjoiMWM3N2MzMmYtNWY1Yi00MzQyLTkxYjItOTYxZTM4MDQ1NDk4IiwiZXhwaXJlc0luIjozNjAwLCJleHBpcnkiOjE2ODQxNTM2MDMsInVzZXJJZCI6MTAwMDEsInRlbmFudElkIjoxMDAwMCwidG9rZW5UeXBlIjoiQmVhcmVyIiwicGVybWlzc2lvbnMiOlt7Im5hbWUiOiJnb2FsIiwiZGVzY3JpcHRpb24iOiJoYXMgYWNjZXNzIHRvIGxlYWQgcmVzb3VyY2UiLCJsaW1pdHMiOi0xLCJ1bml0cyI6ImNvdW50IiwiYWN0aW9uIjp7InJlYWQiOnRydWUsInJlYWRBbGwiOnRydWV9fSx7Im5hbWUiOiJyZXBvcnQiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6ImRlYWwiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6Im1lZXRpbmciLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbWVldGluZyByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19XX19.xKGIbSW9ho6wLFPp3QXqr08L24aq3KoyyRiSQqnSCnQ")
        .build();

    var resourceMono =
        webClient
            .get()
            .uri("/v3/reports/1110/download?timezone=Asia/Calcutta&baseCurrencyId=43&baseCurrency=INR")
            .retrieve()
            .bodyToMono(Resource.class);

    //then
    String expectedCsvAsString = getResourceAsString("classpath:contract/it/meeting-goal-report-with-invitee-dimension.csv");
    StepVerifier.create(resourceMono)
        .expectNextMatches(
            resource -> {
              try {
                String actualCsvAsString = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
                Assertions.assertThat(actualCsvAsString).isEqualToIgnoringWhitespace(expectedCsvAsString);
                return true;
              } catch (IOException e) {
                Assertions.fail(e.getMessage());
              }
              return false;
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/lead-with-goal-and-report.sql")
  public void givenLeadGoalReport_shouldDownloadIt() throws IOException {
    //given
    stubFor(
        get("/v1/leads/layout/list")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"pageConfig\": {\"tableConfig\":{\"columns\": [{\"id\": \"ownerId\", \"header\": \"Owner\"}]}}}")));

    stubFor(
        get("/v1/entities/label")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"LEAD\": {\"displayName\": \"Lead\", \"displayNamePlural\": \"leads\"}}")));

    //when
    var port = environment.getProperty("local.server.port");
    WebClient webClient = WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(
            HttpHeaders.AUTHORIZATION,
            "Bearer eyJhbGciOiJIUzI1NiJ9.eyJkYXRhIjp7ImFjY2Vzc1Rva2VuIjoiMWM3N2MzMmYtNWY1Yi00MzQyLTkxYjItOTYxZTM4MDQ1NDk4IiwiZXhwaXJlc0luIjozNjAwLCJleHBpcnkiOjE2ODQxNTM2MDMsInVzZXJJZCI6MTAwMDEsInRlbmFudElkIjoxMDAwMCwidG9rZW5UeXBlIjoiQmVhcmVyIiwicGVybWlzc2lvbnMiOlt7Im5hbWUiOiJnb2FsIiwiZGVzY3JpcHRpb24iOiJoYXMgYWNjZXNzIHRvIGxlYWQgcmVzb3VyY2UiLCJsaW1pdHMiOi0xLCJ1bml0cyI6ImNvdW50IiwiYWN0aW9uIjp7InJlYWQiOnRydWUsInJlYWRBbGwiOnRydWV9fSx7Im5hbWUiOiJyZXBvcnQiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6ImRlYWwiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6Im1lZXRpbmciLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbWVldGluZyByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6ImxlYWQiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19XX19.agXtuY69XVt8ssZxRosouh9_EUyDDaJVIb4Z6u0I1xs")
        .build();

    var resourceMono =
        webClient
            .get()
            .uri("/v3/reports/1110/download?timezone=Asia/Calcutta&baseCurrencyId=43&baseCurrency=INR")
            .retrieve()
            .bodyToMono(Resource.class);

    //then
    String expectedCsvAsString = getResourceAsString("classpath:contract/it/lead-goal-report-with-owner-dimension.csv");
    StepVerifier.create(resourceMono)
        .expectNextMatches(
            resource -> {
              try {
                String actualCsvAsString = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
                Assertions.assertThat(actualCsvAsString).isEqualToIgnoringWhitespace(expectedCsvAsString);
                return true;
              } catch (IOException e) {
                Assertions.fail(e.getMessage());
              }
              return false;
            })
        .verifyComplete();
  }


  private WebClient buildWebClient() {
    var port = environment.getProperty("local.server.port");

    return WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(
            HttpHeaders.AUTHORIZATION,
            "Bearer "
                + accessToken)
        .build();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = resourceLoader.getResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }
}
