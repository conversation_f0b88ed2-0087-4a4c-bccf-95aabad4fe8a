package com.sling.sales.report;

import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;

import com.sling.sales.report.GoalIntegrationTests.TestEnvironmentSetup;
import com.sling.sales.report.core.api.request.NudgeUserRequest;
import com.sling.sales.report.mq.event.NudgeUserEvent;
import com.sling.sales.report.setup.TestDatabaseInitializer;
import java.io.IOException;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.reactive.function.client.WebClient;
import org.testcontainers.containers.RabbitMQContainer;
import reactor.test.StepVerifier;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestEnvironmentSetup.class, TestDatabaseInitializer.class})
@AutoConfigureWireMock(port = 9090)
@TestPropertySource(properties = {
    "client.iam.basePath=http://localhost:9090"
})
public class GoalIntegrationTests {

  private static final String REPORT_EXCHANGE = "ex.report";
  public static final String EXPECTED_QUEUE = "report";
  String accessToken = "eyJhbGciOiJIUzI1NiJ9.eyJkYXRhIjp7ImFjY2Vzc1Rva2VuIjoiMWM3N2MzMmYtNWY1Yi00MzQyLTkxYjItOTYxZTM4MDQ1NDk4IiwiZXhwaXJlc0luIjozNjAwLCJleHBpcnkiOjE2ODQxNTM2MDMsInVzZXJJZCI6MTAwMDEsInRlbmFudElkIjoxMDAwMCwidG9rZW5UeXBlIjoiQmVhcmVyIiwicGVybWlzc2lvbnMiOlt7Im5hbWUiOiJnb2FsIiwiZGVzY3JpcHRpb24iOiJoYXMgYWNjZXNzIHRvIGxlYWQgcmVzb3VyY2UiLCJsaW1pdHMiOi0xLCJ1bml0cyI6ImNvdW50IiwiYWN0aW9uIjp7InJlYWQiOnRydWUsInJlYWRBbGwiOnRydWV9fSx7Im5hbWUiOiJyZXBvcnQiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6ImRlYWwiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6Im1lZXRpbmciLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbWVldGluZyByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6ImxlYWQiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZSwicmVhZEFsbCI6dHJ1ZSwiY2FsbCI6dHJ1ZX19LHsibmFtZSI6ImNhbGwiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gY2FsbCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZSwicmVhZEFsbCI6dHJ1ZX19XX19.WTIoVN22GvQ7_w8ujlk_fZw_fv9kNEPWWS9KvdCJLDc";
  @Autowired
  Environment environment;
  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private ConnectionFactory connectionFactory;
  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  @Test
  @Sql("/test-scripts/deal-goal-with-default-report.sql")
  public void givenShareRuleAchievementRequest_shouldReturnAchievementForCurrentUser() throws IOException {
    // given
    // when
    var responseMono =
        buildWebClient()
            .get()
            .uri("/v1/reports/goals/1000/achievement?timezone=Asia/Kolkata")
            .retrieve()
            .bodyToMono(String.class);
    // then
    var response = getResourceAsString("classpath:contract/it/goal-achievement-for-current-user.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(response, jsonResponse, JSONCompareMode.LENIENT);
          } catch (JSONException e) {
            e.printStackTrace();
          }
        })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/deal-goal-with-default-report.sql")
  public void givenNudgeUserRequest_ShouldSendNudgeUserEvent() throws Exception {
    //given
    MockMqListener nudgeUserEventListener = new MockMqListener();
    SimpleMessageListenerContainer container = initializeRabbitMqListener(nudgeUserEventListener, NudgeUserEvent.getGoalNudgeUserEventName());
    NudgeUserRequest nudgeUserRequest = new NudgeUserRequest(List.of(10002L, 10003L,10004L),
        "Embrace the challenge and conquer {Goal - Name} with unwavering commitment. Your resilience and talent will lead us to remarkable success.","#FF0000" );

    //when
    var responseMono =  buildWebClient()
        .post()
        .uri("/v1/reports/goals/1000/nudge-user")
        .contentType(APPLICATION_JSON)
        .bodyValue(nudgeUserRequest)
        .retrieve()
        .toBodilessEntity();
//then
    nudgeUserEventListener.latch.await(5, TimeUnit.SECONDS);
    StepVerifier.create(responseMono)
        .expectNextMatches(responseEntity -> responseEntity.getStatusCode().equals(HttpStatus.OK))
        .verifyComplete();
    Assertions.assertThat(nudgeUserEventListener.actualMessages.get(0)).isEqualToIgnoringWhitespace(getResourceAsString("classpath:contract/it/nudge-user-notification-payload-for-user-1002.json"));
    Assertions.assertThat(nudgeUserEventListener.actualMessages.get(1)).isEqualToIgnoringWhitespace(getResourceAsString("classpath:contract/it/nudge-user-notification-payload-for-user-1003.json"));
    Assertions.assertThat(nudgeUserEventListener.actualMessages.get(2)).isEqualToIgnoringWhitespace(getResourceAsString("classpath:contract/it/nudge-user-notification-payload-for-user-1004.json"));
    container.stop();
  }

  @Test
  @Sql("/test-scripts/meeting-with-goal-and-report.sql")
  public void givenAchievementRequestForMeetingInviteeGoal_shouldReturnAchievementForCurrentUser() throws IOException {
    // given
    // when
    var responseMono =
        buildWebClient()
            .get()
            .uri("/v1/reports/goals/1000/achievement?timezone=Asia/Kolkata")
            .retrieve()
            .bodyToMono(String.class);

    // then
    var response = getResourceAsString("classpath:contract/it/meeting-goal-achievement-for-current-user.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(response, jsonResponse, JSONCompareMode.LENIENT);
          } catch (JSONException e) {
            Assertions.fail(e.getMessage());
          }
        })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/lead-with-goal-and-report.sql")
  public void givenAchievementRequestForLeadGoalOnOwnedByDimension_shouldReturnAchievementForCurrentUser() throws IOException {
    // given
    // when
    var responseMono =
        buildWebClient()
            .get()
            .uri("/v1/reports/goals/1000/achievement?timezone=Asia/Kolkata")
            .retrieve()
            .bodyToMono(String.class);

    // then
    var response = getResourceAsString("classpath:contract/it/lead-goal-achievement-for-current-user.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(response, jsonResponse, JSONCompareMode.LENIENT);
          } catch (JSONException e) {
            Assertions.fail(e.getMessage());
          }
        })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/call-with-goal-and-report.sql")
  public void givenAchievementRequestForCallGoalOnOwnerDimension_shouldReturnAchievementForCurrentUser() throws IOException {
    // given
    // when
    var responseMono =
        buildWebClient()
            .get()
            .uri("/v1/reports/goals/4001/achievement?timezone=Asia/Kolkata")
            .retrieve()
            .bodyToMono(String.class);

    // then
    var response = getResourceAsString("classpath:contract/it/call-goal-achievement-for-current-user.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(response, jsonResponse, JSONCompareMode.LENIENT);
          } catch (JSONException e) {
            Assertions.fail(e.getMessage());
          }
        })
        .verifyComplete();
  }

  private WebClient buildWebClient() {
    var port = environment.getProperty("local.server.port");

    return WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(
            HttpHeaders.AUTHORIZATION,
            "Bearer "
                + accessToken)
        .build();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = resourceLoader.getResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }
  private String getResponsePayload(String resourcePath) throws IOException {
    return IOUtils.toString(this.getClass().getResourceAsStream(resourcePath), "UTF-8");
  }

  class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;
    List<String> actualMessages = new LinkedList<>();

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
      actualMessages.add(this.actualMessage);
    }
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(MockMqListener mockMockMqListener, String eventName) {
    rabbitAdmin.declareBinding(
        BindingBuilder.bind(new Queue(EXPECTED_QUEUE)).to(new TopicExchange(REPORT_EXCHANGE)).with(eventName));
    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMockMqListener, "receiveMessage");
    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(EXPECTED_QUEUE);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  @TestConfiguration
  public static class TestEnvironmentSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer.withExchange(REPORT_EXCHANGE, "topic").withQueue(EXPECTED_QUEUE).start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.rabbitmq.virtual-host=" + "/");
    }
  }
}
