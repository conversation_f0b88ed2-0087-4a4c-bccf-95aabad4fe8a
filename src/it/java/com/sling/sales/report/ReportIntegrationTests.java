package com.sling.sales.report;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.api.request.FilterRequest;
import com.sling.sales.report.setup.TestDatabaseInitializer;
import java.io.IOException;
import java.util.List;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestDatabaseInitializer.class})
@AutoConfigureWireMock(port = 9090)
@TestPropertySource(properties = {
    "client.config.basePath=http://localhost:9090",
    "client.search.basePath=http://localhost:9090",
    "client.iam.basePath=http://localhost:9090"
})
public class ReportIntegrationTests {

  String access = "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzZWxsIiwiZGF0YSI6eyJleHBpcmVzSW4iOjEwMDAwLCJleHBpcnkiOjE2MjQzNDQ4Nzk0NTUsInRva2VuVHlwZSI6IkJlYXJlciIsInBlcm1pc3Npb25zIjpbeyJpZCI6NCwibmFtZSI6InJlcG9ydCIsImRlc2NyaXB0aW9uIjoiaGFzIGFjY2VzcyB0byByZXBvcnQgcmVzb3VyY2UiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZSwidXBkYXRlIjpmYWxzZSwiZGVsZXRlIjp0cnVlLCJ3cml0ZSI6ZmFsc2UsInJlYWRBbGwiOnRydWUsInVwZGF0ZUFsbCI6ZmFsc2UsImRlbGV0ZUFsbCI6dHJ1ZSwiY2FsbCI6ZmFsc2V9fV0sInVzZXJJZCI6IjkxNSIsInRlbmFudElkIjoiODg4In19.Qb34N7Sh4ydHnSaWMGi1sXfnJk622WrogdRZJX8njKg";
  @Autowired
  Environment environment;
  @Autowired
  private ResourceLoader resourceLoader;

  @Test
  @Sql({"/test-scripts/insert-into-currency.sql", "/test-scripts/create-reports.sql"})
  public void shouldGetReportListingPage() throws IOException {
    //given

    stubFor(
        get("/v2/users/915")
            .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer " + access))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("classpath:contract/it/user-915-response.json"))));

    //when
    var responseMono =
        buildWebClient()
            .get()
            .uri("/v2/reports/search?page=0&size=2&sort=createdAt,desc")
            .retrieve()
            .bodyToMono(String.class);
    //then
    var expectedResponse =
        getResourceAsString("classpath:contract/it/report-listing-page-response.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(expectedResponse, jsonResponse, new CustomComparator(JSONCompareMode.STRICT,
                new Customization("content[0].createdAt",(o, t1) -> true),
                new Customization("content[0].updatedAt",(o, t1) -> true),
                new Customization("content[1].createdAt",(o, t1) -> true),
                new Customization("content[1].updatedAt",(o, t1) -> true)
            ));
          } catch (JSONException e) {
            Assertions.fail(e.getMessage());
          }
        })
        .verifyComplete();
  }

  @Test
  @Sql({"/test-scripts/insert-into-currency.sql", "/test-scripts/create-reports.sql"})
  public void givenReportFilter_withSort_shouldGetReports() throws IOException {
    //given

    stubFor(
        get("/v2/users/915")
            .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer " + access))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("classpath:contract/it/user-915-response.json"))));

    List<FilterRequest.Filter> jsonRules = List
        .of(new FilterRequest.Filter("begins_with", "name", "string", "Lead"), new FilterRequest.Filter("equal", "createdBy", "long", 915L));
    FilterRequest filterRequest = new FilterRequest(jsonRules);

    //when
    var responseMono =
        buildWebClient()
            .post()
            .uri("/v2/reports/search?page=0&size=10&sort=createdAt,desc")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(new ObjectMapper().writeValueAsString(filterRequest))
            .retrieve()
            .bodyToMono(String.class);
    //then
    var expectedResponse =
        getResourceAsString("classpath:contract/it/report-listing-page-response-1.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(expectedResponse, jsonResponse, new CustomComparator(JSONCompareMode.STRICT,
                new Customization("content[0].createdAt",(o, t1) -> true),
                new Customization("content[0].updatedAt",(o, t1) -> true),
                new Customization("content[1].createdAt",(o, t1) -> true),
                new Customization("content[1].updatedAt",(o, t1) -> true)
                ));
          } catch (JSONException e) {
            Assertions.fail(e.getMessage());
          }
        })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/delete-report.sql")
  public void shouldDeleteReport() throws IOException {
    //given
    stubFor(
        get("/v2/users/915")
            .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer " + access))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("classpath:contract/it/user-915-response.json"))));

    //when
    var responseEntityMono = buildWebClient()
        .delete()
        .uri("/v2/reports/10")
        .retrieve()
        .toBodilessEntity();
    //then
    StepVerifier.create(responseEntityMono)
        .expectNextMatches(responseEntity -> responseEntity.getStatusCode().equals(HttpStatus.OK))
        .verifyComplete();
  }

  private WebClient buildWebClient() {
    var port = environment.getProperty("local.server.port");

    return WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(
            HttpHeaders.AUTHORIZATION,
            "Bearer "
                + access)
        .build();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = resourceLoader.getResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }
}
