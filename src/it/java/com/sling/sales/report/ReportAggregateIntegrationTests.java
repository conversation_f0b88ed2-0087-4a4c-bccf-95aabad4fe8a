package com.sling.sales.report;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;

import com.sling.sales.report.setup.TestDatabaseInitializer;
import java.io.IOException;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestDatabaseInitializer.class})
@AutoConfigureWireMock(port = 9090)
@TestPropertySource(properties = {
    "client.config.basePath=http://localhost:9090",
    "client.search.basePath=http://localhost:9090",
    "client.iam.basePath=http://localhost:9090",
    "client.sales.basePath=http://localhost:9090",
    "client.deal.basePath=http://localhost:9090",
    "client.company.basePath=http://localhost:9090",
    "client.meeting.basePath=http://localhost:9090",
    "client.call.basePath=http://localhost:9090"
})
public class ReportAggregateIntegrationTests {

  String accessToken = "eyJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PeXuSCFCWWuphMUuwj6UuPJLcdh5ImSyvjM_Jt5v2tE";
  @Autowired
  Environment environment;
  @Autowired
  private ResourceLoader resourceLoader;

  @Test
  @Sql("/test-scripts/insert-deals.sql")
  public void shouldGetReportForDeal() throws IOException {
    //given
    var requestPayload = getResourceAsString("classpath:contract/it/deal-report-request.json");

    //when
    var responseMono =
        buildWebClient()
            .post()
            .uri("/v3/reports/deals?baseCurrencyId=413")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(requestPayload)
            .retrieve()
            .bodyToMono(String.class);

    //then
    var response = getResourceAsString("classpath:contract/it/deal-report-response.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(response, jsonResponse, JSONCompareMode.LENIENT);
          } catch (JSONException e) {
            e.printStackTrace();
          }
        })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/create-report-data-for-summaries.sql")
  public void givenReportIdsForLeadReports_shouldReturnReportSummaries() throws IOException {
    //given
    stubFor(
        get("/v1/leads/layout/list")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"pageConfig\":{\"tableConfig\":{\"columns\":[{\"id\":\"ownerId\",\"header\":\"Owner\"},{\"id\":\"createdAt\",\"header\":\"Changes in Created At\"}]}}}")));

    stubFor(
        get("/v1/entities/label")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"LEAD\": {\"displayName\": \"Lead\", \"displayNamePlural\": \"leads\"}}")));
    //when
    var responseMono =
        buildWebClient()
            .get()
            .uri("/v3/reports/leads/summary?timezone=Asia/Calcutta&id=1&id=2")
            .retrieve()
            .bodyToMono(String.class);

    //then
    var response = getResourceAsString("classpath:contract/it/report-summaries-response.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(response, jsonResponse, JSONCompareMode.NON_EXTENSIBLE);
          } catch (JSONException e) {
            Assertions.fail(e.getMessage());
          }
        })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/create-report-data-for-summaries.sql")
  public void givenReportIdsForCompanyMultiMetricReports_shouldReturnReportSummaries() throws IOException {
    //given
    stubFor(
        get("/v1/companies/layout/list")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        "{\"pageConfig\":{\"tableConfig\":{\"columns\":[{\"id\":\"ownedBy\",\"header\":\"Owner\"},{\"id\":\"annualRevenue\",\"header\":\"Annual Revenue\"},{\"id\":\"createdAt\",\"header\":\"Created At Updated\"}]}}}")));

    stubFor(
        get("/v1/entities/label")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"COMPANY\": {\"displayName\": \"Company\", \"displayNamePlural\": \"companies\"}}")));
    //when
    var responseMono =
        buildWebClient()
            .get()
            .uri("/v3/reports/companies/summary?timezone=Asia/Calcutta&id=3&baseCurrencyId=413")
            .retrieve()
            .bodyToMono(String.class);

    //then
    var response = getResourceAsString("classpath:contract/it/report-company-summaries-response.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(response, jsonResponse, JSONCompareMode.NON_EXTENSIBLE);
          } catch (JSONException e) {
            Assertions.fail(e.getMessage());
          }
        })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/insert-deals.sql")
  public void shouldGetHierarchyDealReport() throws IOException {
    //given
    var requestPayload = getResourceAsString("classpath:contract/it/deal-hierarchy-report-request.json");

    stubFor(
        get("/v1/users/hierarchy")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(getResourceAsString("classpath:contract/user/users-hierarchy-response.json"))));


    //when
    var responseMono =
        buildWebClient()
            .post()
            .uri("/v3/reports/deals?baseCurrencyId=413")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(requestPayload)
            .retrieve()
            .bodyToMono(String.class);

    //then
    var response = getResourceAsString("classpath:contract/it/deal-hierarchy-report-response.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(response, jsonResponse, JSONCompareMode.LENIENT);
          } catch (JSONException e) {
            e.printStackTrace();
          }
        })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/create-report-data-for-summaries.sql")
  public void givenReportIdForLeadHierarchyReport_shouldReturnReportSummaries() throws IOException {
    //given
    stubFor(
        get("/v1/leads/layout/list")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"pageConfig\":{\"tableConfig\":{\"columns\":[{\"id\":\"ownerId\",\"header\":\"Owner\"},{\"id\":\"createdAt\",\"header\":\"Created At Updated\"}]}}}")));

    stubFor(
        get("/v1/entities/label")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"LEAD\": {\"displayName\": \"Lead\", \"displayNamePlural\": \"leads\"}}")));

    stubFor(
        get("/v1/users/hierarchy")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(getResourceAsString("classpath:contract/user/users-hierarchy-response.json"))));

    //when
    var responseMono =
        buildWebClient()
            .get()
            .uri("/v3/reports/leads/summary?timezone=Asia/Calcutta&id=10")
            .retrieve()
            .bodyToMono(String.class);

    //then
    var response = getResourceAsString("classpath:contract/it/hierarchy-report-summaries.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(response, jsonResponse, JSONCompareMode.NON_EXTENSIBLE);
          } catch (JSONException e) {
            Assertions.fail(e.getMessage());
          }
        })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/meeting-with-goal-and-report.sql")
  public void shouldGetReportSummaryForMeetingGoalReport() throws IOException {
    //given
    stubFor(
        get("/v1/meetings/layout/list")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"pageConfig\":{\"tableConfig\":{\"columns\":[{\"id\":\"participants\",\"header\":\"Meeting Invitees\"},{\"id\":\"createdAt\",\"header\":\"Created At Updated\"}]}}}")));

    stubFor(
        get("/v1/entities/label")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"MEETING\": {\"displayName\": \"Meeting\", \"displayNamePlural\": \"meetings\"}}")));

    //when
    var port = environment.getProperty("local.server.port");
    WebClient webClient = WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(
            HttpHeaders.AUTHORIZATION,
            "Bearer eyJhbGciOiJIUzI1NiJ9.eyJkYXRhIjp7ImFjY2Vzc1Rva2VuIjoiMWM3N2MzMmYtNWY1Yi00MzQyLTkxYjItOTYxZTM4MDQ1NDk4IiwiZXhwaXJlc0luIjozNjAwLCJleHBpcnkiOjE2ODQxNTM2MDMsInVzZXJJZCI6MTAwMDEsInRlbmFudElkIjoxMDAwMCwidG9rZW5UeXBlIjoiQmVhcmVyIiwicGVybWlzc2lvbnMiOlt7Im5hbWUiOiJnb2FsIiwiZGVzY3JpcHRpb24iOiJoYXMgYWNjZXNzIHRvIGxlYWQgcmVzb3VyY2UiLCJsaW1pdHMiOi0xLCJ1bml0cyI6ImNvdW50IiwiYWN0aW9uIjp7InJlYWQiOnRydWUsInJlYWRBbGwiOnRydWV9fSx7Im5hbWUiOiJyZXBvcnQiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6ImRlYWwiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6Im1lZXRpbmciLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbWVldGluZyByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19XX19.xKGIbSW9ho6wLFPp3QXqr08L24aq3KoyyRiSQqnSCnQ")
        .build();

    var responseMono =
        webClient
            .get()
            .uri("/v3/reports/meetings/summary?id=1110&timezone=Asia/Calcutta&baseCurrencyId=413")
            .retrieve()
            .bodyToMono(String.class);

    //then
    var response = getResourceAsString("classpath:contract/it/meeting-goal-report-summary-response.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(response, jsonResponse, JSONCompareMode.LENIENT);
          } catch (JSONException e) {
            Assertions.fail(e.getMessage());
          }
        })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/lead-with-goal-and-report.sql")
  public void shouldGetReportSummaryForLeadGoalReport() throws IOException {
    //given
    stubFor(
        get("/v1/leads/layout/list")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"pageConfig\":{\"tableConfig\":{\"columns\":[{\"id\":\"ownerId\",\"header\":\"Owner\"},{\"id\":\"createdAt\",\"header\":\"Created At Updated\"}]}}}\n")));

    stubFor(
        get("/v1/entities/label")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"LEAD\": {\"displayName\": \"Lead\", \"displayNamePlural\": \"leads\"}}")));

    //when
    var port = environment.getProperty("local.server.port");
    WebClient webClient = WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(
            HttpHeaders.AUTHORIZATION,
            "Bearer eyJhbGciOiJIUzI1NiJ9.eyJkYXRhIjp7ImFjY2Vzc1Rva2VuIjoiMWM3N2MzMmYtNWY1Yi00MzQyLTkxYjItOTYxZTM4MDQ1NDk4IiwiZXhwaXJlc0luIjozNjAwLCJleHBpcnkiOjE2ODQxNTM2MDMsInVzZXJJZCI6MTAwMDEsInRlbmFudElkIjoxMDAwMCwidG9rZW5UeXBlIjoiQmVhcmVyIiwicGVybWlzc2lvbnMiOlt7Im5hbWUiOiJnb2FsIiwiZGVzY3JpcHRpb24iOiJoYXMgYWNjZXNzIHRvIGxlYWQgcmVzb3VyY2UiLCJsaW1pdHMiOi0xLCJ1bml0cyI6ImNvdW50IiwiYWN0aW9uIjp7InJlYWQiOnRydWUsInJlYWRBbGwiOnRydWV9fSx7Im5hbWUiOiJyZXBvcnQiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6ImRlYWwiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6Im1lZXRpbmciLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbWVldGluZyByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6ImxlYWQiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19XX19.agXtuY69XVt8ssZxRosouh9_EUyDDaJVIb4Z6u0I1xs")
        .build();

    var responseMono =
        webClient
            .get()
            .uri("/v3/reports/leads/summary?id=1110&timezone=Asia/Calcutta&baseCurrencyId=413")
            .retrieve()
            .bodyToMono(String.class);

    //then
    var response = getResourceAsString("classpath:contract/it/lead-goal-report-summary-response.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(response, jsonResponse, JSONCompareMode.LENIENT);
          } catch (JSONException e) {
            Assertions.fail(e.getMessage());
          }
        })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/call-with-goal-and-report.sql")
  public void shouldGetReportSummaryForCallGoalReport() throws IOException {
    //given
    stubFor(
        get("/v1/call-logs/layout/list")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"pageConfig\":{\"tableConfig\":{\"columns\":[{\"id\":\"owner\",\"header\":\"Logged By\"},{\"id\":\"createdAt\",\"header\":\"Created At Updated\"}]}}}\n")));

    stubFor(
        get("/v1/entities/label")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody("{\"CALL\": {\"displayName\": \"Call\", \"displayNamePlural\": \"calls\"}}")));

    //when
    var port = environment.getProperty("local.server.port");
    WebClient webClient = WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(
            HttpHeaders.AUTHORIZATION,
            "Bearer eyJhbGciOiJIUzI1NiJ9.eyJkYXRhIjp7ImFjY2Vzc1Rva2VuIjoiMWM3N2MzMmYtNWY1Yi00MzQyLTkxYjItOTYxZTM4MDQ1NDk4IiwiZXhwaXJlc0luIjozNjAwLCJleHBpcnkiOjE2ODQxNTM2MDMsInVzZXJJZCI6MTAwMDEsInRlbmFudElkIjoxMDAwMCwidG9rZW5UeXBlIjoiQmVhcmVyIiwicGVybWlzc2lvbnMiOlt7Im5hbWUiOiJnb2FsIiwiZGVzY3JpcHRpb24iOiJoYXMgYWNjZXNzIHRvIGxlYWQgcmVzb3VyY2UiLCJsaW1pdHMiOi0xLCJ1bml0cyI6ImNvdW50IiwiYWN0aW9uIjp7InJlYWQiOnRydWUsInJlYWRBbGwiOnRydWV9fSx7Im5hbWUiOiJyZXBvcnQiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6ImRlYWwiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6Im1lZXRpbmciLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbWVldGluZyByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZX19LHsibmFtZSI6ImxlYWQiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZSwicmVhZEFsbCI6dHJ1ZSwiY2FsbCI6dHJ1ZX19LHsibmFtZSI6ImNhbGwiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gY2FsbCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZSwicmVhZEFsbCI6dHJ1ZX19XX19.WTIoVN22GvQ7_w8ujlk_fZw_fv9kNEPWWS9KvdCJLDc")
        .build();

    var responseMono =
        webClient
            .get()
            .uri("/v3/reports/calls/summary?id=1110&timezone=Asia/Calcutta&baseCurrencyId=413")
            .retrieve()
            .bodyToMono(String.class);

    //then
    var response = getResourceAsString("classpath:contract/it/call-goal-report-summary-response.json");
    StepVerifier.create(responseMono)
        .assertNext(jsonResponse -> {
          try {
            JSONAssert.assertEquals(response, jsonResponse, JSONCompareMode.LENIENT);
          } catch (JSONException e) {
            Assertions.fail(e.getMessage());
          }
        })
        .verifyComplete();
  }

  private WebClient buildWebClient() {
    var port = environment.getProperty("local.server.port");

    return WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(
            HttpHeaders.AUTHORIZATION,
            "Bearer "
                + accessToken)
        .build();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = resourceLoader.getResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }
}
