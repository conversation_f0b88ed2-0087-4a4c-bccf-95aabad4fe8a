CREATE TABLE IF NOT EXISTS task_related_to
(
    id BIGINT GE<PERSON>RATED ALWAYS AS IDENTITY NOT NULL,
    task_id BIGINT,
    entity_id BIGINT NOT NULL,
    entity_type VARCHAR(255) NOT NULL,
    entity_name VARCHAR(512) NOT NULL,
    owner_id BIGINT NOT NULL,
    tenant_id BIGINT NOT NULL,

    PRIMARY KEY (id),
    FOREI<PERSON><PERSON> KEY (task_id) REFERENCES task(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS taskrelatedto_taskid_idx ON public.task_related_to USING btree (task_id);
CREATE INDEX IF NOT EXISTS taskrelatedto_entitytype_entityid_idx ON public.task_related_to USING btree (entity_type, entity_id);