package com.sling.sales.report.mq;

import static org.springframework.amqp.core.BindingBuilder.bind;

import com.sling.sales.report.core.domain.retry.ErrorMessageRecoveryFacade;
import com.sling.sales.report.mq.event.CallDeletedEvent;
import com.sling.sales.report.mq.event.CallEvent;
import com.sling.sales.report.mq.event.CompanyDeletedEvent;
import com.sling.sales.report.mq.event.CompanyEvent;
import com.sling.sales.report.mq.event.CompanyNameUpdatedEvent;
import com.sling.sales.report.mq.event.CompanyOwnerUpdateEventV2;
import com.sling.sales.report.mq.event.ContactDeletedEvent;
import com.sling.sales.report.mq.event.ContactEvent;
import com.sling.sales.report.mq.event.ContactEventPayload;
import com.sling.sales.report.mq.event.ContactNameUpdatedEvent;
import com.sling.sales.report.mq.event.ContactOwnerUpdateEvent;
import com.sling.sales.report.mq.event.DealDeletedEvent;
import com.sling.sales.report.mq.event.DealEvent;
import com.sling.sales.report.mq.event.DealNameUpdatedEvent;
import com.sling.sales.report.mq.event.DealOwnerUpdatedEvent;
import com.sling.sales.report.mq.event.EmailEventV2;
import com.sling.sales.report.mq.event.EmailLookUpUpdatedEvent;
import com.sling.sales.report.mq.event.ExchangeRateHistoryAddedEvent;
import com.sling.sales.report.mq.event.FieldEvent;
import com.sling.sales.report.mq.event.LeadEvent;
import com.sling.sales.report.mq.event.LeadEventPayload;
import com.sling.sales.report.mq.event.LeadNameUpdatedEvent;
import com.sling.sales.report.mq.event.LeadOwnerUpdateEvent;
import com.sling.sales.report.mq.event.MeetingEntityDisassociatedEvent;
import com.sling.sales.report.mq.event.MeetingEventV2;
import com.sling.sales.report.mq.event.PicklistValueUpdatedEvent;
import com.sling.sales.report.mq.event.PipelineUpdatedEvent;
import com.sling.sales.report.mq.event.ProductNameUpdatedEvent;
import com.sling.sales.report.mq.event.ShareRuleDeletedEvent;
import com.sling.sales.report.mq.event.ShareRuleEvent;
import com.sling.sales.report.mq.event.ShareRuleEventV2;
import com.sling.sales.report.mq.event.TaskDeletedEvent;
import com.sling.sales.report.mq.event.TaskEvent;
import com.sling.sales.report.mq.event.TeamCreatedEventV2;
import com.sling.sales.report.mq.event.TeamUpdatedEvent;
import com.sling.sales.report.mq.event.TenantCreatedEvent;
import com.sling.sales.report.mq.event.TenantCreatedEventFromConfig;
import com.sling.sales.report.mq.event.TenantCurrencyAddedEvent;
import com.sling.sales.report.mq.event.UsageLimitChangedEvent;
import com.sling.sales.report.mq.event.UserEmailUpdatedEvent;
import com.sling.sales.report.mq.event.UserNameUpdatedEvent;
import com.sling.sales.report.security.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.aop.Advice;
import org.springframework.amqp.core.Declarables;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.config.RetryInterceptorBuilder;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.interceptor.RetryOperationsInterceptor;

@Configuration
@Slf4j
public class RabbitMqConfig {

  static final String IAM_EXCHANGE = "ex.iam";
  static final String CONFIG_EXCHANGE = "ex.config";
  static final String SALES_EXCHANGE = "ex.sales";
  static final String PRODUCT_EXCHANGE = "ex.product";
  static final String DEAL_EXCHANGE = "ex.deal";
  static final String CALL_EXCHANGE = "ex.call";
  static final String PRODUCTIVITY_EXCHANGE = "ex.productivity";
  static final String COMPANY_EXCHANGE = "ex.company";
  static final String MEETING_EXCHANGE = "ex.meeting";
  static final String SCHEDULER_EXCHANGE = "ex.scheduler";
  static final String FOREX_EXCHANGE = "ex.forex";
  static final String EMAIL_EXCHANGE = "ex.email";
  static final String LEAD_REPORT_QUEUE_NAME = "q.sales.lead.report";
  static final String CALL_REPORT_QUEUE_NAME = "q.call.report";
  static final String TASK_REPORT_QUEUE_NAME = "q.task.report";
  static final String TEAM_EVENTS_QUEUE_NAME = "q.team.report";
  static final String MEETING_SHARE_RULE_CREATED_QUEUE_NAME = "q.meeting.sharerule.created.report";
  static final String MEETING_SHARE_RULE_UPDATED_QUEUE_NAME = "q.meeting.sharerule.updated.report";
  static final String MEETING_SHARE_RULE_DELETED_QUEUE_NAME = "q.meeting.sharerule.deleted.report";
  static final String SHARE_RULE_CREATED_QUEUE_NAME = "q.sharerule.created.report";
  static final String SHARE_RULE_UPDATED_QUEUE_NAME = "q.sharerule.updated.report";
  static final String SHARE_RULE_DELETED_QUEUE = "q.sharerule.deleted.report";
  static final String USER_EVENTS_QUEUE = "q.user.updated.report";
  static final String PRODUCT_EVENTS_QUEUE = "q.product.updated.report";
  static final String PIPELINE_EVENTS_QUEUE = "q.pipeline.updated.report";
  static final String COMPANY_EVENTS_QUEUE = "q.company.updated.report";
  static final String TENANT_CREATE_QUEUE = "q.tenant.created.report";
  static final String TASK_ENTITY_CREATED_QUEUE = "q.config.task.entity.created.report";
  static final String DEAL_REPORT_QUEUE = "q.deal.report";
  static final String LEAD_FIELD_CREATED_QUEUE = "q.config.lead.field.created.report";
  static final String LEAD_FIELD_UPDATED_QUEUE = "q.config.lead.field.updated.report";
  static final String LEAD_NAME_UPDATED_QUEUE = "q.sales.lead.name.updated.report";
  static final String CONTACT_NAME_UPDATED_QUEUE = "q.sales.contact.name.updated.report";
  static final String DEAL_FIELD_CREATED_QUEUE = "q.deal.field.created.report";
  static final String DEAL_FIELD_UPDATED_QUEUE = "q.deal.field.updated.report";
  static final String COMPANY_REPORT_QUEUE = "q.company.report";
  static final String COMPANY_FIELD_CREATED_QUEUE = "q.company.field.created.report";
  static final String COMPANY_FIELD_UPDATED_QUEUE = "q.company.field.updated.report";
  static final String MEETING_V2_REPORT_QUEUE = "q.meeting.v2.report";
  static final String MEETING_FIELD_CREATED_QUEUE = "q.meeting.field.created.report";
  static final String MEETING_FIELD_UPDATED_QUEUE = "q.meeting.field.updated.report";
  static final String CONFIG_LEAD_PICK_LIST_VALUE_UPDATED_QUEUE = "q.config.lead.picklist.value.updated.report";
  static final String DEAL_PICK_LIST_VALUE_UPDATED_QUEUE = "q.deal.picklist.value.updated.report";
  static final String CONTACT_REPORT_QUEUE = "q.sales.contact.report";
  static final String CALL_FIELD_CREATED_QUEUE = "q.call.field.created.report";
  static final String CALL_FIELD_UPDATED_QUEUE = "q.call.field.updated.report";
  static final String COMPANY_PICK_LIST_VALUE_UPDATED_QUEUE = "q.company.picklist.value.updated.report";
  static final String MEETING_PICK_LIST_VALUE_UPDATED_QUEUE = "q.meeting.picklist.value.updated.report";
  static final String CALL_PICK_LIST_VALUE_UPDATED_QUEUE = "q.call.picklist.value.updated.report";
  static final String CONTACT_FIELD_CREATED_QUEUE = "q.config.contact.field.created.report";
  static final String EMAIL_LOOK_UP_UPDATED_QUEUE = "q.email.lookup.updated.report";
  static final String CONTACT_FIELD_UPDATED_QUEUE = "q.config.contact.field.updated.report";
  static final String CONTACT_PICK_LIST_VALUE_UPDATED_QUEUE = "q.contact.picklist.value.updated.report";
  static final String DEAL_NAME_UPDATED_QUEUE = "q.deal.name.updated.report";
  public static final String USAGE_LIMIT_CHANGED_QUEUE = "q.usage.limit.changed.report";
  static final String TASK_FIELD_CREATED_QUEUE = "q.task.field.created.report";
  static final String TASK_FIELD_UPDATED_QUEUE = "q.task.field.updated.report";
  static final String TASK_PICK_LIST_VALUE_UPDATED_QUEUE = "q.config.task.picklist.value.updated.report";
  static final String TENANT_CURRENCY_ADDED_QUEUE = "q.iam.tenant.currency.added.report";
  static final String ENTITY_ERROR_RETRY_QUEUE = "q.scheduler.every15Minute.report";
  static final String EVENT_EVERY_15_MINUTE = "scheduler.every15Minute";
  static final String EVERYDAY_AT_1AM_QUEUE = "q.scheduler.delete.records.report";
  static final String EVENT_AT_1AM = "scheduler.1am";
  static final String TEAM_CREATED_EVENT_V2_QUEUE = "q.team.created.v2.report";

  static final String USER_CREATED_EVENT_QUEUE = "q.iam.user.created.report";
  static final String LEAD_REASSIGN_EVENT_QUEUE = "q.sales.lead.owner_updated.report";
  static final String CONTACT_REASSIGN_EVENT_QUEUE = "q.sales.contact.owner_updated.report";
  static final String DEAL_REASSIGN_EVENT_QUEUE = "q.deal.reassigned.report";
  static final String COMPANY_REASSIGN_V2_EVENT_QUEUE = "q.company.reassigned.report";
  public static final String EXCHANGE_RATE_HISTORY_ADDED_QUEUE = "q.forex.history.added.report";
  static final String USER_EMAIL_UPDATED_EVENT_QUEUE = "q.user.email.updated.report";
  static final String LEAD_UPDATED_V2_EVENT_QUEUE = "q.sales.lead.updated.v2.report";
  static final String CONTACT_UPDATED_V2_EVENT_QUEUE = "q.sales.contact.updated.v2.report";
  static final String MEETING_ENTITY_DISASSOCIATED_EVENT_QUEUE = "q.meeting.entity.disassociated.report";
  static final String EMAIL_V2_REPORT_QUEUE = "q.email.v2.report";

  private final long initialInterval;
  private final double multiplier;
  private final long maxInterval;
  private final int maxAttempt;

  private final ErrorMessageRecoveryFacade errorMessageRecoveryFacade;
  @Autowired
  public RabbitMqConfig(
      @Value("${spring.rabbitmq.retry.initial-interval}") long initialInterval,
      @Value("${spring.rabbitmq.retry.multiplier}") double multiplier,
      @Value("${spring.rabbitmq.retry.max-interval}") long maxInterval,
      @Value("${spring.rabbitmq.retry.max-attempt}") int maxAttempt, ErrorMessageRecoveryFacade errorMessageRecoveryFacade) {
    this.initialInterval = initialInterval;
    this.multiplier = multiplier;
    this.maxInterval = maxInterval;
    this.maxAttempt = maxAttempt;
    this.errorMessageRecoveryFacade = errorMessageRecoveryFacade;
  }

  @Bean
  public RabbitTemplate rabbitTemplate(final ConnectionFactory connectionFactory) {
    final var rabbitTemplate = new RabbitTemplate(connectionFactory);
    rabbitTemplate.setMessageConverter(producerJackson2MessageConverter());
    return rabbitTemplate;
  }

  @Bean
  public RetryOperationsInterceptor retryInterceptor() {
    return RetryInterceptorBuilder.stateless()
        .backOffOptions(initialInterval, multiplier, maxInterval)
        .maxAttempts(maxAttempt)
        .recoverer(errorMessageRecoveryFacade)
        .build();
  }

  @Bean
  public SimpleRabbitListenerContainerFactory retryContainerFactory(
      final ConnectionFactory connectionFactory, final RetryOperationsInterceptor retryInterceptor) {
    SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
    factory.setConnectionFactory(connectionFactory);
    factory.setAdviceChain(new Advice[]{retryInterceptor});
    factory.setPrefetchCount(1);
    return factory;
  }

  @Bean
  public Jackson2JsonMessageConverter producerJackson2MessageConverter() {
    return new Jackson2JsonMessageConverter();
  }

  @Bean
  public Declarables topicBindings() {
    var taskEntityCreatedQ = new Queue(TASK_ENTITY_CREATED_QUEUE, true);
    var leadSalesQ = new Queue(LEAD_REPORT_QUEUE_NAME, true);
    var callReportQ = new Queue(CALL_REPORT_QUEUE_NAME, true);
    var taskReportQ = new Queue(TASK_REPORT_QUEUE_NAME, true);
    var shareRuleCreatedQ = new Queue(SHARE_RULE_CREATED_QUEUE_NAME, true);
    var shareRuleUpdatedQ = new Queue(SHARE_RULE_UPDATED_QUEUE_NAME, true);
    var shareRuleDeletedQ = new Queue(SHARE_RULE_DELETED_QUEUE, true);
    var meetingShareRuleCreatedQ = new Queue(MEETING_SHARE_RULE_CREATED_QUEUE_NAME, true);
    var meetingShareRuleUpdatedQ = new Queue(MEETING_SHARE_RULE_UPDATED_QUEUE_NAME, true);
    var meetingShareRuleDeletedQ = new Queue(MEETING_SHARE_RULE_DELETED_QUEUE_NAME, true);
    var iamQueue = new Queue(USER_EVENTS_QUEUE, true);
    var productQueue = new Queue(PRODUCT_EVENTS_QUEUE, true);
    var pipelineQueue = new Queue(PIPELINE_EVENTS_QUEUE, true);
    var teamUpdatedQ = new Queue(TEAM_EVENTS_QUEUE_NAME, true);
    var tenantCreatedQ = new Queue(TENANT_CREATE_QUEUE, true);
    var dealQ = new Queue(DEAL_REPORT_QUEUE, true);
    var companyNameUpdatedQ = new Queue(COMPANY_EVENTS_QUEUE, true);
    var leadFieldCreatedQ = new Queue(LEAD_FIELD_CREATED_QUEUE, true);
    var leadFieldUpdatedQ = new Queue(LEAD_FIELD_UPDATED_QUEUE, true);
    var leadNameChangedQ = new Queue(LEAD_NAME_UPDATED_QUEUE, true);
    var contactNameChangedQ = new Queue(CONTACT_NAME_UPDATED_QUEUE, true);
    var dealFieldCreatedQ = new Queue(DEAL_FIELD_CREATED_QUEUE, true);
    var dealFieldUpdatedQ = new Queue(DEAL_FIELD_UPDATED_QUEUE, true);
    var companyReportQ = new Queue(COMPANY_REPORT_QUEUE, true);
    var companyFieldCreatedQ = new Queue(COMPANY_FIELD_CREATED_QUEUE, true);
    var companyFieldUpdatedQ = new Queue(COMPANY_FIELD_UPDATED_QUEUE, true);
    var meetingReportV2Q = new Queue(MEETING_V2_REPORT_QUEUE, true);
    var meetingFieldCreatedQ = new Queue(MEETING_FIELD_CREATED_QUEUE, true);
    var meetingFieldUpdatedQ = new Queue(MEETING_FIELD_UPDATED_QUEUE, true);
    var configLeadPicklistValueUpdatedQ = new Queue(CONFIG_LEAD_PICK_LIST_VALUE_UPDATED_QUEUE, true);
    var dealPicklistValueUpdatedQ = new Queue(DEAL_PICK_LIST_VALUE_UPDATED_QUEUE, true);
    var contactQ = new Queue(CONTACT_REPORT_QUEUE, true);
    var callLogFieldCreatedQ = new Queue(CALL_FIELD_CREATED_QUEUE, true);
    var callLogFieldUpdatedQ = new Queue(CALL_FIELD_UPDATED_QUEUE, true);
    var companyPicklistValueUpdateQ = new Queue(COMPANY_PICK_LIST_VALUE_UPDATED_QUEUE, true);
    var meetingPicklistValueUpdatedQ = new Queue(MEETING_PICK_LIST_VALUE_UPDATED_QUEUE, true);
    var callPicklistValueUpdatedQ = new Queue(CALL_PICK_LIST_VALUE_UPDATED_QUEUE, true);
    var contactFieldCreatedQ = new Queue(CONTACT_FIELD_CREATED_QUEUE, true);
    var contactFieldUpdatedQ = new Queue(CONTACT_FIELD_UPDATED_QUEUE, true);
    var contactPicklistValueUpdatedQ = new Queue(CONTACT_PICK_LIST_VALUE_UPDATED_QUEUE, true);
    var dealNameUpdatedQ = new Queue(DEAL_NAME_UPDATED_QUEUE, true);
    var usageLimitChangedQ = new Queue(USAGE_LIMIT_CHANGED_QUEUE, true);
    var taskFieldCreatedQ = new Queue(TASK_FIELD_CREATED_QUEUE, true);
    var taskFieldUpdatedQ = new Queue(TASK_FIELD_UPDATED_QUEUE, true);
    var taskPicklistValueUpdatedQ = new Queue(TASK_PICK_LIST_VALUE_UPDATED_QUEUE, true);
    var tenantCurrencyAddedQ = new Queue(TENANT_CURRENCY_ADDED_QUEUE, true);
    var entityErrorRetryQueue = new Queue(ENTITY_ERROR_RETRY_QUEUE, true);
    var exchangeRateHistoryAddedQueue = new Queue(EXCHANGE_RATE_HISTORY_ADDED_QUEUE, true);
    var every30DaysQueue = new Queue(EVERYDAY_AT_1AM_QUEUE, true);
    var teamCreatedEventV2Q = new Queue(TEAM_CREATED_EVENT_V2_QUEUE, true);
    var userCreatedEventQ = new Queue(USER_CREATED_EVENT_QUEUE, true);
    var leadReassignEventQ = new Queue(LEAD_REASSIGN_EVENT_QUEUE, true);
    var contactReassignEventQ = new Queue(CONTACT_REASSIGN_EVENT_QUEUE, true);
    var dealReassignEventQ = new Queue(DEAL_REASSIGN_EVENT_QUEUE, true);
    var companyReassignV2EventQ = new Queue(COMPANY_REASSIGN_V2_EVENT_QUEUE, true);
    var userEmailUpdatedEventQ = new Queue(USER_EMAIL_UPDATED_EVENT_QUEUE, true);
    var leadUpdatedV2EventQ = new Queue(LEAD_UPDATED_V2_EVENT_QUEUE, true);
    var contactUpdatedV2EventQ = new Queue(CONTACT_UPDATED_V2_EVENT_QUEUE, true);
    var meetingEntityDisassociatedEventQ = new Queue(MEETING_ENTITY_DISASSOCIATED_EVENT_QUEUE, true);
    var emailReportV2Q = new Queue(EMAIL_V2_REPORT_QUEUE, true);
    var emailLookupUpdatedQueue = new Queue(EMAIL_LOOK_UP_UPDATED_QUEUE, true);

    var iamExchange = new TopicExchange(IAM_EXCHANGE, true, false);
    var salesExchange = new TopicExchange(SALES_EXCHANGE, true, false);
    var configExchange = new TopicExchange(CONFIG_EXCHANGE, true, false);
    var productExchange = new TopicExchange(PRODUCT_EXCHANGE, true, false);
    var dealExchange = new TopicExchange(DEAL_EXCHANGE, true, false);
    var callExchange = new TopicExchange(CALL_EXCHANGE, true, false);
    var productivityExchange = new TopicExchange(PRODUCTIVITY_EXCHANGE, true, false);
    var companyExchange = new TopicExchange(COMPANY_EXCHANGE, true, false);
    var meetingExchange = new TopicExchange(MEETING_EXCHANGE, true, false);
    var schedulerExchange = new TopicExchange(SCHEDULER_EXCHANGE, true, false);
    var forexExchange = new TopicExchange(FOREX_EXCHANGE, true, false);
    var emailExchange = new TopicExchange(EMAIL_EXCHANGE, true, false);

    return new Declarables(
        taskEntityCreatedQ,
        leadSalesQ,
        callReportQ,
        taskReportQ,
        meetingShareRuleCreatedQ,
        meetingShareRuleUpdatedQ,
        meetingShareRuleDeletedQ,
        shareRuleCreatedQ,
        shareRuleUpdatedQ,
        shareRuleDeletedQ,
        teamUpdatedQ,
        iamQueue,
        productQueue,
        pipelineQueue,
        tenantCreatedQ,
        dealQ,
        companyNameUpdatedQ,
        leadFieldCreatedQ,
        leadFieldUpdatedQ,
        leadNameChangedQ,
        contactNameChangedQ,
        dealFieldCreatedQ,
        dealFieldUpdatedQ,
        companyReportQ,
        companyFieldCreatedQ,
        companyFieldUpdatedQ,
        meetingReportV2Q,
        meetingFieldCreatedQ,
        meetingFieldUpdatedQ,
        configLeadPicklistValueUpdatedQ,
        dealPicklistValueUpdatedQ,
        contactQ,
        callLogFieldCreatedQ,
        callLogFieldUpdatedQ,
        companyPicklistValueUpdateQ,
        meetingPicklistValueUpdatedQ,
        callPicklistValueUpdatedQ,
        contactFieldCreatedQ,
        contactFieldUpdatedQ,
        contactPicklistValueUpdatedQ,
        dealNameUpdatedQ,
        usageLimitChangedQ,
        taskFieldCreatedQ,
        taskFieldUpdatedQ,
        taskPicklistValueUpdatedQ,
        tenantCurrencyAddedQ,
        entityErrorRetryQueue,
        exchangeRateHistoryAddedQueue,
        every30DaysQueue,
        teamCreatedEventV2Q,
        userCreatedEventQ,
        leadReassignEventQ,
        contactReassignEventQ,
        dealReassignEventQ,
        companyReassignV2EventQ,
        userEmailUpdatedEventQ,
        leadUpdatedV2EventQ,
        contactUpdatedV2EventQ,
        meetingEntityDisassociatedEventQ,
        emailReportV2Q,
        emailLookupUpdatedQueue,
        salesExchange,
        configExchange,
        iamExchange,
        dealExchange,
        callExchange,
        productivityExchange,
        productExchange,
        companyExchange,
        meetingExchange,
        schedulerExchange,
        forexExchange,
        emailExchange,
        bind(taskEntityCreatedQ).to(configExchange).with(TenantCreatedEventFromConfig.getEventName()),
        bind(leadSalesQ).to(salesExchange).with(LeadEvent.getCreatedEventName()),
        bind(leadSalesQ).to(salesExchange).with(LeadEvent.getUpdatedEventName()),
        bind(leadSalesQ).to(salesExchange).with(LeadEvent.getDeletedEventName()),
        bind(leadSalesQ).to(salesExchange).with(LeadEvent.getMetaInfoUpdatedEventName()),
        bind(shareRuleCreatedQ).to(configExchange).with(ShareRuleEvent.getCreatedEvent()),
        bind(shareRuleUpdatedQ).to(configExchange).with(ShareRuleEvent.getUpdatedEvent()),
        bind(teamUpdatedQ).to(iamExchange).with(TeamUpdatedEvent.getEventName()),
        bind(shareRuleUpdatedQ).to(configExchange).with(ShareRuleEvent.getUpdatedEvent()),
        bind(meetingShareRuleCreatedQ).to(meetingExchange).with(ShareRuleEventV2.getMeetingCreatedEvent()),
        bind(meetingShareRuleUpdatedQ).to(meetingExchange).with(ShareRuleEventV2.getMeetingUpdatedEvent()),
        bind(meetingShareRuleDeletedQ).to(meetingExchange).with(ShareRuleEventV2.getMeetingDeletedEvent()),
        bind(shareRuleDeletedQ).to(configExchange).with(ShareRuleDeletedEvent.getEventName()),
        bind(iamQueue).to(iamExchange).with(UserNameUpdatedEvent.getEventName()),
        bind(productQueue).to(productExchange).with(ProductNameUpdatedEvent.getEventName()),
        bind(pipelineQueue).to(salesExchange).with(PipelineUpdatedEvent.getEventName()),
        bind(tenantCreatedQ).to(iamExchange).with(TenantCreatedEvent.getEventName()),
        bind(dealQ).to(dealExchange).with(DealEvent.getCreatedEventName()),
        bind(dealQ).to(dealExchange).with(DealEvent.getUpdatedEventName()),
        bind(dealQ).to(dealExchange).with(DealEvent.getMetaInfoUpdatedEventName()),
        bind(dealQ).to(dealExchange).with(DealDeletedEvent.getEventName()),
        bind(callReportQ).to(callExchange).with(CallEvent.getCreatedEventName()),
        bind(callReportQ).to(callExchange).with(CallEvent.getUpdatedEventName()),
        bind(callReportQ).to(callExchange).with(CallDeletedEvent.getEventName()),
        bind(taskReportQ).to(productivityExchange).with(TaskEvent.getCreatedEventName()),
        bind(taskReportQ).to(productivityExchange).with(TaskEvent.getUpdatedEventName()),
        bind(taskReportQ).to(productivityExchange).with(TaskDeletedEvent.getEventName()),
        bind(companyNameUpdatedQ).to(companyExchange).with(CompanyNameUpdatedEvent.getEventName()),
        bind(leadFieldCreatedQ).to(configExchange).with(FieldEvent.getLeadFieldCreatedEventName()),
        bind(leadFieldUpdatedQ).to(configExchange).with(FieldEvent.getLeadFieldUpdatedEventName()),
        bind(leadNameChangedQ).to(salesExchange).with(LeadNameUpdatedEvent.getEventName()),
        bind(contactNameChangedQ).to(salesExchange).with(ContactNameUpdatedEvent.getEventName()),
        bind(dealFieldCreatedQ).to(dealExchange).with(FieldEvent.getDealFieldCreatedEventName()),
        bind(dealFieldUpdatedQ).to(dealExchange).with(FieldEvent.getDealFieldUpdatedEventName()),
        bind(companyReportQ).to(companyExchange).with(CompanyEvent.getCompanyCreatedEventName()),
        bind(companyReportQ).to(companyExchange).with(CompanyEvent.getCompanyUpdatedEventName()),
        bind(companyReportQ).to(companyExchange).with(CompanyDeletedEvent.getEventName()),
        bind(companyFieldCreatedQ).to(companyExchange).with(FieldEvent.getCompanyFieldCreatedEventName()),
        bind(companyFieldUpdatedQ).to(companyExchange).with(FieldEvent.getCompanyFieldUpdatedEventName()),
        bind(meetingReportV2Q).to(meetingExchange).with(MeetingEventV2.getMeetingCreatedEventName()),
        bind(meetingReportV2Q).to(meetingExchange).with(MeetingEventV2.getMeetingUpdatedEventName()),
        bind(meetingReportV2Q).to(meetingExchange).with(MeetingEventV2.getMeetingDeletedEventName()),
        bind(meetingFieldCreatedQ).to(meetingExchange).with(FieldEvent.getMeetingFieldCreatedEventName()),
        bind(meetingFieldUpdatedQ).to(meetingExchange).with(FieldEvent.getMeetingFieldUpdatedEventName()),
        bind(configLeadPicklistValueUpdatedQ).to(configExchange).with(PicklistValueUpdatedEvent.getLeadPicklistValueUpdatedEventName()),
        bind(dealPicklistValueUpdatedQ).to(dealExchange).with(PicklistValueUpdatedEvent.getDealPicklistValueUpdatedEventName()),
        bind(contactQ).to(salesExchange).with(ContactEvent.getContactCreatedEventName()),
        bind(contactQ).to(salesExchange).with(ContactEvent.getContactUpdatedEventName()),
        bind(contactQ).to(salesExchange).with(ContactDeletedEvent.getContactDeletedEventName()),
        bind(callLogFieldCreatedQ).to(callExchange).with(FieldEvent.getCallFieldCreatedEventName()),
        bind(callLogFieldUpdatedQ).to(callExchange).with(FieldEvent.getCallFieldUpdatedEventName()),
        bind(companyPicklistValueUpdateQ).to(companyExchange).with(PicklistValueUpdatedEvent.getCompanyPicklistValueUpdatedEventName()),
        bind(meetingPicklistValueUpdatedQ).to(meetingExchange).with(PicklistValueUpdatedEvent.getMeetingPicklistValueUpdatedEventName()),
        bind(callPicklistValueUpdatedQ).to(callExchange).with(PicklistValueUpdatedEvent.getCallPicklistValueUpdatedEventName()),
        bind(meetingPicklistValueUpdatedQ).to(meetingExchange).with(PicklistValueUpdatedEvent.getMeetingPicklistValueUpdatedEventName()),
        bind(contactFieldCreatedQ).to(configExchange).with(FieldEvent.getContactFieldCreatedEventName()),
        bind(contactFieldUpdatedQ).to(configExchange).with(FieldEvent.getContactFieldUpdatedEventName()),
        bind(callPicklistValueUpdatedQ).to(callExchange).with(PicklistValueUpdatedEvent.getCallPicklistValueUpdatedEventName()),
        bind(contactPicklistValueUpdatedQ).to(configExchange).with(PicklistValueUpdatedEvent.getContactPicklistValueUpdatedEventName()),
        bind(dealNameUpdatedQ).to(dealExchange).with(DealNameUpdatedEvent.getEventName()),
        bind(usageLimitChangedQ).to(iamExchange).with(UsageLimitChangedEvent.getEventName()),
        bind(taskFieldCreatedQ).to(configExchange).with(FieldEvent.getTaskFieldCreatedEventName()),
        bind(taskFieldUpdatedQ).to(configExchange).with(FieldEvent.getTaskFieldUpdatedEventName()),
        bind(taskPicklistValueUpdatedQ).to(configExchange).with(PicklistValueUpdatedEvent.getTaskPicklistValueUpdatedEventName()),
        bind(tenantCurrencyAddedQ).to(iamExchange).with(TenantCurrencyAddedEvent.getEventName()),
        bind(entityErrorRetryQueue).to(schedulerExchange).with(EVENT_EVERY_15_MINUTE),
        bind(exchangeRateHistoryAddedQueue).to(forexExchange).with(ExchangeRateHistoryAddedEvent.EXCHANGE_HISTORY_ADDED_EVENT),
        bind(every30DaysQueue).to(schedulerExchange).with(EVENT_AT_1AM),
        bind(teamCreatedEventV2Q).to(iamExchange).with(TeamCreatedEventV2.getEventName()),
        bind(userCreatedEventQ).to(iamExchange).with(User.getCreatedEventName()),
        bind(leadReassignEventQ).to(salesExchange).with(LeadOwnerUpdateEvent.getEventName()),
        bind(contactReassignEventQ).to(salesExchange).with(ContactOwnerUpdateEvent.getEventName()),
        bind(dealReassignEventQ).to(dealExchange).with(DealOwnerUpdatedEvent.getEventName()),
        bind(companyReassignV2EventQ).to(companyExchange).with(CompanyOwnerUpdateEventV2.getEventName()),
        bind(userEmailUpdatedEventQ).to(iamExchange).with(UserEmailUpdatedEvent.getEventName()),
        bind(leadUpdatedV2EventQ).to(salesExchange).with(LeadEventPayload.getUpdatedEventName()),
        bind(contactUpdatedV2EventQ).to(salesExchange).with(ContactEventPayload.getUpdatedEventName()),
        bind(meetingEntityDisassociatedEventQ).to(meetingExchange).with(MeetingEntityDisassociatedEvent.getEventName()),
        bind(emailReportV2Q).to(emailExchange).with(EmailEventV2.getEmailCreatedEventName()),
        bind(emailReportV2Q).to(emailExchange).with(EmailEventV2.getEmailUpdatedEventName()),
        bind(emailLookupUpdatedQueue).to(emailExchange).with(EmailLookUpUpdatedEvent.getEventName()),
        bind(emailReportV2Q).to(emailExchange).with(EmailEventV2.getEmailDeletedEventName()));
  }
}
