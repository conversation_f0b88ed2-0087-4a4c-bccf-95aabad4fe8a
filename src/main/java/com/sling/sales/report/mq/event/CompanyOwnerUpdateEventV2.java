package com.sling.sales.report.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.security.domain.User;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties
public class CompanyOwnerUpdateEventV2 {
  private final CompanyReassignPayload entity;
  private final CompanyReassignPayload oldEntity;
  private final Metadata metadata;

  @JsonCreator
  public CompanyOwnerUpdateEventV2(
      @JsonProperty("entity") CompanyReassignPayload entity,
      @JsonProperty("oldEntity") CompanyReassignPayload oldEntity,
      @JsonProperty("metadata") Metadata metadata) {
    this.entity = entity;
    this.oldEntity = oldEntity;
    this.metadata = metadata;
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class CompanyReassignPayload {

    private Long id;
    private String name;
    private final IdName updatedBy;
    private User ownedBy;

    @JsonCreator
    public CompanyReassignPayload(@JsonProperty("id") Long id, @JsonProperty("name") String name,
        @JsonProperty("updatedBy") IdName updatedBy,
        @JsonProperty("ownedBy") User ownedBy) {
      this.id = id;
      this.name = name;
      this.updatedBy = updatedBy;
      this.ownedBy = ownedBy;
    }
  }

  public static final String getEventName() {
    return "company.reassigned";
  }
}

