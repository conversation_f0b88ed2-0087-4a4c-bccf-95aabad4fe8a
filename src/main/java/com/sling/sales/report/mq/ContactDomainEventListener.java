package com.sling.sales.report.mq;

import static com.sling.sales.report.mq.RabbitMqConfig.CONTACT_REPORT_QUEUE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.domain.call.CallFacade;
import com.sling.sales.report.core.domain.contact.ContactFacade;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.core.domain.meeting.MeetingInviteeFacade;
import com.sling.sales.report.core.domain.meeting.MeetingRelatedToFacade;
import com.sling.sales.report.core.domain.meeting.OrganizerFacade;
import com.sling.sales.report.core.domain.retry.ErrorMessageRecoveryFacade;
import com.sling.sales.report.core.domain.task.TaskFacade;
import com.sling.sales.report.error.JsonParseException;
import com.sling.sales.report.mq.event.ContactDeletedEvent;
import com.sling.sales.report.mq.event.ContactEvent;
import com.sling.sales.report.mq.event.ContactEventPayload;
import com.sling.sales.report.mq.event.LeadOwnerUpdateEvent;
import java.lang.reflect.Type;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.AmqpRejectAndDontRequeueException;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.web.reactive.function.client.WebClientResponseException;

@Component
@Slf4j
public class ContactDomainEventListener {

  private final ObjectMapper objectMapper;
  private final ContactFacade contactFacade;
  private final OrganizerFacade organizerFacade;
  private final MeetingRelatedToFacade meetingRelatedToFacade;
  private final MeetingInviteeFacade meetingInviteeFacade;
  private final ErrorMessageRecoveryFacade errorMessageRecoveryFacade;
  private final CallFacade callFacade;
  private final TaskFacade taskFacade;

  @Autowired
  public ContactDomainEventListener(ObjectMapper objectMapper, ContactFacade contactFacade,
      ErrorMessageRecoveryFacade errorMessageRecoveryFacade, CallFacade callFacade,
      OrganizerFacade organizerFacade, MeetingRelatedToFacade meetingRelatedToFacade,
      MeetingInviteeFacade meetingInviteeFacade, TaskFacade taskFacade) {
    this.objectMapper = objectMapper;
    this.contactFacade = contactFacade;
    this.errorMessageRecoveryFacade = errorMessageRecoveryFacade;
    this.callFacade = callFacade;
    this.organizerFacade = organizerFacade;
    this.meetingRelatedToFacade = meetingRelatedToFacade;
    this.meetingInviteeFacade = meetingInviteeFacade;
    this.taskFacade = taskFacade;
  }

  @RabbitListener(queues = {CONTACT_REPORT_QUEUE}, containerFactory = "retryContainerFactory", concurrency = "1-1")
  public void contactEventListener(Message message) {
    String messageJson = new String(message.getBody());
    long start = System.currentTimeMillis();
    try {
      HashMap<String, Object> payload =
          objectMapper.readValue(
              messageJson, new TypeReference<>() {
                @Override
                public Type getType() {
                  return super.getType();
                }
              });
      log.debug("event received {} with payload {}", message.getMessageProperties().getReceivedRoutingKey(), messageJson);
      performContactOperation(message.getMessageProperties().getReceivedRoutingKey(), messageJson, payload);

    } catch (Exception e) {
      log.error("Contact event Error : Something went wrong for {} event, with payload {}, error {} ",
          message.getMessageProperties().getReceivedRoutingKey(),
          messageJson,
          e.getMessage(), e);
      errorMessageRecoveryFacade.recover(message, e);
    }
  }

  @RabbitListener(queues = {RabbitMqConfig.CONTACT_REASSIGN_EVENT_QUEUE}, containerFactory = "retryContainerFactory")
  public void contactOwnerUpdatedEventListener(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long contactId = null;
    Long tenantId = null;
    long start = System.currentTimeMillis();
    try {
      var event = objectMapper.readValue(new String(message.getBody()), LeadOwnerUpdateEvent.class);
      contactId = event.getEntityId();
      tenantId = event.getTenantId();
      long updatedOwnerId = event.getNewOwnerId();
      String entity = "contact";
      callFacade.updateCallRelatedToOwnersByEntityIdAndEntity(updatedOwnerId, contactId, entity);
      callFacade.updateCallAssociatedToOwnersByEntityIdAndEntity(updatedOwnerId, contactId, entity);
      organizerFacade.updateOrganizerOwnerByEntityIdAndEntity(updatedOwnerId, contactId, entity);
      meetingRelatedToFacade.updateMeetingRelatedToOwnerByEntityIdAndEntity(updatedOwnerId, contactId, entity);
      meetingInviteeFacade.updateMeetingInviteeOwnerByEntityIdAndEntity(updatedOwnerId, contactId, entity);
      taskFacade.updateTaskRelatedToOwnerIdByEntityIdAndEntity(tenantId, updatedOwnerId, contactId, "CONTACT");
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by ContactOwner process time for event {} for entityId {} for tenantId {}", time,
          message.getMessageProperties().getReceivedRoutingKey(),
          contactId,
          tenantId);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the contact owner updated event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error("Contact owner update failed for contact with id {} while listening to contact owner updated event with error details {}", contactId,
          e);
    }
  }

  @RabbitListener(queues = {RabbitMqConfig.CONTACT_UPDATED_V2_EVENT_QUEUE}, containerFactory = "retryContainerFactory")
  public void listenToContactUpdatedV2Event(Message message) {
    log.info(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long entityId = null;
    Long tenantId = null;
    try {
      var event = objectMapper.readValue(new String(message.getBody()), ContactEventPayload.class);
      entityId = event.getEntity().getId();
      tenantId = event.getEntity().getTenantId();
      meetingInviteeFacade.updateMeetingInviteeEmailIfChanged(entityId, EntityType.CONTACT,
          event.getEntity().getPrimaryEmail(), event.getOldEntity().getPrimaryEmail());
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event contact updated v2 from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error(
          "contact email update failed for contact with entityId: {}, tenantId: {} while listening to contact updated v2 event with error details {}",
          entityId, tenantId, e.getMessage(), e);
    }
  }

  public void retryContactOperation(String routingKey, HashMap<String, Object> payload) throws JsonProcessingException {
    String messageJson = objectMapper.writeValueAsString(payload);
    performContactOperation(routingKey, messageJson, payload);
  }

  private void performContactOperation(String routingKey, String messageJson, HashMap<String, Object> payload) {
    long start = System.currentTimeMillis();
    long contactId = 0;
    Long tenantId = null;
    if (routingKey.equals(ContactEvent.getContactCreatedEventName())) {
      ContactEvent contactEvent = getContactEvent(messageJson);
      contactId = contactEvent.getId();
      tenantId = contactEvent.getTenantId();
      createOrUpdateContact(contactEvent, payload, true);
    }
    if (routingKey.equals(ContactEvent.getContactUpdatedEventName())) {
      ContactEvent contactEvent = getContactEvent(messageJson);
      contactId = contactEvent.getId();
      tenantId = contactEvent.getTenantId();
      createOrUpdateContact(contactEvent, payload, false);
    }
    if (routingKey.equals(ContactDeletedEvent.getContactDeletedEventName())) {
      try {
        ContactDeletedEvent event = objectMapper.readValue(messageJson, ContactDeletedEvent.class);
        contactId = event.getId();
        tenantId = event.getTenantId();
        deleteContact(event);
      } catch (JsonProcessingException e) {
        throw new JsonParseException(e);
      }
    }
    double time = (System.currentTimeMillis() - start) / 1000d;
    log.info("{} seconds took by Contact process time for event {} for entityId {} for tenantId {}", time,
        routingKey,
        contactId,
        tenantId);
  }

  private void deleteContact(ContactDeletedEvent event) {
    contactFacade.delete(event);
    errorMessageRecoveryFacade.deleteAllRetryEntriesForEntity(EntityType.CONTACT, event.getId());
  }

  private void createOrUpdateContact(ContactEvent event, HashMap<String, Object> payload, boolean isCreateEvent) {
    contactFacade.createOrUpdateContact(event, payload, false, isCreateEvent);
  }

  private ContactEvent getContactEvent(String messageJson) {
    try {
      ContactEvent event = objectMapper.readValue(messageJson, ContactEvent.class);
      return event;
    } catch (JsonProcessingException e) {
      throw new JsonParseException(e);
    }
  }
}
