package com.sling.sales.report.mq;

import static com.sling.sales.report.mq.RabbitMqConfig.CALL_REPORT_QUEUE_NAME;
import static com.sling.sales.report.mq.RabbitMqConfig.COMPANY_EVENTS_QUEUE;
import static com.sling.sales.report.mq.RabbitMqConfig.DEAL_FIELD_CREATED_QUEUE;
import static com.sling.sales.report.mq.RabbitMqConfig.DEAL_FIELD_UPDATED_QUEUE;
import static com.sling.sales.report.mq.RabbitMqConfig.DEAL_REPORT_QUEUE;
import static com.sling.sales.report.mq.RabbitMqConfig.LEAD_FIELD_CREATED_QUEUE;
import static com.sling.sales.report.mq.RabbitMqConfig.LEAD_FIELD_UPDATED_QUEUE;
import static com.sling.sales.report.mq.RabbitMqConfig.LEAD_REPORT_QUEUE_NAME;
import static com.sling.sales.report.mq.RabbitMqConfig.MEETING_SHARE_RULE_CREATED_QUEUE_NAME;
import static com.sling.sales.report.mq.RabbitMqConfig.MEETING_SHARE_RULE_DELETED_QUEUE_NAME;
import static com.sling.sales.report.mq.RabbitMqConfig.MEETING_SHARE_RULE_UPDATED_QUEUE_NAME;
import static com.sling.sales.report.mq.RabbitMqConfig.PIPELINE_EVENTS_QUEUE;
import static com.sling.sales.report.mq.RabbitMqConfig.PRODUCT_EVENTS_QUEUE;
import static com.sling.sales.report.mq.RabbitMqConfig.SHARE_RULE_CREATED_QUEUE_NAME;
import static com.sling.sales.report.mq.RabbitMqConfig.SHARE_RULE_DELETED_QUEUE;
import static com.sling.sales.report.mq.RabbitMqConfig.SHARE_RULE_UPDATED_QUEUE_NAME;
import static com.sling.sales.report.mq.RabbitMqConfig.TASK_ENTITY_CREATED_QUEUE;
import static com.sling.sales.report.mq.RabbitMqConfig.TASK_REPORT_QUEUE_NAME;
import static com.sling.sales.report.mq.RabbitMqConfig.TENANT_CREATE_QUEUE;
import static com.sling.sales.report.mq.RabbitMqConfig.USER_EVENTS_QUEUE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.domain.call.CallFacade;
import com.sling.sales.report.core.domain.deal.DealFacade;
import com.sling.sales.report.core.domain.entity.EntityDimensionNameChangeFacade;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.core.domain.field.FieldFacade;
import com.sling.sales.report.core.domain.goal.GoalFacade;
import com.sling.sales.report.core.domain.lead.LeadFacade;
import com.sling.sales.report.core.domain.meeting.MeetingInviteeFacade;
import com.sling.sales.report.core.domain.meeting.MeetingRelatedToFacade;
import com.sling.sales.report.core.domain.meeting.OrganizerFacade;
import com.sling.sales.report.core.domain.report.ReportFacade;
import com.sling.sales.report.core.domain.retry.ErrorMessageRecoveryFacade;
import com.sling.sales.report.core.domain.sharing.ShareRuleFacade;
import com.sling.sales.report.core.domain.sharing.ShareStakeholder;
import com.sling.sales.report.core.domain.sharing.SharedEntity;
import com.sling.sales.report.core.domain.task.TaskFacade;
import com.sling.sales.report.error.JsonParseException;
import com.sling.sales.report.mq.event.CallDeletedEvent;
import com.sling.sales.report.mq.event.CallEvent;
import com.sling.sales.report.mq.event.CompanyNameUpdatedEvent;
import com.sling.sales.report.mq.event.ContactNameUpdatedEvent;
import com.sling.sales.report.mq.event.DealDeletedEvent;
import com.sling.sales.report.mq.event.DealEvent;
import com.sling.sales.report.mq.event.DealNameUpdatedEvent;
import com.sling.sales.report.mq.event.DealOwnerUpdatedEvent;
import com.sling.sales.report.mq.event.FieldEvent;
import com.sling.sales.report.mq.event.LeadDeletedEventPayload;
import com.sling.sales.report.mq.event.LeadEvent;
import com.sling.sales.report.mq.event.LeadEventPayload;
import com.sling.sales.report.mq.event.LeadNameUpdatedEvent;
import com.sling.sales.report.mq.event.LeadOwnerUpdateEvent;
import com.sling.sales.report.mq.event.Money;
import com.sling.sales.report.mq.event.PipelineUpdatedEvent;
import com.sling.sales.report.mq.event.ProductNameUpdatedEvent;
import com.sling.sales.report.mq.event.ShareRuleDeletedEvent;
import com.sling.sales.report.mq.event.ShareRuleEvent;
import com.sling.sales.report.mq.event.ShareRuleEventV2;
import com.sling.sales.report.mq.event.ShareRuleEventV2.Metadata;
import com.sling.sales.report.mq.event.ShareRuleEventV2.ShareRule;
import com.sling.sales.report.mq.event.TaskDeletedEvent;
import com.sling.sales.report.mq.event.TaskEntityCreatedEvent;
import com.sling.sales.report.mq.event.TaskEvent;
import com.sling.sales.report.mq.event.TenantCreatedEvent;
import com.sling.sales.report.mq.event.UserEmailUpdatedEvent;
import com.sling.sales.report.mq.event.UserNameUpdatedEvent;
import com.sling.sales.report.security.domain.Permission;
import com.sling.sales.report.security.domain.TenantFacade;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.UserCacheFacade;
import com.sling.sales.report.security.domain.UserFacade;
import com.sling.sales.report.security.jwt.Authentication;
import com.sling.sales.report.security.jwt.InternalAuthProvider;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;

@Component
@Slf4j
public class DomainEventListener {

  private final ObjectMapper objectMapper;
  private final LeadFacade leadFacade;
  private final CallFacade callFacade;
  private final TaskFacade taskFacade;
  private final DealFacade dealFacade;
  private final ShareRuleFacade shareRuleFacade;
  private final EntityDimensionNameChangeFacade nameChangeFacade;
  private final UserFacade userFacade;
  private final ReportFacade reportFacade;
  private final InternalAuthProvider internalAuthProvider;
  private String jwtSigningKey;
  private final FieldFacade fieldFacade;
  private final GoalFacade goalFacade;
  private final OrganizerFacade organizerFacade;
  private final TenantFacade tenantFacade;
  private final ErrorMessageRecoveryFacade errorMessageRecoveryFacade;
  private final MeetingRelatedToFacade meetingRelatedToFacade;
  private final MeetingInviteeFacade meetingInviteeFacade;

  private final UserCacheFacade userCacheFacade;

  @Autowired
  public DomainEventListener(
      ObjectMapper objectMapper, LeadFacade leadFacade, CallFacade callFacade,
      TaskFacade taskFacade, DealFacade dealFacade, ShareRuleFacade shareRuleFacade,
      EntityDimensionNameChangeFacade nameChangeFacade, UserFacade userFacade,
      ReportFacade reportFacade, InternalAuthProvider internalAuthProvider,
      @Value("${security.jwt.key}") String jwtSigningKey, FieldFacade fieldFacade,
      GoalFacade goalFacade, OrganizerFacade organizerFacade,
      TenantFacade tenantFacade, ErrorMessageRecoveryFacade errorMessageRecoveryFacade,
      MeetingRelatedToFacade meetingRelatedToFacade,
      MeetingInviteeFacade meetingInviteeFacade, UserCacheFacade userCacheFacade) {
    this.objectMapper = objectMapper;
    this.leadFacade = leadFacade;
    this.callFacade = callFacade;
    this.taskFacade = taskFacade;
    this.dealFacade = dealFacade;
    this.shareRuleFacade = shareRuleFacade;
    this.nameChangeFacade = nameChangeFacade;
    this.userFacade = userFacade;
    this.reportFacade = reportFacade;
    this.internalAuthProvider = internalAuthProvider;
    this.jwtSigningKey = jwtSigningKey;
    this.fieldFacade = fieldFacade;
    this.goalFacade = goalFacade;
    this.organizerFacade = organizerFacade;
    this.tenantFacade = tenantFacade;
    this.errorMessageRecoveryFacade = errorMessageRecoveryFacade;
    this.meetingRelatedToFacade = meetingRelatedToFacade;
    this.meetingInviteeFacade = meetingInviteeFacade;
    this.userCacheFacade = userCacheFacade;
  }

  @RabbitListener(queues = {LEAD_REPORT_QUEUE_NAME}, containerFactory = "retryContainerFactory", concurrency = "1-1")
  public void listenToLeadEvent(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    String messageJson = new String(message.getBody());

    try {
      HashMap<String, Object> payload =
          objectMapper.readValue(
              messageJson,
              new TypeReference<>() {
                @Override
                public Type getType() {
                  return super.getType();
                }
              });
      performLeadOperation(message.getMessageProperties().getReceivedRoutingKey(), messageJson, payload);

    } catch (Exception e) {
      log.error("Error listening to the LeadEvent Exception from messageId {} , EventName: {}, eventPayload {}, errorMessage {}",
          message.getMessageProperties().getMessageId(),
          message.getMessageProperties().getReceivedRoutingKey(),
          messageJson, e);
      errorMessageRecoveryFacade.recover(message, e);
    }
  }

  public void retryEntityOperation(EntityType entityType, String routingKey, HashMap<String, Object> payload) throws JsonProcessingException {
    String messageJson = objectMapper.writeValueAsString(payload);
    if (EntityType.LEAD.equals(entityType)) {
      performLeadOperation(routingKey, messageJson, payload);
    }
    if (EntityType.DEAL.equals(entityType)) {
      performDealOperation(routingKey, messageJson, payload);
    }
    if (EntityType.CALL.equals(entityType)) {
      performCallOperation(routingKey, messageJson, payload);
    }
    if (EntityType.TASK.equals(entityType)) {
      performTaskOperation(routingKey, messageJson, payload);
    }
    if (EntityType.SHARE_RULE.equals(entityType)) {
      retryShareRuleOperation(routingKey, messageJson);
    }
  }

  private void performLeadOperation(String routingKey, String messageJson, HashMap<String, Object> payload) {
    long start = System.currentTimeMillis();
    long id = 0;
    long tennatId = 0;
    if (routingKey.equals(LeadEvent.getCreatedEventName())) {
      LeadEvent event = getLeadEvent(routingKey, messageJson);
      log.info("In perform entity operation for {} for entityId {} for tenantId {}",routingKey,event.getId(),event.getTenantId());
      leadFacade.create(event, payload, false, true);
      id = event.getId();
      tennatId = event.getTenantId();
    }
    if (routingKey.equals(LeadEvent.getUpdatedEventName())) {
      LeadEvent event = getLeadEvent(routingKey, messageJson);
      log.info("In perform entity operation for {} for entityId {} for tenantId {}",routingKey,event.getId(),event.getTenantId());
      leadFacade.update(event, payload, false);
      id = event.getId();
      tennatId = event.getTenantId();
    }
    if (routingKey.equals(LeadEvent.getMetaInfoUpdatedEventName())) {
      log.debug("Received lead meta info updated event in report with payload:{}", payload);
      LeadEvent event = getLeadEvent(routingKey, messageJson);
      log.info("In perform entity operation for {} for entityId {} for tenantId {}",routingKey,event.getId(),event.getTenantId());
      leadFacade.updateMetaInfo(event, payload);
      id = event.getId();
      tennatId = event.getTenantId();
    }
    if (routingKey.equals(LeadEvent.getDeletedEventName())) {
      try {
        LeadDeletedEventPayload event = objectMapper.readValue(messageJson, LeadDeletedEventPayload.class);
        log.info("In perform entity operation create {} entityId {} tenantId {}",routingKey,event.getId(),event.getTenantId());
        deleteLead(routingKey, messageJson, event);
        id = event.getId();
        tennatId = event.getTenantId();
      } catch (JsonProcessingException e) {
        throw new JsonParseException(e);
      }
    }
    double time = (System.currentTimeMillis() - start) / 1000d;
    log.info("{} seconds took by Lead process time for event {} for entityId {} for tenantId {}", time,
        routingKey,
        id,
        tennatId);
  }

  private void deleteLead(String routingKey, String messageJson, LeadDeletedEventPayload leadDeletedEventPayload) {
    leadFacade.delete(leadDeletedEventPayload.getId(), leadDeletedEventPayload.getTenantId(), leadDeletedEventPayload.getUserId());
    errorMessageRecoveryFacade.deleteAllRetryEntriesForEntity(EntityType.LEAD, leadDeletedEventPayload.getId());
    log.debug("Lead event for operation {} received for lead id {} and tenant id {}", routingKey,
        leadDeletedEventPayload.getId(),
        leadDeletedEventPayload.getTenantId());
  }

  private LeadEvent getLeadEvent(String routingKey, String messageJson) {
    try {
      LeadEvent event = objectMapper.readValue(messageJson, LeadEvent.class);
      log.debug("Lead event for operation {} received for lead id {} and tenant id {}", routingKey,
          event.getId(),
          event.getTenantId());
      return event;
    } catch (JsonProcessingException e) {
      throw new JsonParseException(e);
    }
  }

  @RabbitListener(queues = {CALL_REPORT_QUEUE_NAME}, containerFactory = "retryContainerFactory", concurrency = "1-1")
  public void listenToCallEvent(Message message) {
    log.debug(String.format(
        "Received message: %s , Event: %s",
        message.getMessageProperties().getMessageId(),
        message.getMessageProperties().getReceivedRoutingKey()));
    var messageJson = new String(message.getBody());
    try {
      HashMap<String, Object> payload = objectMapper.readValue(messageJson, new TypeReference<>() {
        @Override
        public Type getType() {
          return super.getType();
        }
      });
      performCallOperation(message.getMessageProperties().getReceivedRoutingKey(), messageJson, payload);

    } catch (Exception e) {
      log.error("error in listening to call eventName {} and messageId {} with payload {} and error details {}",
          message.getMessageProperties().getReceivedRoutingKey(),
          message.getMessageProperties().getMessageId(),
          messageJson,
          e);
      errorMessageRecoveryFacade.recover(message, e);
    }
  }

  private void performCallOperation(String routingKey, String messageJson, HashMap<String, Object> payload) {
    long start = System.currentTimeMillis();
    long id =0;
    Long tenantId =null;
    if (routingKey.equals(CallEvent.getCreatedEventName())) {
      CallEvent event = getCallEvent(routingKey, messageJson);
      id = event.getId();
      tenantId = event.getTenantId();
      callFacade.create(event, payload, false);
    }
    if (routingKey.equals(CallEvent.getUpdatedEventName())) {
      CallEvent event = getCallEvent(routingKey, messageJson);
      id = event.getId();
      tenantId = event.getTenantId();
      callFacade.update(event, payload, false);
    }
    if (routingKey.equals(CallDeletedEvent.getEventName())) {
      CallDeletedEvent event = null;
      try {
        event = objectMapper.readValue(messageJson, CallDeletedEvent.class);
        id = event.getId();
        tenantId = event.getTenantId();
        deleteCall(routingKey, messageJson,event);
      } catch (JsonProcessingException e) {
        throw new JsonParseException(e);
      }
    }
    long endTime = System.currentTimeMillis();
    double time = (endTime - start) / 1000d;
    log.info("{} seconds took by Call process time for event {} for entityId {} for tenantId {}", time,
        routingKey,
        id,
        tenantId);
  }

  private void deleteCall(String routingKey, String messageJson,CallDeletedEvent event) {
    log.debug("Call {} event id received for tenant id {} and call id {}", routingKey,
        event.getTenantId(), event.getId());
    callFacade.delete(event);
    errorMessageRecoveryFacade.deleteAllRetryEntriesForEntity(EntityType.CALL, event.getId());
  }

  private CallEvent getCallEvent(String routingKey, String messageJson) {
    try {
      CallEvent event = objectMapper.readValue(messageJson, CallEvent.class);
      log.debug("Call {} event id received for tenant id {} and call id {}", routingKey,
          event.getTenantId(), event.getId());
      return event;
    } catch (JsonProcessingException e) {
      throw new JsonParseException(e);
    }
  }

  @RabbitListener(queues = {TASK_REPORT_QUEUE_NAME}, containerFactory = "retryContainerFactory", concurrency = "1-1")
  public void listenToTaskEvent(Message message) {
    log.debug(String.format(
        "Received message: %s , Event: %s",
        message.getMessageProperties().getMessageId(),
        message.getMessageProperties().getReceivedRoutingKey()));
    String messageJson = new String(message.getBody());
    long start = System.currentTimeMillis();
    try {
      HashMap<String, Object> payload =
          objectMapper.readValue(
              messageJson,
              new TypeReference<>() {
                @Override
                public Type getType() {
                  return super.getType();
                }
              });
      performTaskOperation(message.getMessageProperties().getReceivedRoutingKey(), messageJson, payload);
    } catch (Exception e) {
      log.error("error in listening to task event {} with payload {} and error details {}", message.getMessageProperties().getReceivedRoutingKey(),
          messageJson, e);
      errorMessageRecoveryFacade.recover(message, e);
    }
  }

  private void performTaskOperation(String routingKey, String messageJson, HashMap<String, Object> payload) {
    long start = System.currentTimeMillis();
    long id = 0;
    Long tenantId = null;
    if (routingKey.equals(TaskEvent.getCreatedEventName())) {
      TaskEvent taskEvent = getTaskEvent(messageJson);
      id = taskEvent.getEntity().getId();
      tenantId = taskEvent.getEntity().getTenantId();
      createTask(taskEvent, payload);
    }
    if (routingKey.equals(TaskEvent.getUpdatedEventName())) {
      TaskEvent taskEvent = getTaskEvent(messageJson);
      id = taskEvent.getEntity().getId();
      tenantId = taskEvent.getEntity().getTenantId();
      updateTask(taskEvent, payload);
    }
    if (routingKey.equals(TaskDeletedEvent.getEventName())) {
      try {
        var event = objectMapper.readValue(messageJson, TaskDeletedEvent.class);
        id = event.getId();
        tenantId = event.getTenantId();
        deleteTask(event);
      } catch (JsonProcessingException e) {
        throw new JsonParseException(e);
      }
    }
    double time = (System.currentTimeMillis() - start) / 1000d;
    log.info("{} seconds took by Task process time for event {} for entityId {} for tenantId {}",
        time, routingKey,
        id,
        tenantId);
  }

  private void deleteTask(TaskDeletedEvent taskDeletedEvent) {
    taskFacade.delete(taskDeletedEvent);
    errorMessageRecoveryFacade.deleteAllRetryEntriesForEntity(EntityType.TASK, taskDeletedEvent.getId());
  }

  private void updateTask(TaskEvent taskEvent, HashMap<String, Object> payload) {
      taskFacade.update(taskEvent, payload, false);

  }

  private TaskEvent getTaskEvent(String messageJson)  {
      try {
        var event = objectMapper.readValue(messageJson, TaskEvent.class);
        return event;
      } catch (JsonProcessingException e) {
        throw new JsonParseException(e);
      }
  }

  private void createTask(TaskEvent taskEvent, HashMap<String, Object> payload) {
    taskFacade.create(taskEvent, payload, false);
  }

  @RabbitListener(queues = {SHARE_RULE_CREATED_QUEUE_NAME}, containerFactory = "retryContainerFactory")
  public void listenToShareRuleCreatedEvent(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long shareRuleId = null;
    Long tenantId = null;
    long start = System.currentTimeMillis();
    try {
      var event =
          objectMapper.readValue((message.getBody()), ShareRuleEvent.class);
      shareRuleId = event.getId();
      tenantId = event.getTenantId();
      shareRuleFacade.createOrUpdateShareRule(event, true);
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by config ShareRule process time for event {} for entityId {} for tenantId {}",
          time, message.getMessageProperties().getReceivedRoutingKey(),
          shareRuleId,
          tenantId);
    } catch (Exception e) {
      log.error(
          "Share rule creation failed for a share rule with id {} of tenantId {} while listening to share rule created event with error details {}",
          shareRuleId,
          tenantId, e);
      errorMessageRecoveryFacade.recover(message, e);
    }
  }

  @RabbitListener(queues = {SHARE_RULE_UPDATED_QUEUE_NAME}, containerFactory = "retryContainerFactory")
  public void listenToShareRuleUpdatedEvent(Message message) {
    long start = System.currentTimeMillis();
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long shareRuleId = null;
    Long tenantId = null;
    try {
      var event =
          objectMapper.readValue(new String(message.getBody()), ShareRuleEvent.class);
      shareRuleId = event.getId();
      tenantId = event.getTenantId();
      shareRuleFacade.createOrUpdateShareRule(event, false);
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by config ShareRule process time for event {} for entityId {} for tenantId {}",
          time, message.getMessageProperties().getReceivedRoutingKey(),
          shareRuleId,
          tenantId);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()),
          e);
    } catch (Exception e) {
      log.error(
          "Share rule update failed for a share rule with id {} of tenantId {} while listening to share rule updated event with error details {}",
          shareRuleId,
          tenantId, e);
      errorMessageRecoveryFacade.recover(message, e);
    }
  }

  @RabbitListener(queues = {SHARE_RULE_DELETED_QUEUE}, containerFactory = "retryContainerFactory")
  public void listenToShareRuleDeletedEvent(Message message) {
    long start = System.currentTimeMillis();
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long shareRuleId = null;
    Long tenantId = null;
    try {
      var event = objectMapper.readValue(new String(message.getBody()), ShareRuleDeletedEvent.class);
      shareRuleId = event.getId();
      tenantId = event.getTenantId();
      performShareRuleDeleteOperation(event);
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by config ShareRule process time for event {} for entityId {} for tenantId {}",
          time, message.getMessageProperties().getReceivedRoutingKey(),
          shareRuleId,
          tenantId);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()),
          e);
    } catch (Exception e) {
      log.error(
          "Share rule deletion failed for a share rule with id {} of tenantId {} while listening to share rule deleted event with error details {}",
          shareRuleId,
          tenantId, e);
      errorMessageRecoveryFacade.recover(message, e);
    }
  }

  @RabbitListener(queues = {MEETING_SHARE_RULE_CREATED_QUEUE_NAME}, containerFactory = "retryContainerFactory")
  public void listenToMeetingShareRuleCreatedEvent(Message message) {
    long start = System.currentTimeMillis();
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long shareRuleId = null;
    Long tenantId = null;
    try {
      var event =
          objectMapper.readValue((message.getBody()), ShareRuleEventV2.class);
      shareRuleId = event.getEntity().getId();
      tenantId = event.getMetadata().getTenantId();
      ShareRuleEvent shareRuleEvent = fromShareRuleV2ToShareEvent(event.getEntity(), event.getMetadata());
      shareRuleFacade.createOrUpdateShareRule(shareRuleEvent, true);
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by meeting ShareRule process time for event {} for entityId {} for tenantId {}",
          time, message.getMessageProperties().getReceivedRoutingKey(),
          shareRuleId,
          tenantId);
    } catch (Exception e) {
      log.error(
          "Share rule creation failed for a share rule with id {} of tenantId {} while listening to share rule created event with error details {}",
          shareRuleId,
          tenantId, e);
      errorMessageRecoveryFacade.recover(message, e);
    }
  }

  @RabbitListener(queues = {MEETING_SHARE_RULE_UPDATED_QUEUE_NAME}, containerFactory = "retryContainerFactory")
  public void listenToMeetingShareRuleUpdatedEvent(Message message) {
    long start = System.currentTimeMillis();
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long shareRuleId = null;
    Long tenantId = null;
    try {
      var event =
          objectMapper.readValue((message.getBody()), ShareRuleEventV2.class);
      shareRuleId = event.getEntity().getId();
      tenantId = event.getMetadata().getTenantId();
      ShareRuleEvent shareRuleEvent = fromShareRuleV2ToShareEvent(event.getEntity(), event.getMetadata());
      shareRuleFacade.createOrUpdateShareRule(shareRuleEvent, false);
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by meeting ShareRule process time for event {} for entityId {} for tenantId {}",
          time, message.getMessageProperties().getReceivedRoutingKey(),
          shareRuleId,
          tenantId);
    } catch (Exception e) {
      log.error(
          "Share rule update failed for a share rule with id {} of tenantId {} while listening to share rule updated event with error details {}",
          shareRuleId,
          tenantId, e);
      errorMessageRecoveryFacade.recover(message, e);
    }
  }

  @RabbitListener(queues = {MEETING_SHARE_RULE_DELETED_QUEUE_NAME}, containerFactory = "retryContainerFactory")
  public void listenToMeetingShareRuleDeletedEvent(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long shareRuleId = null;
    Long tenantId = null;
    long start = System.currentTimeMillis();
    try {
      var event =
          objectMapper.readValue((message.getBody()), ShareRuleEventV2.class);
      ShareRuleDeletedEvent shareRuleDeletedEvent = fromShareRuleV2ToShareRuleDeletedEvent(event.getOldEntity(), event.getMetadata());
      shareRuleId = shareRuleDeletedEvent.getId();
      tenantId = shareRuleDeletedEvent.getTenantId();
      performShareRuleDeleteOperation(shareRuleDeletedEvent);
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by meeting ShareRule process time for event {} for entityId {} for tenantId {}",
          time, message.getMessageProperties().getReceivedRoutingKey(),
          shareRuleId,
          tenantId);
    } catch (Exception e) {
      log.error(
          "Share rule deletion failed for a share rule with id {} of tenantId {} while listening to share rule deleted event with error details {}",
          shareRuleId,
          tenantId, e);
      errorMessageRecoveryFacade.recover(message, e);
    }
  }

  private ShareRuleEvent fromShareRuleV2ToShareEvent(ShareRule event, Metadata metadata) {

    return new ShareRuleEvent(event.getId(),
        event.getName(),
        event.getDescription(),
        ShareStakeholder.valueOf(event.getFrom().getType().toUpperCase()),
        event.getFrom().getId(),
        ShareStakeholder.valueOf(event.getTo().getType().toUpperCase()),
        event.getTo().getId(),
        event.isShareAllRecords(),
        SharedEntity.valueOf(event.getEntityType().toUpperCase()),
        event.getEntityId(),
        event.getActions(),
        metadata.getTenantId(),
        metadata.getUserId(),
        event.getCreatedAt(),
        event.getUpdatedAt());
  }

  private ShareRuleDeletedEvent fromShareRuleV2ToShareRuleDeletedEvent(ShareRule event, Metadata metadata) {

    return new ShareRuleDeletedEvent(event.getId(),
        event.getEntityId(),
        metadata.getTenantId(),
        metadata.getUserId(),
        SharedEntity.valueOf(event.getEntityType().toUpperCase()));
  }

  private void retryShareRuleOperation(String routingKey, String messageJson) throws JsonProcessingException {
    if (routingKey.equals(ShareRuleEvent.getCreatedEvent())) {
      ShareRuleEvent shareRuleEvent = objectMapper.readValue(messageJson, ShareRuleEvent.class);
      shareRuleFacade.createOrUpdateShareRule(shareRuleEvent, true);
    }
    if (routingKey.equals(ShareRuleEvent.getUpdatedEvent())) {
      ShareRuleEvent shareRuleEvent = objectMapper.readValue(messageJson, ShareRuleEvent.class);
      shareRuleFacade.createOrUpdateShareRule(shareRuleEvent, false);
    }
    if (routingKey.equals(ShareRuleDeletedEvent.getEventName())) {
      ShareRuleDeletedEvent shareRuleDeletedEvent = objectMapper.readValue(messageJson, ShareRuleDeletedEvent.class);
      performShareRuleDeleteOperation(shareRuleDeletedEvent);
    }
  }

  private void performShareRuleDeleteOperation(ShareRuleDeletedEvent shareRuleDeletedEvent) {
    shareRuleFacade.deleteShareRule(shareRuleDeletedEvent);
    errorMessageRecoveryFacade.deleteAllRetryEntriesForEntity(EntityType.SHARE_RULE, shareRuleDeletedEvent.getId());
  }

  @RabbitListener(queues = {USER_EVENTS_QUEUE}, containerFactory = "retryContainerFactory")
  public void listenToUserNameChangedEvent(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long userId = null;
    Long tenantId = null;
    long start = System.currentTimeMillis();
    try {
      var event = objectMapper.readValue(new String(message.getBody()), UserNameUpdatedEvent.class);
      userId = event.getUserId();
      tenantId = event.getTenantId();
      nameChangeFacade.updateUserName(event);
      userCacheFacade.refreshUserCache(event.getTenantId(), event.getUserId());
      goalFacade.updateNameByUserId(event.getUserId(), event.getFirstName(), event.getLastName());
      String name = StringUtils.isBlank(event.getFirstName()) ? event.getLastName() : event.getFirstName() + " " + event.getLastName();
      organizerFacade.updateOrganizerNameByEntityIdAndEntity(event.getUserId(), "user", name);
      meetingInviteeFacade.updateMeetingInviteeNameByEntityIdAndEntity(event.getUserId(), "user", name);
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by User name updated process time for event {} for entityId {} for tenantId {}",
          time, message.getMessageProperties().getReceivedRoutingKey(),
          userId,
          tenantId);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error("User name update failed for a user with id {} of tenantId {} while listening to user name updated event with error details {}",
          userId,
          tenantId, e);
    }
  }

  @RabbitListener(queues = {PRODUCT_EVENTS_QUEUE}, containerFactory = "retryContainerFactory")
  public void listenToProductNameChangedEvent(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long productId = null;
    Long tenantId = null;
    long start = System.currentTimeMillis();
    try {
      var event = objectMapper.readValue(new String(message.getBody()), ProductNameUpdatedEvent.class);
      productId = event.getProductId();
      tenantId = event.getTenantId();
      nameChangeFacade.updateProductName(event);
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by ProductName updated process time for event {} for entityId {} for tenantId {}",
          time, message.getMessageProperties().getReceivedRoutingKey(),
          productId,
          tenantId);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error(
          "Product name update failed for a product with id {} of tenantId {} while listening to product name updated event with error details {}",
          productId,
          tenantId, e);
    }
  }

  @RabbitListener(queues = {PIPELINE_EVENTS_QUEUE}, containerFactory = "retryContainerFactory")
  public void listenToPipelineUpdatedEvent(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long pipelineId = null;
    Long tenantId = null;
    long start= System.currentTimeMillis();
    try {
      var event = objectMapper.readValue(new String(message.getBody()), PipelineUpdatedEvent.class);
      pipelineId = event.getId();
      tenantId = event.getTenantId();
      nameChangeFacade.updatePipeline(event);
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by pipeline updated process time for event {} for entityId {} for tenantId {}",
          time, message.getMessageProperties().getReceivedRoutingKey(),
          pipelineId,
          tenantId);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error(
          "Pipeline update failed for a pipeline with id {} of tenantId {} while listening to pipeline updated event with error details {}",
          pipelineId,
          tenantId, e);
    }
  }

  @RabbitListener(queues = {TENANT_CREATE_QUEUE}, containerFactory = "retryContainerFactory")
  public void listenToTenantCreationEvent(Message message) {
    log.info(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long userId = null;
    Long tenantId = null;
    try {
      var event = objectMapper.readValue(new String(message.getBody()), TenantCreatedEvent.class);
      Set<Permission> permissions = Authentication.from(event.getJwtToken(), jwtSigningKey).getPermissions();
      var user = new User(event.getUserId(), event.getTenantId(), event.getUserName()).withPermissions(permissions);
      userId = event.getUserId();
      tenantId = event.getTenantId();
      tenantFacade.createTenant(event);
      User persistedUser = userFacade.getExistingOrCreateNewUser(user);

      reportFacade.createDefaultReports(persistedUser);

    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error(
          "default reports creation failed for a user with id {} of tenantId {} while listening to tenant creation event with error details {}",
          userId,
          tenantId, e);
    }
  }

  @RabbitListener(queues = {TASK_ENTITY_CREATED_QUEUE}, containerFactory = "retryContainerFactory")
  public void listenToTaskEntityCreatedEvent(Message message) {
    log.info(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long userId = null;
    Long tenantId = null;
    try {
      var event = objectMapper.readValue(new String(message.getBody()), TaskEntityCreatedEvent.class);
      Set<Permission> permissions = Authentication.from(internalAuthProvider.create(event.getUserId(), event.getTenantId()), jwtSigningKey)
          .getPermissions();
      var user = new User(event.getUserId(), event.getTenantId(), event.getUserName()).withPermissions(permissions);
      userId = event.getUserId();
      tenantId = event.getTenantId();
      User persistedUser = userFacade.getExistingOrCreateNewUser(user);
      reportFacade.createDefaultTaskReports(persistedUser);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error(
          "default task reports creation failed for a user with id {} of tenantId {} while listening to task entity created event with error details {}",
          userId,
          tenantId, e);
    }
  }

  @RabbitListener(queues = {DEAL_REPORT_QUEUE}, containerFactory = "retryContainerFactory", concurrency = "1-1")
  public void listenToDealEvent(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    String messageJson = "";
    long start = System.currentTimeMillis();
    try {
      messageJson = new String(message.getBody());
      HashMap<String, Object> payload =
          objectMapper.readValue(
              messageJson,
              new TypeReference<>() {
                @Override
                public Type getType() {
                  return super.getType();
                }
              });
      performDealOperation(message.getMessageProperties().getReceivedRoutingKey(), messageJson, payload);

    } catch (Exception e) {
      log.error(
          "deal operation {} failed with error details {} for payload {}", message.getMessageProperties().getReceivedRoutingKey(),
          e, messageJson);
      errorMessageRecoveryFacade.recover(message, e);
    }
  }

  private void performDealOperation(String routingKey, String messageJson, HashMap<String, Object> payload) {
    long start = System.currentTimeMillis();
    long id = 0;
    Long tenantId= null;
    if (routingKey.equals(DealEvent.getCreatedEventName())) {
      DealEvent event = getDealEvent(routingKey, messageJson);
      log.info("In perform entity operation for {} for entityId {} for tenantId {}",routingKey,event.getId(),event.getTenantId());
      id = event.getId();
      tenantId = event.getTenantId();
      dealFacade.create(event, payload, false, true);
    }
    if (routingKey.equals(DealEvent.getUpdatedEventName())) {
      DealEvent event = getDealEvent(routingKey, messageJson);
      log.info("In perform entity operation for {} for entityId {} for tenantId {}",routingKey,event.getId(),event.getTenantId());
      id = event.getId();
      tenantId = event.getTenantId();
      dealFacade.update(event, payload, false);
    }
    if (routingKey.equals(DealEvent.getMetaInfoUpdatedEventName())) {
      DealEvent event = getDealEvent(routingKey, messageJson);
      log.info("In perform entity operation for {} for entityId {} for tenantId {}",routingKey,event.getId(),event.getTenantId());
      id = event.getId();
      tenantId = event.getTenantId();
      dealFacade.updateMetaInfo(event, payload);
    }
    if (routingKey.equals(DealDeletedEvent.getEventName())) {
      try {
        DealDeletedEvent event = objectMapper.readValue(messageJson, DealDeletedEvent.class);
        log.info("In perform entity operation for {} for entityId {} for tenantId {}",routingKey,event.getId(),event.getTenantId());
        deleteDeal(event, messageJson);
      } catch (JsonProcessingException e) {
        throw new JsonParseException(e);
      }
    }
    double time = (System.currentTimeMillis() - start) / 1000d;
    log.info("{} seconds took by pipeline updated process time for event {} for entityId {} for tenantId {}",
        time,
        routingKey,
        id,
        tenantId);

  }

  private void deleteDeal(DealDeletedEvent event, String messageJson) {
    dealFacade.delete(event);
    errorMessageRecoveryFacade.deleteAllRetryEntriesForEntity(EntityType.DEAL, event.getId());
  }

  private DealEvent getDealEvent(String routingKey, String messageJson) {
    try {
      DealEvent event = objectMapper.readValue(messageJson, DealEvent.class);
      log.debug("deal event for operation {} with tenant id {} and deal id {}", routingKey,
          event.getTenantId(), event.getId());
      event.setEstimatedValue(getEstimatedValue(event));
      event.setActualValue(getActualValue(event));
      return event;
    } catch (JsonProcessingException e) {
      throw new JsonParseException(e);
    }
  }

  private Money getEstimatedValue(DealEvent event) {
    Money estimatedValue = event.getEstimatedValue();
    if (estimatedValue == null) {
      return new Money(null, null, event.getEstimatedValueExchangeDate(), event.getEstimatedValueExchangeRate());
    }
    estimatedValue.setExchangeRate(event.getEstimatedValueExchangeRate());
    estimatedValue.setExchangeDate(event.getEstimatedValueExchangeDate());
    return estimatedValue;
  }

  private Money getActualValue(DealEvent event) {
    Money actualValue = event.getActualValue();
    if (actualValue == null) {
      return new Money(null, null, event.getActualValueExchangeDate(), event.getActualValueExchangeRate());
    }
    actualValue.setExchangeRate(event.getActualValueExchangeRate());
    actualValue.setExchangeDate(event.getActualValueExchangeDate());
    return actualValue;
  }

  @RabbitListener(queues = {COMPANY_EVENTS_QUEUE}, containerFactory = "retryContainerFactory")
  public void listenToCompanyNameUpdatedEvent(Message message) {
    long start = System.currentTimeMillis();
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long companyId = null;
    Long tenantId = null;
    try {
      var event = objectMapper.readValue(new String(message.getBody()), CompanyNameUpdatedEvent.class);
      companyId = event.getCompanyId();
      tenantId = event.getTenantId();
      nameChangeFacade.updateAssociatedCompanyName(event);
      nameChangeFacade.updateContactCompanyName(event);
      taskFacade.updateTaskRelatedToNameByEntityIdAndEntity(tenantId, companyId, "COMPANY", event.getCompanyName());
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by companyName updated process time for event {} for entityId {} for tenantId {}",
          time,
          message.getMessageProperties().getReceivedRoutingKey(),
          companyId,
          tenantId);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error(
          "Company name update failed for a company with id {} of tenantId {} while listening to company name updated event with error details {}",
          companyId,
          tenantId, e);
    }
  }

  @RabbitListener(queues = {LEAD_FIELD_CREATED_QUEUE}, containerFactory = "retryContainerFactory")
  public void listenToLeadFieldCreatedEvent(Message message) {
    log.info(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long tenantId = null;
    try {
      var event = objectMapper.readValue(new String(message.getBody()), FieldEvent.class);
      tenantId = event.getTenantId();
      fieldFacade.createField(event, EntityType.LEAD);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error("Lead fields creation failed for tenant with id {} while listening to lead field created event with error details {}", tenantId, e);
    }
  }

  @RabbitListener(queues = {LEAD_FIELD_UPDATED_QUEUE}, containerFactory = "retryContainerFactory")
  public void listenToLeadFieldUpdatedEvent(Message message) {
    log.info(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long tenantId = null;
    try {
      var event = objectMapper.readValue(new String(message.getBody()), FieldEvent.class);
      tenantId = event.getTenantId();
      fieldFacade.updateField(event, EntityType.LEAD);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error("Lead fields update failed for tenant with id {} while listening to lead field updated event with error details {}", tenantId, e);
    }
  }

  @RabbitListener(queues = DEAL_FIELD_CREATED_QUEUE, containerFactory = "retryContainerFactory")
  public void listenToDealFieldCreatedEvent(Message message) {
    log.info(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long tenantId = null;
    try {
      var event = objectMapper.readValue(new String(message.getBody()), FieldEvent.class);
      tenantId = event.getTenantId();
      fieldFacade.createField(event, EntityType.DEAL);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error("Deal fields creation failed for tenant with id {} while listening to deal field created event with error details {}", tenantId, e);
    }
  }

  @RabbitListener(queues = DEAL_FIELD_UPDATED_QUEUE, containerFactory = "retryContainerFactory")
  public void listenToDealFieldUpdatedEvent(Message message) {
    log.info(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long tenantId = null;
    try {
      var event = objectMapper.readValue(new String(message.getBody()), FieldEvent.class);
      tenantId = event.getTenantId();
      fieldFacade.updateField(event, EntityType.DEAL);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error("Deal fields update failed for tenant with id {} while listening to deal field updated event with error details {}", tenantId, e);
    }
  }

  @RabbitListener(queues = {RabbitMqConfig.LEAD_NAME_UPDATED_QUEUE}, containerFactory = "retryContainerFactory")
  public void listenToLeadNameUpdatedEvent(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    long start = System.currentTimeMillis();
    Long leadId = null;
    Long tenantId = null;
    try {
      var event = objectMapper.readValue(new String(message.getBody()), LeadNameUpdatedEvent.class);
      leadId = event.getLeadId();
      tenantId = event.getTenantId();
      callFacade.updateCallRelatedToEntityNameByEntityIdAndEntity(event.getName(), leadId, "lead");
      callFacade.updateCallAssociatedToEntityNameByEntityIdAndEntity(event.getName(), leadId, "lead");
      organizerFacade.updateOrganizerNameByEntityIdAndEntity(leadId, "lead", event.getName());
      meetingRelatedToFacade.updateMeetingRelatedToNameByEntityIdAndEntity(leadId, "lead", event.getName());
      meetingInviteeFacade.updateMeetingInviteeNameByEntityIdAndEntity(leadId, "lead", event.getName());
      taskFacade.updateTaskRelatedToNameByEntityIdAndEntity(tenantId, leadId, "LEAD", event.getName());
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by leadName updated process time for event {} for entityId {} for tenantId {}",
          time,
          message.getMessageProperties().getReceivedRoutingKey(),
          leadId,
          tenantId);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event lead name updated from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error("Lead name update failed for lead with id {} while listening to lead name updated event with error details {}", leadId,
          e);
    }
  }

  @RabbitListener(queues = {RabbitMqConfig.CONTACT_NAME_UPDATED_QUEUE}, containerFactory = "retryContainerFactory")
  public void listenToContactNameUpdatedEvent(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long contactId = null;
    Long tenantId = null;
    long start = System.currentTimeMillis();
    try {
      var event = objectMapper.readValue(new String(message.getBody()), ContactNameUpdatedEvent.class);
      contactId = event.getContactId();
      tenantId = event.getTenantId();
      nameChangeFacade.updateDealContactName(event);
      organizerFacade.updateOrganizerNameByEntityIdAndEntity(contactId, "contact", event.getName());
      callFacade.updateCallRelatedToEntityNameByEntityIdAndEntity(event.getName(), contactId, "contact");
      callFacade.updateCallAssociatedToEntityNameByEntityIdAndEntity(event.getName(), contactId, "contact");
      meetingRelatedToFacade.updateMeetingRelatedToNameByEntityIdAndEntity(contactId, "contact", event.getName());
      meetingInviteeFacade.updateMeetingInviteeNameByEntityIdAndEntity(contactId, "contact", event.getName());
      taskFacade.updateTaskRelatedToNameByEntityIdAndEntity(tenantId, contactId, "CONTACT", event.getName());
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by contactName updated process time for event {} for entityId {} for tenantId {}",
          time,
          message.getMessageProperties().getReceivedRoutingKey(),
          contactId,
          tenantId);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event contact name updated from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error("Contact name update failed for contact with id {} while listening to contact name updated event with error details {}", contactId,
          e);
    }
  }

  @RabbitListener(queues = {RabbitMqConfig.DEAL_NAME_UPDATED_QUEUE}, containerFactory = "retryContainerFactory")
  public void dealNameUpdatedEventListener(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long dealId = null;
    Long tenantId = null;
    long start = System.currentTimeMillis();
    try {
      var event = objectMapper.readValue(new String(message.getBody()), DealNameUpdatedEvent.class);
      dealId = event.getId();
      tenantId = event.getTenantId();
      nameChangeFacade.updateContactDealName(event);
      callFacade.updateCallRelatedToEntityNameByEntityIdAndEntity(event.getName(), dealId, "deal");
      callFacade.updateCallAssociatedToEntityNameByEntityIdAndEntity(event.getName(), dealId, "deal");
      organizerFacade.updateOrganizerNameByEntityIdAndEntity(dealId, "deal", event.getName());
      meetingRelatedToFacade.updateMeetingRelatedToNameByEntityIdAndEntity(dealId, "deal", event.getName());
      meetingInviteeFacade.updateMeetingInviteeNameByEntityIdAndEntity(dealId, "deal", event.getName());
      taskFacade.updateTaskRelatedToNameByEntityIdAndEntity(tenantId, dealId, "DEAL", event.getName());
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by dealName updated process time for event {} for entityId {} for tenantId {}",
          time,
          message.getMessageProperties().getReceivedRoutingKey(),
          dealId,
          tenantId);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event deal name updated from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error("Deal name update failed for deal with id {} while listening to deal name updated event with error details {}", dealId,
          e);
    }
  }

  @RabbitListener(queues = {RabbitMqConfig.LEAD_REASSIGN_EVENT_QUEUE}, containerFactory = "retryContainerFactory")
  public void leadOwnerUpdatedEventListener(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long leadId = null;
    Long tenantId = null;
    long start =System.currentTimeMillis();
    try {
      var event = objectMapper.readValue(new String(message.getBody()), LeadOwnerUpdateEvent.class);
      leadId = event.getEntityId();
      tenantId = event.getTenantId();
      long updatedOwnerId = event.getNewOwnerId();
      String entity = "lead";
      callFacade.updateCallRelatedToOwnersByEntityIdAndEntity(updatedOwnerId, leadId, entity);
      callFacade.updateCallAssociatedToOwnersByEntityIdAndEntity(updatedOwnerId, leadId, entity);
      organizerFacade.updateOrganizerOwnerByEntityIdAndEntity(updatedOwnerId, leadId, entity);
      meetingRelatedToFacade.updateMeetingRelatedToOwnerByEntityIdAndEntity(updatedOwnerId, leadId, entity);
      meetingInviteeFacade.updateMeetingInviteeOwnerByEntityIdAndEntity(updatedOwnerId, leadId, entity);
      taskFacade.updateTaskRelatedToOwnerIdByEntityIdAndEntity(tenantId, updatedOwnerId, leadId, "LEAD");
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by lead owner updated process time for event {} for entityId {} for tenantId {}",
          time,
          message.getMessageProperties().getReceivedRoutingKey(),
          leadId,
          tenantId);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the lead owner updated event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error("Lead owner update failed for lead with id {} while listening to lead owner updated event with error details {}", leadId,
          e);
    }
  }

  @RabbitListener(queues = {RabbitMqConfig.DEAL_REASSIGN_EVENT_QUEUE}, containerFactory = "retryContainerFactory")
  public void dealOwnerUpdatedEventListener(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long dealId = null;
    Long tenantId = null;
    long start = System.currentTimeMillis();
    try {
      var event = objectMapper.readValue(new String(message.getBody()), DealOwnerUpdatedEvent.class);
      dealId = event.getId();
      tenantId = event.getDeal().getTenantId();
      if (ObjectUtils.isNotEmpty(event.getDeal()) &&
          ObjectUtils.isNotEmpty(event.getDeal().getOwnedBy()) &&
          ObjectUtils.isNotEmpty(event.getDeal().getOwnedBy().getId())) {
        long updatedOwnerId = event.getDeal().getOwnedBy().getId();
        String entity = "deal";
        callFacade.updateCallRelatedToOwnersByEntityIdAndEntity(updatedOwnerId, dealId, entity);
        callFacade.updateCallAssociatedToOwnersByEntityIdAndEntity(updatedOwnerId, dealId, entity);
        organizerFacade.updateOrganizerOwnerByEntityIdAndEntity(updatedOwnerId, dealId, entity);
        meetingRelatedToFacade.updateMeetingRelatedToOwnerByEntityIdAndEntity(updatedOwnerId, dealId, entity);
        meetingInviteeFacade.updateMeetingInviteeOwnerByEntityIdAndEntity(updatedOwnerId, dealId, entity);
        taskFacade.updateTaskRelatedToOwnerIdByEntityIdAndEntity(tenantId, updatedOwnerId, dealId, "DEAL");
        double time = (System.currentTimeMillis() - start) / 1000d;
        log.info("{} seconds took by deal owner updated process time for event {} for entityId {} for tenantId {}",
            time,
            message.getMessageProperties().getReceivedRoutingKey(),
            dealId,
            tenantId);
      }
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the deal owner updated event from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error("Deal owner update failed for deal with id {} while listening to deal owner updated event with error details {}", dealId,
          e);
    }
  }

  @RabbitListener(queues = RabbitMqConfig.USER_CREATED_EVENT_QUEUE, containerFactory = "retryContainerFactory")
  public void userCreatedEventListener(Message message) {
    String eventPayload = new String(message.getBody());
    try {
      log.info("User created event listen with details {}", eventPayload);
      User user = objectMapper.readValue(eventPayload, User.class);
      userFacade.createUser(user);
      log.info("User Id {} created successfully ", user.getId());
    } catch (TransactionException e) {
      log.error("Error listening to the User Created event TransactionException from messageId {} , EventName: {}, eventPayload {}, errorMessage {}",
          message.getMessageProperties().getMessageId(),
          message.getMessageProperties().getReceivedRoutingKey(),
          eventPayload, e);
      throw new AmqpException(e);
    } catch (Exception e) {
      log.error(
          "Error listening to the User Created event Exception will not retry from messageId {} , EventName: {}, eventPayload {}, errorMessage {}",
          message.getMessageProperties().getMessageId(),
          message.getMessageProperties().getReceivedRoutingKey(),
          eventPayload, e);
    }
  }

  @RabbitListener(queues = {RabbitMqConfig.USER_EMAIL_UPDATED_EVENT_QUEUE}, containerFactory = "retryContainerFactory")
  public void userEmailUpdatedEventListener(Message message) {
    log.debug(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long userId = null;
    Long tenantId = null;
    long start = System.currentTimeMillis();
    try {
      var event = objectMapper.readValue(new String(message.getBody()), UserEmailUpdatedEvent.class);
      userId = event.getUserId();
      tenantId = event.getTenantId();
      meetingInviteeFacade.updateMeetingInviteeEmailByEntityIdAndEntity(userId, EntityType.USER, event.getEmail());
      double time = (System.currentTimeMillis() - start) / 1000d;
      log.info("{} seconds took by user email updated process time for event {} for entityId {} for tenantId {}",
          time,
          message.getMessageProperties().getReceivedRoutingKey(),
          userId,
          tenantId);
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event user email updated from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error("User email update failed for user with userId: {}, tenantId: {} while listening to user email updated event with error details {}",
          userId, tenantId, e.getMessage(), e);
    }
  }

  @RabbitListener(queues = {RabbitMqConfig.LEAD_UPDATED_V2_EVENT_QUEUE}, containerFactory = "retryContainerFactory")
  public void listenToLeadUpdatedV2Event(Message message) {
    log.info(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    Long entityId = null;
    Long tenantId = null;
    try {
      var event = objectMapper.readValue(new String(message.getBody()), LeadEventPayload.class);
      entityId = event.getEntity().getId();
      tenantId = event.getEntity().getTenantId();
      meetingInviteeFacade.updateMeetingInviteeEmailIfChanged(entityId, EntityType.LEAD,
          event.getEntity().getPrimaryEmail(), event.getOldEntity().getPrimaryEmail());
    } catch (JsonProcessingException e) {
      log.error(
          String.format(
              "Error listening to the event lead updated v2 from message %s , Event: %s",
              message.getMessageProperties().getMessageId(),
              message.getMessageProperties().getReceivedRoutingKey()));
    } catch (Exception e) {
      log.error("lead email update failed for lead with entityId: {}, tenantId: {} while listening to lead updated v2 event with error details {}",
          entityId, tenantId, e.getMessage(), e);
    }
  }
}
