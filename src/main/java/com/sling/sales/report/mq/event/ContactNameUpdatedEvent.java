package com.sling.sales.report.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContactNameUpdatedEvent {

  private final long contactId;
  private final String firstName;
  private final String lastName;
  private final long tenantId;

  @JsonCreator
  public ContactNameUpdatedEvent(@JsonProperty("contactId") long contactId, @JsonProperty("firstName") String firstName,
      @JsonProperty("lastName") String lastName, long tenantId) {
    this.contactId = contactId;
    this.firstName = firstName;
    this.lastName = lastName;
    this.tenantId = tenantId;
  }

  public static final String getEventName() {
    return "contact.name.updated";
  }

  public String getName() {
    return Stream.of(firstName, lastName)
        .filter(Objects::nonNull)
        .collect(Collectors.joining(" "));
  }
}
