package com.sling.sales.report.mq.event;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskEvent {

  private EventEntity entity;

  public static String getCreatedEventName() {
    return "task.created";
  }

  public static String getUpdatedEventName() {
    return "task.updated";
  }

  @Getter
  @Setter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class EventEntity {

    private Long id;
    private Long tenantId;
    private String name;
    private Date dueDate;
    private IdName assignedTo;
    private IdName owner;
    private IdName createdBy;
    private IdName updatedBy;
    private Date createdAt;
    private Date updatedAt;
    private IdName type;
    private IdName status;
    private IdName priority;
    private Date originalDueDate;
    private Date completedAt;
    @JsonProperty("entityDetails")
    private List<RelatedEntity> relatedTo;
    private Map<String, Object> customFieldValues;
  }

  @Getter
  @Setter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class RelatedEntity {

    private Long entityId;
    private String entityName;
    private String entityType;
    private Long ownerId;
  }
}
