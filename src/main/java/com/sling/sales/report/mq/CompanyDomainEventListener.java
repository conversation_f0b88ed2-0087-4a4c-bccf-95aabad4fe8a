package com.sling.sales.report.mq;

import static com.sling.sales.report.mq.RabbitMqConfig.COMPANY_REASSIGN_V2_EVENT_QUEUE;
import static com.sling.sales.report.mq.RabbitMqConfig.COMPANY_REPORT_QUEUE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.domain.call.CallFacade;
import com.sling.sales.report.core.domain.company.CompanyFacade;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.core.domain.retry.ErrorMessageRecoveryFacade;
import com.sling.sales.report.core.domain.task.TaskFacade;
import com.sling.sales.report.error.JsonParseException;
import com.sling.sales.report.mq.event.CompanyDeletedEvent;
import com.sling.sales.report.mq.event.CompanyEvent;
import com.sling.sales.report.mq.event.CompanyOwnerUpdateEventV2;
import java.lang.reflect.Type;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpRejectAndDontRequeueException;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.web.reactive.function.client.WebClientResponseException;

@Component
@Slf4j
public class CompanyDomainEventListener {

  private final ObjectMapper objectMapper;
  private final CompanyFacade companyFacade;
  private final ErrorMessageRecoveryFacade errorMessageRecoveryFacade;
  private final CallFacade callFacade;
  private final TaskFacade taskFacade;

  @Autowired
  public CompanyDomainEventListener(ObjectMapper objectMapper, CompanyFacade companyFacade,
      ErrorMessageRecoveryFacade errorMessageRecoveryFacade, CallFacade callFacade, TaskFacade taskFacade) {
    this.objectMapper = objectMapper;
    this.companyFacade = companyFacade;
    this.errorMessageRecoveryFacade = errorMessageRecoveryFacade;
    this.callFacade = callFacade;
    this.taskFacade = taskFacade;
  }

  @RabbitListener(queues = {COMPANY_REPORT_QUEUE}, containerFactory = "retryContainerFactory", concurrency = "1-1")
  public void companyEventListener(Message message) {
    String messageJson = new String(message.getBody());
    try {
      HashMap<String, Object> payload =
          objectMapper.readValue(
              messageJson, new TypeReference<>() {
                @Override
                public Type getType() {
                  return super.getType();
                }
              });
      performCompanyOperation(message.getMessageProperties().getReceivedRoutingKey(), messageJson, payload);
    } catch (JsonProcessingException | JsonParseException e) {
      log.error("Company event Error : listening to the {} from, payload {}, error {}",
          message.getMessageProperties().getReceivedRoutingKey(),
          messageJson, e);
    } catch (AmqpRejectAndDontRequeueException | ObjectOptimisticLockingFailureException |TransactionException |WebClientResponseException e) {
      log.error("Retriable exception store into database while listening to company event {} with payload {} error details {}",
          message.getMessageProperties().getReceivedRoutingKey(), messageJson, e);
      errorMessageRecoveryFacade.recover(message,e);
    } catch (Exception e) {
      log.error("Company event Error : Something went wrong for {} event, with payload {}, error {} ",
          message.getMessageProperties().getReceivedRoutingKey(),
          messageJson,
          e);
      errorMessageRecoveryFacade.recover(message,e);
    }
  }

  @RabbitListener(queues = {COMPANY_REASSIGN_V2_EVENT_QUEUE}, containerFactory = "retryContainerFactory")
  public void companyOwnerUpdateEventListener(Message message) {
    String messageJson = new String(message.getBody());
    log.info(
        String.format(
            "Received message: %s , Event: %s",
            message.getMessageProperties().getMessageId(),
            message.getMessageProperties().getReceivedRoutingKey()));
    try {
      var event = objectMapper.readValue(new String(message.getBody()), CompanyOwnerUpdateEventV2.class);
      Long tenantId = event.getMetadata().getTenantId();
      callFacade.updateCallRelatedToOwnersByEntityIdAndEntity(event.getEntity().getOwnedBy().getId(), event.getEntity().getId(), "company");
      callFacade.updateCallAssociatedToOwnersByEntityIdAndEntity(event.getEntity().getOwnedBy().getId(), event.getEntity().getId(), "company");
      taskFacade.updateTaskRelatedToOwnerIdByEntityIdAndEntity(tenantId, event.getEntity().getOwnedBy().getId(), event.getEntity().getId(), "COMPANY");
    } catch (JsonProcessingException e) {
      log.error("Company event v2 Error : listening to the {} from, payload {}, error {}",
          message.getMessageProperties().getReceivedRoutingKey(),
          messageJson, e);
    } catch (Exception e) {
      log.error("Company event Error : Something went wrong for {} event, with payload {}, error {} ",
          message.getMessageProperties().getReceivedRoutingKey(),
          messageJson,
          e);
    }
  }

  public void retryCompanyOperation(String routingKey, HashMap<String, Object> payload) throws JsonProcessingException {
    String messageJson = objectMapper.writeValueAsString(payload);
    performCompanyOperation(routingKey, messageJson, payload);
  }

  private void performCompanyOperation(String routingKey, String messageJson, HashMap<String, Object> payload) {
    long start = System.currentTimeMillis();
    long companyId = 0;
    long tenantId = 0;
    if (routingKey.equals(CompanyEvent.getCompanyCreatedEventName())) {
      CompanyEvent companyEvent = getCompanyEvent(messageJson);
      companyId = companyEvent.getId();
      tenantId = companyEvent.getTenantId();
      createOrUpdateCompany(companyEvent, payload, true);
    }
    if (routingKey.equals(CompanyEvent.getCompanyUpdatedEventName())) {
      CompanyEvent companyEvent = getCompanyEvent(messageJson);
      companyId = companyEvent.getId();
      tenantId = companyEvent.getTenantId();
      createOrUpdateCompany(companyEvent, payload, false);
    }
    if (routingKey.equals(CompanyDeletedEvent.getEventName())) {
      try{
      CompanyDeletedEvent event = objectMapper.readValue(messageJson, CompanyDeletedEvent.class);
      companyId = event.getId();
      tenantId = event.getTenantId();
      deleteCompany(event);
      } catch (JsonProcessingException e) {
        throw new JsonParseException(e);
      }
    }
    double time = (System.currentTimeMillis() - start) / 1000d;
    log.info("{} seconds took by Company process time for event {} for entityId {} for tenantId {}", time,
        routingKey,
        companyId,
        tenantId);
  }

  private void deleteCompany(CompanyDeletedEvent event) {
    companyFacade.delete(event);
    errorMessageRecoveryFacade.deleteAllRetryEntriesForEntity(EntityType.COMPANY, event.getId());

  }

  private void createOrUpdateCompany(CompanyEvent event, HashMap<String, Object> payload, boolean isCreateEvent) {
    companyFacade.createOrUpdateCompany(event, payload, false, isCreateEvent);
  }

  private CompanyEvent getCompanyEvent(String messageJson)  {
    try {
      CompanyEvent event = objectMapper.readValue(messageJson, CompanyEvent.class);
      return event;
    } catch (JsonProcessingException e) {
      throw new JsonParseException(e);
    }
  }

}
