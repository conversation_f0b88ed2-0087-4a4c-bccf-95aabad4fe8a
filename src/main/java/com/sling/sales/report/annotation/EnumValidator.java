package com.sling.sales.report.annotation;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class EnumValidator implements ConstraintValidator<AllowedValue, CharSequence> {

  private List<String> acceptedValues;

  @Override
  public void initialize(AllowedValue annotation) {
    acceptedValues = Stream.of(annotation.enumClass().getEnumConstants())
        .map(Enum::name)
        .collect(Collectors.toList());
  }

  @Override
  public boolean isValid(CharSequence value, ConstraintValidatorContext context) {
    if (value == null) {
      return true;
    }
    return acceptedValues.stream()
        .map(String::toLowerCase)
        .anyMatch(acceptedValue -> acceptedValue.equals(value.toString().toLowerCase()));
  }
}
