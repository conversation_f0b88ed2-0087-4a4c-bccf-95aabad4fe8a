package com.sling.sales.report.config.api;

import com.sling.sales.report.config.api.response.ListLayout;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/v1/reports")
public class LayoutController {

  private final LayoutService layoutService;

  @Autowired
  public LayoutController(LayoutService layoutService) {
    this.layoutService = layoutService;
  }

  @GetMapping(value = "/layout/list", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ListLayout> getListLayout() {
    return ResponseEntity.ok(layoutService.getListLayout());
  }

  @GetMapping(value = "/goals/layout/list", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ListLayout> getGoalListLayout() {
    return ResponseEntity.ok(layoutService.getGoalListLayout());
  }
}
