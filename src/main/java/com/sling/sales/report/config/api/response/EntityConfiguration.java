package com.sling.sales.report.config.api.response;

import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

@Getter
public class EntityConfiguration<T extends Fact> implements Serializable {

  private final String entity;
  private final List<Dimension> dimensions = new ArrayList<>();
  private final List<Filter> filters = new ArrayList<>();

  public EntityConfiguration(Class<T> fact, List<Dimension> dimensions, List<Filter> filters) {
    this.entity = fact.getSimpleName().toLowerCase();
    this.dimensions.addAll(dimensions);
    this.filters.addAll(filters);
  }
}
