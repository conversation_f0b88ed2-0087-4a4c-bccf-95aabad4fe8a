package com.sling.sales.report.config.domain;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Getter;

@Getter
public class Lookup implements Serializable {

  private final String entity;
  private final String lookupUrl;

  @JsonCreator
  public Lookup(
      @JsonProperty("entity") String entity, @JsonProperty("lookupUrl") String lookupUrl) {
    this.entity = entity;
    this.lookupUrl = lookupUrl;
  }
}
