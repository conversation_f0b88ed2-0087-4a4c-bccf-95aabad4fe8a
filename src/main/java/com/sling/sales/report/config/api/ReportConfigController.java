package com.sling.sales.report.config.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.sling.sales.report.config.api.response.EntityConfiguration;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.report.ReportType;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping(value = "/v1/reports/config")
public class ReportConfigController {

  private final ReportConfigurationCacheService reportConfigurationCacheService;

  @Autowired
  public ReportConfigController(ReportConfigurationCacheService reportConfigurationCacheService) {
    this.reportConfigurationCacheService = reportConfigurationCacheService;
  }

  @ApiOperation(
      value = "Get entity configurations",
      code = 200,
      response = EntityConfiguration[].class)
  @GetMapping(value = "", produces = APPLICATION_JSON_VALUE)
  public Flux<EntityConfiguration<? extends Fact>> getReportConfiguration() {
    return reportConfigurationCacheService.getAllConfigurations();
  }

  @ApiOperation(
      value = "Get Lead configurations",
      code = 200,
      response = EntityConfiguration.class)
  @GetMapping(value = "/leads", produces = APPLICATION_JSON_VALUE)
  public Mono<EntityConfiguration<? extends Fact>> getLeadReportConfiguration() {
    return reportConfigurationCacheService.getEntityConfiguration(ReportType.LEAD);
  }

  @ApiOperation(
      value = "Get Deal configurations",
      code = 200,
      response = EntityConfiguration.class)
  @GetMapping(value = "/deals", produces = APPLICATION_JSON_VALUE)
  public Mono<EntityConfiguration<? extends Fact>> getDealReportConfiguration() {
    return reportConfigurationCacheService.getEntityConfiguration(ReportType.DEAL);
  }

  @ApiOperation(
      value = "Get Call configurations",
      code = 200,
      response = EntityConfiguration.class)
  @GetMapping(value = "/calls", produces = APPLICATION_JSON_VALUE)
  public Mono<EntityConfiguration<? extends Fact>> getCallReportConfiguration() {
    return reportConfigurationCacheService.getEntityConfiguration(ReportType.CALL);
  }

  @ApiOperation(
      value = "Get Task configurations",
      code = 200,
      response = EntityConfiguration.class)
  @GetMapping(value = "/tasks", produces = APPLICATION_JSON_VALUE)
  public Mono<EntityConfiguration<? extends Fact>> getTaskReportConfiguration() {
    return reportConfigurationCacheService.getEntityConfiguration(ReportType.TASK);
  }

  @ApiOperation(
      value = "Get Company configurations",
      code = 200,
      response = EntityConfiguration.class)
  @GetMapping(value = "/companies", produces = APPLICATION_JSON_VALUE)
  public Mono<EntityConfiguration<? extends Fact>> getCompanyReportConfiguration() {
    return reportConfigurationCacheService.getEntityConfiguration(ReportType.COMPANY);
  }

  @ApiOperation(
      value = "Get Meeting configurations",
      code = 200,
      response = EntityConfiguration.class)
  @GetMapping(value = "/meetings", produces = APPLICATION_JSON_VALUE)
  public Mono<EntityConfiguration<? extends Fact>> getMeetingReportConfiguration() {
    return reportConfigurationCacheService.getEntityConfiguration(ReportType.MEETING);
  }

  @ApiOperation(
      value = "Get Contact configurations",
      code = 200,
      response = EntityConfiguration.class)
  @GetMapping(value = "/contacts", produces = APPLICATION_JSON_VALUE)
  public Mono<EntityConfiguration<? extends Fact>> getContactReportConfiguration() {
    return reportConfigurationCacheService.getEntityConfiguration(ReportType.CONTACT);
  }

  @ApiOperation(
      value = "Get Email configurations",
      code = 200,
      response = EntityConfiguration.class)
  @GetMapping(value = "/emails", produces = APPLICATION_JSON_VALUE)
  public Mono<EntityConfiguration<? extends Fact>> getEmailReportConfiguration() {
    return reportConfigurationCacheService.getEntityConfiguration(ReportType.EMAIL);
  }
}
