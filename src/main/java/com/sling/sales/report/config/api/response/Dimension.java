package com.sling.sales.report.config.api.response;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

@Getter
public class Dimension implements Serializable {

  private final String id;
  private final String header;
  private final List<Metric> supportedMetrics = new ArrayList<>();
  private final List<String> requiredFilters = new ArrayList<>();
  private final boolean filterable;
  private final boolean active;
  private final String fieldType;
  private final Lookup lookup;
  private final String primaryField;
  private final String property;

  public Dimension(
      String id, String header, List<Metric> supportedMetrics, List<String> requiredFilters, boolean filterable, boolean active,
      String fieldType, Lookup lookup, String property, String primaryField) {
    this.id = id;
    this.header = header;
    this.filterable = filterable;
    this.active = active;
    this.fieldType = fieldType;
    this.supportedMetrics.addAll(supportedMetrics);
    this.requiredFilters.addAll(requiredFilters);
    this.primaryField = primaryField;
    this.property = property;
    this.lookup = lookup;
  }
}
