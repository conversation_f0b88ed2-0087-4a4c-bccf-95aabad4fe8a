package com.sling.sales.report.config.api;

import com.sling.sales.report.config.api.response.EntityConfiguration;
import com.sling.sales.report.config.domain.ConfigFacade;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.service.CustomFieldService;
import com.sling.sales.report.dto.DimensionDetail;
import com.sling.sales.report.security.domain.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
public class ReportConfigurationService {

  private final ConfigFacade configFacade;
  private final UserService userService;
  private final CustomFieldService customFieldService;

  @Autowired
  public ReportConfigurationService(ConfigFacade configFacade, UserService userService,
      CustomFieldService customFieldService) {
    this.configFacade = configFacade;
    this.userService = userService;
    this.customFieldService = customFieldService;
  }

  public <T extends Fact> Mono<EntityConfiguration<T>> getEntityConfiguration(Class<T> factClass) {
    var loggedInUser = userService.getLoggedInUser();
    var authenticationToken = userService.getAuthenticationToken();
    DimensionDetail<T> dimensionDetail = customFieldService.getFactDimensionsForCustomFieldsByEntity(factClass, loggedInUser);
    Flux<EntityConfiguration<T>> entityConfig = configFacade
        .getEntityConfigurationFor(loggedInUser, authenticationToken, factClass, dimensionDetail);
    return entityConfig.singleOrEmpty();
  }
}
