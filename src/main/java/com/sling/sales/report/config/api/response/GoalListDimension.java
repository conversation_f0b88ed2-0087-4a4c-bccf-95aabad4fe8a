package com.sling.sales.report.config.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoalListDimension implements Serializable {

  private final String id;
  private final String header;

  @JsonCreator
  public GoalListDimension(@JsonProperty("id") String id, @JsonProperty("header") String header) {
    this.id = id;
    this.header = header;
  }
}
