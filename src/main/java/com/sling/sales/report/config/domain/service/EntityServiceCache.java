package com.sling.sales.report.config.domain.service;

import com.sling.sales.report.cache.CacheFacade;
import com.sling.sales.report.config.domain.Field;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class EntityServiceCache {

  private final EntityService entityService;
  private final CacheFacade cacheFacade;


  @Autowired
  public EntityServiceCache(EntityService entityService, CacheFacade cacheFacade) {
    this.entityService = entityService;
    this.cacheFacade = cacheFacade;
  }

  public <T extends Fact> List<Field> getFiltersByEntity(long tenantId, String entityName, Class<T> entity, String authenticationToken) {
    List<Field> fields = cacheFacade.getFields(tenantId, entityName);
    if (ObjectUtils.isNotEmpty(fields)) {
      return fields;
    }
    List<Field> filtersByEntity1 = entityService.getFiltersByEntity1(entity, authenticationToken);
    cacheFacade.putFields(tenantId, entityName, filtersByEntity1);
    return filtersByEntity1;
  }
  public void refreshCache(long tenantId, String entityName){
    cacheFacade.refreshFieldsCache(tenantId,entityName.toLowerCase());
  }
}
