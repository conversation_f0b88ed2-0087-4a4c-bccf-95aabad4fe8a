package com.sling.sales.report.config.domain;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class FieldDto {

  private final String name;
  private final PicklistDto picklist;

  @JsonCreator
  public FieldDto(@JsonProperty("name") String name, @JsonProperty("picklist") PicklistDto picklist) {
    this.name = name;
    this.picklist = picklist;
  }
}
