package com.sling.sales.report.config.domain;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomPicklist implements Serializable {

  private final Long id;
  private final String name;
  private final List<CustomPicklistValue> values;

  @JsonCreator
  public CustomPicklist(@JsonProperty("id") Long id, @JsonProperty("name") String name,
      @JsonProperty("values") @JsonAlias({"pickListValues","picklistValues"}) List<CustomPicklistValue> values) {
    this.id = id;
    this.name = name;
    this.values = values;
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class CustomPicklistValue implements Serializable {

    private final Long id;
    private final String name;
    private final String displayName;

    @JsonCreator
    public CustomPicklistValue(
        @JsonProperty("id") Long id,
        @JsonProperty("name") String name,
        @JsonProperty("displayName") String displayName) {
      this.id = id;
      this.name = name;
      this.displayName = displayName;
    }
  }
}
