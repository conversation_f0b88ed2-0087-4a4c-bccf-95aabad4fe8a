package com.sling.sales.report.config.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class EntityGoalListConfiguration implements Serializable {

  private final List<GoalListDimension> dimensions;
  private final List<GoalListMetric> metrics;

  @JsonCreator
  public EntityGoalListConfiguration(@JsonProperty("dimensions") List<GoalListDimension> dimensions,
      @JsonProperty("metrics") List<GoalListMetric> metrics) {
    this.dimensions = dimensions;
    this.metrics = metrics;
  }
}
