package com.sling.sales.report.config.api.response;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

@Getter
public class GoalDimension implements Serializable {

  private final String id;
  private final String header;
  private final List<Metric> supportedMetrics = new ArrayList<>();
  private final List<String> requiredFilters = new ArrayList<>();
  private final boolean filterable;
  private final boolean active;
  private final String fieldType;
  private final Lookup lookup;
  private final Picklist picklist;
  private final String primaryField;
  private final String property;

  public GoalDimension(
      String id, String header, List<Metric> supportedMetrics, List<String> requiredFilters, boolean filterable, boolean active,
      String fieldType, Lookup lookup, Picklist picklist, String primaryField, String property) {
    this.id = id;
    this.header = header;
    this.filterable = filterable;
    this.active = active;
    this.fieldType = fieldType;
    this.supportedMetrics.addAll(supportedMetrics);
    this.requiredFilters.addAll(requiredFilters);
    this.lookup = lookup;
    this.picklist = picklist;
    this.primaryField = primaryField;
    this.property = property;
  }
}
