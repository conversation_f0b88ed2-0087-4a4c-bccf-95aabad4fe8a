package com.sling.sales.report.config.domain.service;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.config.domain.Field;
import com.sling.sales.report.config.domain.FieldDto;
import com.sling.sales.report.core.domain.aggregation.exception.InvalidFactForReporting;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.call.Call;
import com.sling.sales.report.core.domain.company.Company;
import com.sling.sales.report.core.domain.contact.Contact;
import com.sling.sales.report.core.domain.deal.Deal;
import com.sling.sales.report.core.domain.email.Email;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.core.domain.lead.Lead;
import com.sling.sales.report.core.domain.meeting.Meeting;
import com.sling.sales.report.core.domain.task.Task;
import com.sling.sales.report.dto.EntityLabelDto;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClient.Builder;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class EntityService {

  private final String salesBasePath;
  private final String dealBasePath;
  private final String configBasePath;
  private final String companyBasePath;
  private final String meetingBasePath;
  private final String callBasePath;
  private final String emailBasePath;
  private final RestTemplate restTemplate;

  @Autowired
  public EntityService(
      @Value("${client.sales.basePath}") String salesBasePath,
      @Value("${client.deal.basePath}") String dealBasePath,
      @Value("${client.config.basePath}") String configBasePath,
      @Value("${client.company.basePath}") String companyBasePath,
      @Value("${client.meeting.basePath}") String meetingBasePath,
      @Value("${client.call.basePath}") String callBasePath,
      @Value("${client.email.basePath}") String emailBasePath, RestTemplate restTemplate) {
    this.salesBasePath = salesBasePath;
    this.dealBasePath = dealBasePath;
    this.configBasePath = configBasePath;
    this.companyBasePath = companyBasePath;
    this.meetingBasePath = meetingBasePath;
    this.callBasePath = callBasePath;
    this.emailBasePath = emailBasePath;
    this.restTemplate = restTemplate;
  }

  public <T extends Fact> Mono<List<Field>> getFiltersByEntity(Class<T> entity, String authenticationToken) {
    return getWebClientBuilder()
        .baseUrl(getBasePath(entity))
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path(getEntityPath(entity)).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(Layout.class)
        .map(layout -> getFields(entity, layout));
  }

  public <T extends Fact> List<Field> getFiltersByEntity1(Class<T> entity, String authenticationToken) {
    HttpHeaders headers = new HttpHeaders();
    headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
    headers.set(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken);

    HttpEntity<String> entityHeaders = new HttpEntity<>(headers);

    Layout layout = restTemplate.exchange(
        getBasePath(entity) + getEntityPath(entity),
        HttpMethod.GET,
        entityHeaders,
        Layout.class).getBody();

    return getFields(entity, layout);

  }

  private <T extends Fact> List<Field> getFields(Class<T> entity, Layout layout) {
    PageConfig pageConfig = layout.getPageConfig();
    TableConfig tableConfig = pageConfig.getTableConfig();
    if (Call.class.equals(entity)) {
      return tableConfig.getFields()
          .stream()
          .map(this::getFieldWithUpdatedHeader)
          .collect(Collectors.toList());
    }
    if (Email.class.equals(entity)) {
      return tableConfig.getFields()
          .stream()
          .filter(field -> !field.getId().equalsIgnoreCase("userFields"))
          .collect(Collectors.toList());
    }
    return tableConfig.getFields();
  }

  private Field getFieldWithUpdatedHeader(Field field) {
    if (field.getId().equals("duration")) {
      return field.withUpdatedHeader("Duration (In Minutes)");
    }
    if (field.getId().equals("callbackDuration")) {
      return field.withUpdatedHeader("Callback Duration (In Minutes)");
    }
    return field;
  }


  public FieldDto getFieldByEntityTypeAndFieldName(String entityType, String fieldName, String authToken) {
    HttpHeaders headers = new HttpHeaders();
    headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
    headers.set(HttpHeaders.AUTHORIZATION, "Bearer " + authToken);

    HttpEntity<String> entityHeaders = new HttpEntity<>(headers);

    return restTemplate.exchange(
        getBasePath(Task.class) + "/v1/fields/" + entityType + "/" + fieldName,
        HttpMethod.GET,
            entityHeaders,
        FieldDto.class)
        .getBody();
  }

  public Map<EntityType, EntityLabelDto> getEntitiesLabel(String authenticationToken) {

    HttpHeaders headers = new HttpHeaders();
    headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
    headers.set(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken);

    HttpEntity<String> entityHeaders = new HttpEntity<>(headers);

    ResponseEntity<Map<EntityType, EntityLabelDto>> responseEntity = restTemplate.exchange(
        configBasePath + "/v1/entities/label",
        HttpMethod.GET,
        entityHeaders,
        new ParameterizedTypeReference<>() {
        });
    return responseEntity.getBody();
  }

  private <T extends Fact> String getBasePath(Class<T> clazz) {
    if (Lead.class.equals(clazz) || Contact.class.equals(clazz)) {
      return salesBasePath;
    }
    if (Deal.class.equals(clazz)) {
      return dealBasePath;
    }
    if (Task.class.equals(clazz)) {
      return configBasePath;
    }
    if (Company.class.equals(clazz)) {
      return companyBasePath;
    }
    if (Meeting.class.equals(clazz)) {
      return meetingBasePath;
    }
    if (Call.class.equals(clazz)) {
      return callBasePath;
    }
    if (Email.class.equals(clazz)) {
      return emailBasePath;
    }
    throw new InvalidFactForReporting();
  }

  private <T extends Fact> String getEntityPath(Class<T> clazz) {
    if (Lead.class.equals(clazz)) {
      return "/v1/leads/layout/list";
    }
    if (Contact.class.equals(clazz)) {
      return "/v1/contacts/layout/list";
    }
    if (Deal.class.equals(clazz)) {
      return "/v1/deals/layout/list";
    }
    if (Task.class.equals(clazz)) {
      return "/v1/ui/layouts/list/task";
    }
    if (Company.class.equals(clazz)) {
      return "/v1/companies/layout/list";
    }
    if (Meeting.class.equals(clazz)) {
      return "/v1/meetings/layout/list";
    }
    if (Call.class.equals(clazz)) {
      return "/v1/call-logs/layout/list";
    }
    if (Email.class.equals(clazz)) {
      return "/v1/emails/layout/list";
    }
    throw new InvalidFactForReporting();
  }

  @JsonIgnoreProperties(ignoreUnknown = true)
  @Getter
  public static class Layout {

    private final PageConfig pageConfig;

    @JsonCreator
    public Layout(@JsonProperty("pageConfig") PageConfig pageConfig) {
      this.pageConfig = pageConfig;
    }
  }

  @JsonIgnoreProperties(ignoreUnknown = true)
  @Getter
  public static class PageConfig {

    private final TableConfig tableConfig;

    @JsonCreator
    public PageConfig(@JsonProperty("tableConfig") TableConfig tableConfig) {
      this.tableConfig = tableConfig;
    }
  }

  @JsonIgnoreProperties(ignoreUnknown = true)
  @Getter
  public static class TableConfig {

    private final List<Field> fields = new ArrayList<>();

    @JsonCreator
    public TableConfig(@JsonProperty("columns") List<Field> fields) {
      this.fields.addAll(fields);
    }
  }

  private Builder getWebClientBuilder() {
    final int size = 16 * 1024 * 1024;
    ExchangeStrategies exchangeStrategies = ExchangeStrategies.builder()
        .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(size))
        .build();

    return WebClient.builder()
        .exchangeStrategies(exchangeStrategies);
  }
}
