package com.sling.sales.report.config.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import java.io.Serializable;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoalListMetric implements Serializable {

  private final MetricType type;
  private final String field;
  private final String header;

  @JsonCreator
  public GoalListMetric(@JsonProperty("type") MetricType type, @JsonProperty("field") String field, @JsonProperty("header") String header) {
    this.type = type;
    this.field = field;
    this.header = header;
  }
}
