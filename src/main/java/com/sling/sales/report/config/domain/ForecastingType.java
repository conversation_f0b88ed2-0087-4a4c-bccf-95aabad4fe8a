package com.sling.sales.report.config.domain;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum ForecastingType {
  OPEN("Open"),
  CLOSED_WON("Won"),
  CLOSED_UNQUALIFIED("Unqualified"),
  CLOSED_LOST("Lost"),
  CLOSED("Closed");

  private final String displayName;

  ForecastingType(String displayName) {
    this.displayName = displayName;
  }

  public String getDisplayName() {
    return displayName;
  }

  public static List<String> toStringValues() {
    return Arrays.stream(ForecastingType.values())
        .map(Enum::name)
        .collect(Collectors.toList());
  }
}
