package com.sling.sales.report.config.api.response;

import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

@Getter
public class EntityGoalConfiguration<T extends Fact> implements Serializable {

  private final String entity;
  private final List<GoalDimension> dimensions = new ArrayList<>();
  private final List<Filter> filters = new ArrayList<>();
  private final List<FrequencyValue> frequencies;

  public EntityGoalConfiguration(Class<T> fact, List<GoalDimension> dimensions,
      List<Filter> filters, List<FrequencyValue> frequencies) {
    this.entity = fact.getSimpleName().toLowerCase();
    this.dimensions.addAll(dimensions);
    this.filters.addAll(filters);
    this.frequencies = frequencies;
  }

  private EntityGoalConfiguration(String entity, List<GoalDimension> dimensions,
      List<Filter> filters, List<FrequencyValue> frequencies) {
    this.entity = entity;
    this.dimensions.addAll(dimensions);
    this.filters.addAll(filters);
    this.frequencies = frequencies;
  }

  public EntityGoalConfiguration<T> withFrequencies(List<FrequencyValue> frequencies) {
    return new EntityGoalConfiguration<>(this.entity, this.dimensions, this.filters, frequencies);
  }

}
