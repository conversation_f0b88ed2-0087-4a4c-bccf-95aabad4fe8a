package com.sling.sales.report.config.domain;

import static java.util.Arrays.asList;
import static java.util.stream.Collectors.toList;

import com.sling.sales.report.config.api.response.Dimension;
import com.sling.sales.report.config.api.response.EntityConfiguration;
import com.sling.sales.report.config.api.response.EntityGoalConfiguration;
import com.sling.sales.report.config.api.response.EntityGoalListConfiguration;
import com.sling.sales.report.config.api.response.Filter;
import com.sling.sales.report.config.api.response.GoalDimension;
import com.sling.sales.report.config.api.response.GoalListDimension;
import com.sling.sales.report.config.api.response.GoalListMetric;
import com.sling.sales.report.config.api.response.Lookup;
import com.sling.sales.report.config.api.response.Picklist;
import com.sling.sales.report.config.api.response.Picklist.PicklistValue;
import com.sling.sales.report.config.domain.service.EntityService;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.AbstractFactFieldFactory;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFieldFactory;
import com.sling.sales.report.core.domain.field.FieldType;
import com.sling.sales.report.dto.DimensionDetail;
import com.sling.sales.report.security.domain.User;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
public class ConfigFacade {

  private final EntityService entityService;
  private final AbstractFactFieldFactory abstractFactFieldFactory;
  private static final Map<String, List<String>> ALLOWED_FIELD_TYPES =
      Map.of("LOOK_UP", List.of("USER", "PRODUCT"),
          "MEETING_INVITEES", Collections.emptyList());
  private static final List<String> NOT_SUPPORTED_ENTITY_FIELDS = List.of("sentByFields", "receivedByFields");

  @Autowired
  public ConfigFacade(EntityService entityService,
      AbstractFactFieldFactory abstractFactFieldFactory) {
    this.entityService = entityService;
    this.abstractFactFieldFactory = abstractFactFieldFactory;
  }

  public <T extends Fact> Flux<EntityConfiguration<T>> getEntityConfigurationFor(User loggedInUser, String authenticationToken, Class<T> clazz,
      DimensionDetail<T> dimensionDetail) {
    return getFactEntityConfigurationFor(authenticationToken, clazz, dimensionDetail);
  }

  public <T extends Fact> EntityGoalConfiguration<T> getEntityConfigurationForGoal(User loggedInUser, String authenticationToken,
      Class<T> clazz,
      DimensionDetail<T> customFieldDimensionDetail) {
    return getFactEntityConfigurationForGoal(authenticationToken, clazz, customFieldDimensionDetail);
  }

  public <T extends Fact> Mono<EntityGoalListConfiguration> getEntityConfigurationForGoalListing(String authenticationToken,
      Class<T> clazz, DimensionDetail<T> customDimensionDetail) {
    return getFactEntityConfigurationForGoalListing(authenticationToken, clazz, customDimensionDetail);
  }

  public <T extends Fact> EntityGoalListConfiguration getEntityConfigurationForGoalListing1(String authenticationToken,
      Class<T> clazz, DimensionDetail<T> customDimensionDetail) {
    return getFactEntityConfigurationForGoalListing1(authenticationToken, clazz, customDimensionDetail);
  }

  private <T extends Fact> EntityGoalConfiguration<T> getFactEntityConfigurationForGoal(String authenticationToken, Class<T> clazz,
      DimensionDetail<T> customFieldDimensionDetail) {

    var factFieldsMono = entityService.getFiltersByEntity1(clazz, authenticationToken);

    FactFieldFactory<T> factFieldFactory = abstractFactFieldFactory.getFieldFactory(clazz);
    DimensionDetail<T> dimensionDetail = factFieldFactory.getDimensionDetail();

    HashMap<String, FilterDimension<T>> filterDimensions = new HashMap<>();
    filterDimensions.putAll(dimensionDetail.getFilterDimensions());
    filterDimensions.putAll(customFieldDimensionDetail.getFilterDimensions());

    Map<String, GroupByDimension<T>> groupByDimensions = new HashMap<>();
    groupByDimensions.putAll(dimensionDetail.getGroupByDimensions());
    groupByDimensions.putAll(customFieldDimensionDetail.getGroupByDimensions());

    List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> metrics = new ArrayList<>();
    metrics.addAll(dimensionDetail.getMetrics());
    metrics.addAll(customFieldDimensionDetail.getMetrics());
    Map<String, Field> fieldMap = fieldToMap(factFieldsMono);

    var filteredGroupByDimensions = getFilteredGroupByDimensions(groupByDimensions, fieldMap);
    var supportedMetrics = toSupportedMetrics(metrics, fieldMap);

    var applicableFilters =
        fieldMap.values().stream()
            .map(f -> toFilter(f, filterDimensions))
            .filter(Optional::isPresent)
            .map(Optional::get)
            .collect(toList());

    var applicableDimensions =
        fieldMap.values().stream()
            .map(f -> toGoalDimension(f, filteredGroupByDimensions, filterDimensions, supportedMetrics))
            .filter(Optional::isPresent)
            .map(Optional::get)
            .collect(toList());

    var factConfiguration =
        new EntityGoalConfiguration<>(clazz, applicableDimensions, applicableFilters, Collections.emptyList());
    return factConfiguration;
  }


  private <T extends Fact> Mono<EntityGoalListConfiguration> getFactEntityConfigurationForGoalListing(String authenticationToken, Class<T> clazz,
      DimensionDetail<T> customDimensionDetail) {

    var factFieldsMono = entityService.getFiltersByEntity(clazz, authenticationToken);

    FactFieldFactory<T> factFieldFactory = abstractFactFieldFactory.getFieldFactory(clazz);
    DimensionDetail<T> dimensionDetail = factFieldFactory.getDimensionDetail();

    Map<String, GroupByDimension<T>> groupByDimensions = new HashMap<>();
    groupByDimensions.putAll(dimensionDetail.getGroupByDimensions());
    groupByDimensions.putAll(customDimensionDetail.getGroupByDimensions());

    List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> metrics = new ArrayList<>();
    metrics.addAll(dimensionDetail.getMetrics());
    metrics.addAll(customDimensionDetail.getMetrics());
    Mono<Map<String, Field>> fieldsMap = fieldToMap(factFieldsMono);
    return fieldsMap.map(
        fieldMap -> {

          var filteredGroupByDimensions = getFilteredGroupByDimensions(groupByDimensions, fieldMap);

          var applicableDimensions =
              fieldMap.values().stream()
                  .map(f -> toGoalListDimension(f, filteredGroupByDimensions))
                  .filter(Optional::isPresent)
                  .map(Optional::get)
                  .collect(toList());

          var goalListMetrics = toGoalListMetric(metrics, fieldMap);

          return new EntityGoalListConfiguration(applicableDimensions, goalListMetrics);
        });
  }

  private <T extends Fact> EntityGoalListConfiguration getFactEntityConfigurationForGoalListing1(String authenticationToken, Class<T> clazz,
      DimensionDetail<T> customDimensionDetail) {

    var factFieldsMono = entityService.getFiltersByEntity1(clazz, authenticationToken);

    FactFieldFactory<T> factFieldFactory = abstractFactFieldFactory.getFieldFactory(clazz);
    DimensionDetail<T> dimensionDetail = factFieldFactory.getDimensionDetail();

    Map<String, GroupByDimension<T>> groupByDimensions = new HashMap<>();
    groupByDimensions.putAll(dimensionDetail.getGroupByDimensions());
    groupByDimensions.putAll(customDimensionDetail.getGroupByDimensions());

    List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> metrics = new ArrayList<>();
    metrics.addAll(dimensionDetail.getMetrics());
    metrics.addAll(customDimensionDetail.getMetrics());
    Map<String, Field> fieldsMap = fieldToMap(factFieldsMono);

    var filteredGroupByDimensions = getFilteredGroupByDimensions(groupByDimensions, fieldsMap);

    var applicableDimensions =
        fieldsMap.values().stream()
            .map(f -> toGoalListDimension(f, filteredGroupByDimensions))
            .filter(Optional::isPresent)
            .map(Optional::get)
            .collect(toList());

    var goalListMetrics = toGoalListMetric(metrics, fieldsMap);

    return new EntityGoalListConfiguration(applicableDimensions, goalListMetrics);

  }

  private <T extends Fact> Map<String, GroupByDimension<T>> getFilteredGroupByDimensions(Map<String, GroupByDimension<T>> groupByDimensions,
      Map<String, Field> fieldMap) {
    return fieldMap.entrySet()
        .stream()
        .filter(stringFieldEntry -> filterByFieldType(stringFieldEntry.getValue()))
        .map(stringFieldEntry -> groupByDimensions.getOrDefault(stringFieldEntry.getKey(), null))
        .filter(ObjectUtils::isNotEmpty)
        .collect(Collectors.toMap(com.sling.sales.report.core.domain.aggregation.dimension.Dimension::getName, groupByDimension -> groupByDimension));
  }

  private boolean filterByFieldType(Field field) {
    if (!ALLOWED_FIELD_TYPES.containsKey(field.getFieldType())) {
      return false;
    }
    if (FieldType.LOOK_UP.equals(FieldType.valueOf(field.getFieldType().toUpperCase()))) {
      List<String> lookUpFieldTypes = ALLOWED_FIELD_TYPES.getOrDefault(field.getFieldType(), Collections.emptyList());
      if (lookUpFieldTypes.isEmpty()) {
        return false;
      }
      return lookUpFieldTypes.contains(field.getLookup().getEntity());
    }
    return true;
  }

  private <T extends Fact> Flux<EntityConfiguration<T>> getFactEntityConfigurationFor(String authenticationToken, Class<T> clazz,
      DimensionDetail<T> customFieldDimensionDetail) {

    FactFieldFactory<T> factFieldFactory = abstractFactFieldFactory.getFieldFactory(clazz);
    DimensionDetail<T> dimensionDetail = factFieldFactory.getDimensionDetail();

    HashMap<String, FilterDimension<T>> filterDimensions = new HashMap<>();
    filterDimensions.putAll(dimensionDetail.getFilterDimensions());
    filterDimensions.putAll(customFieldDimensionDetail.getFilterDimensions());

    Map<String, GroupByDimension<T>> groupByDimensions = new HashMap<>();
    groupByDimensions.putAll(dimensionDetail.getGroupByDimensions());
    groupByDimensions.putAll(customFieldDimensionDetail.getGroupByDimensions());

    List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> metrics = new ArrayList<>();
    metrics.addAll(dimensionDetail.getMetrics());
    metrics.addAll(customFieldDimensionDetail.getMetrics());

    var factFieldsMono = entityService.getFiltersByEntity(clazz, authenticationToken);
    Mono<Map<String, Field>> fieldsMap = fieldToMap(factFieldsMono);
    return fieldsMap.flatMapMany(
        fieldMap -> {

          var supportedMetrics = toSupportedMetrics(metrics, fieldMap);

          var applicableFilters =
              fieldMap.values().stream()
                  .map(f -> toFilter(f, filterDimensions))
                  .filter(Optional::isPresent)
                  .map(Optional::get)
                  .collect(toList());

          var applicableDimensions =
              fieldMap.values().stream()
                  .map(f -> toDimension(f, groupByDimensions, filterDimensions, supportedMetrics))
                  .filter(Optional::isPresent)
                  .map(Optional::get)
                  .collect(toList());

          var factConfiguration =
              new EntityConfiguration<>(clazz, applicableDimensions, applicableFilters);
          return Flux.fromIterable(asList(factConfiguration));
        });
  }

  private Mono<Map<String, Field>> fieldToMap(Mono<List<Field>> fields) {
    return fields
        .flatMap(allFields -> {
          Map<String, Field> fieldsMap = allFields.stream()
              .collect(Collectors.toMap(Field::getId, field -> field));
          return Mono.just(fieldsMap);
        });
  }

  private Map<String, Field> fieldToMap(List<Field> fields) {
    return fields
        .stream()
        .collect(Collectors.toMap(Field::getId, field -> field));
  }

  private <T extends Fact> Optional<Dimension> toDimension(Field field, Map<String, GroupByDimension<T>> groupByDimensions,
      Map<String, FilterDimension<T>> filterDimensions, List<Metric> supportedMetrics) {

    if ("ENTITY_FIELDS".equals(field.getFieldType()) && !NOT_SUPPORTED_ENTITY_FIELDS.contains(field.getId())) {
      return Optional.of(
          new Dimension(field.getId(), field.getHeader(), supportedMetrics, getRequiredFilters(field), field.isFilterable(), field.isActive(),
              field.getFieldType(), toLookup(field), getProperty(field), field.getPrimaryField()));
    }

    if (!groupByDimensions.containsKey(field.getId())) {
      return Optional.empty();
    }

    if (filterDimensions.containsKey(field.getId())) {
      return Optional.of(getDimension(field, supportedMetrics, filterDimensions.get(field.getId())));
    }
    return Optional.of(getDimensionForCustomField(field, supportedMetrics));
  }

  private <T extends Fact> Optional<GoalDimension> toGoalDimension(Field field, Map<String, GroupByDimension<T>> groupByDimensions,
      Map<String, FilterDimension<T>> filterDimensions, List<Metric> supportedMetrics) {

    if (!groupByDimensions.containsKey(field.getId())) {
      return Optional.empty();
    }

    if (filterDimensions.containsKey(field.getId())) {
      return Optional.of(getGoalDimension(field, supportedMetrics, filterDimensions.get(field.getId())));
    }
    return Optional.of(getGoalDimensionForCustomField(field, supportedMetrics));
  }

  private <T extends Fact> Optional<GoalListDimension> toGoalListDimension(Field field, Map<String, GroupByDimension<T>> groupByDimensions) {

    if (!groupByDimensions.containsKey(field.getId())) {
      return Optional.empty();
    }
    return Optional.of(new GoalListDimension(field.getId(), field.getHeader()));
  }

  private <T extends Fact> Dimension getDimension(Field field, List<Metric> supportedMetrics,
      com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension<T> fieldDimension) {
    var requiredFilters =
        fieldDimension.getRequiredFilters().stream()
            .map(com.sling.sales.report.core.domain.aggregation.dimension.Dimension::getName)
            .collect(toList());

    return new Dimension(
        field.getId(), field.getHeader(), supportedMetrics, requiredFilters, field.isFilterable(), field.isActive(), field.getFieldType(),
        toLookup(field), getProperty(field), field.getPrimaryField()
    );
  }

  private String getProperty(Field field) {
    if (ObjectUtils.isNotEmpty(field.getLookup()) && field.getLookup().getEntity().equals("USER") && field.getFieldType()
        .equals("ENTITY_FIELDS")) {
      return "teams";
    }
    return null;
  }

  private <T extends Fact> GoalDimension getGoalDimension(Field field, List<Metric> supportedMetrics,
      com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension<T> fieldDimension) {
    var requiredFilters =
        fieldDimension.getRequiredFilters().stream()
            .map(com.sling.sales.report.core.domain.aggregation.dimension.Dimension::getName)
            .collect(toList());

    return new GoalDimension(
        field.getId(), field.getHeader(), supportedMetrics, requiredFilters, field.isFilterable(), field.isActive(), field.getFieldType(),
        toGoalLookup(field), toPicklist(field), field.getPrimaryField(), getProperty(field));
  }

  private <T extends Fact> List<Metric> toSupportedMetrics(List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> metrics,
      Map<String, Field> fields) {
    return metrics
        .stream()
        .map(m -> new Metric(m.getType(), m.getField(), getHeader(m.getField(), fields)))
        .collect(toList());
  }

  private <T extends Fact> List<GoalListMetric> toGoalListMetric(List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> metrics,
      Map<String, Field> fields) {
    return metrics
        .stream()
        .map(m -> new GoalListMetric(m.getType(), m.getField(), getHeader(m.getField(), fields)))
        .collect(toList());
  }

  private String getHeader(String field, Map<String, Field> fields) {
    if (field.equals("id")) {
      return "Id";
    }
    if (field.equals("numberOfProducts")) {
      return "Products";
    }
    if (fields.containsKey(field)) {
      return fields.get(field).getHeader();
    }
    return field;
  }

  private Dimension getDimensionForCustomField(Field field, List<Metric> supportedMetrics) {
    return new Dimension(field.getId(), field.getHeader(), supportedMetrics, Collections.emptyList(), field.isFilterable(), field.isActive(),
        field.getFieldType(), toLookup(field), null, field.getPrimaryField());
  }

  private GoalDimension getGoalDimensionForCustomField(Field field, List<Metric> supportedMetrics) {
    return new GoalDimension(field.getId(), field.getHeader(), supportedMetrics, Collections.emptyList(), field.isFilterable(), field.isActive(),
        field.getFieldType(), toLookup(field), toPicklist(field), field.getPrimaryField(), null);
  }

  private <T extends Fact> Optional<Filter> toFilter(Field field, Map<String, FilterDimension<T>> filterDimensions) {

    if ("ENTITY_FIELDS".equals(field.getFieldType()) && !NOT_SUPPORTED_ENTITY_FIELDS.contains(field.getId())) {
      return toEntityPropertiesFilter(field);
    }

    if (!filterDimensions.containsKey(field.getId())) {
      return Optional.empty();
    }

    Lookup lookup = toLookup(field);
    Picklist picklist = toPicklist(field);
    var requiredFilters =
        filterDimensions.get(field.getId()).getRequiredFilters().stream()
            .map(com.sling.sales.report.core.domain.aggregation.dimension.Dimension::getName)
            .collect(toList());

    Filter filter = new Filter(
        field.getId(),
        field.getHeader(),
        field.getFieldType(),
        lookup,
        picklist,
        field.shouldShowDefaultOptions(),
        field.isFilterable(), field.isActive(), field.isInternal(), field.isStandard(), requiredFilters, field.getPrimaryField(),
        getProperty(field));
    return Optional.of(filter);
  }

  private Optional<Filter> toEntityPropertiesFilter(Field field) {
    Lookup lookup = toLookup(field);
    Picklist picklist = toPicklist(field);
    Filter filter = new Filter(
        field.getId(),
        field.getHeader(),
        field.getFieldType(),
        lookup,
        picklist,
        field.shouldShowDefaultOptions(),
        field.isFilterable(), field.isActive(), field.isInternal(), field.isStandard(), getRequiredFilters(field), field.getPrimaryField(),
        getProperty(field));
    return Optional.of(filter);
  }

  private Picklist toPicklist(Field field) {
    return field.getPicklist() != null
        ? new Picklist(
        field.getPicklist().getId(),
        field.getPicklist().getName(),
        getPicklistValues(field))
        : null;
  }

  private List<PicklistValue> getPicklistValues(Field field) {
    return field.getPicklist().getPicklistValues() == null ? Collections.emptyList() : field.getPicklist().getPicklistValues().stream()
        .map(
            picklistValue ->
                new PicklistValue(
                    picklistValue.getId(),
                    picklistValue.getName(),
                    picklistValue.getDisplayName()))
        .collect(toList());
  }

  private Lookup toLookup(Field field) {
    return field.getLookup() != null
        ? new Lookup(field.getLookup().getEntity(), field.getLookup().getLookupUrl())
        : null;
  }

  private Lookup toGoalLookup(Field field) {
    if (field.getId().equals("participants")) {
      return new Lookup("USER", "/users/lookup?q=name:");
    }
    return field.getLookup() != null
        ? new Lookup(field.getLookup().getEntity(), field.getLookup().getLookupUrl())
        : null;
  }

  private List<String> getRequiredFilters(Field field) {
    if (field.getId().equals("participantFields")) {
      return Collections.emptyList();
    }
    return Collections.singletonList(field.getPrimaryField());
  }
}
