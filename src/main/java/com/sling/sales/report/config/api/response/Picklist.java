package com.sling.sales.report.config.api.response;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

@Getter
public class Picklist implements Serializable {

  private final Long id;
  private final String name;
  private final List<PicklistValue> picklistValues = new ArrayList<>();

  public Picklist(Long id, String name, List<PicklistValue> picklistValues) {
    this.id = id;
    this.name = name;
    this.picklistValues.addAll(picklistValues);
  }

  @Getter
  public static class PicklistValue implements Serializable{
    private final Long id;
    private final String name;
    private final String displayName;

    public PicklistValue(Long id, String name, String displayName) {
      this.id = id;
      this.name = name;
      this.displayName = displayName;
    }
  }
}
