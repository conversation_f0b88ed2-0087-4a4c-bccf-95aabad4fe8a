package com.sling.sales.report.config.domain;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AccessLevel;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class Field implements Serializable {

  private final String id;
  private String header;
  private final boolean isFilterable;
  private final boolean isStandard;
  private final boolean isInternal;
  private final String fieldType;
  private final Picklist picklist;
  private final Lookup lookup;
  private final String primaryField;

  @Getter(AccessLevel.NONE)
  private final boolean showDefaultOptions;
  private final boolean active;

  @JsonCreator
  public Field(
      @JsonProperty("id") String id,
      @JsonProperty("header") String header,
      @JsonProperty("isFilterable") boolean isFilterable,
      @JsonProperty("isStandard") boolean isStandard, @JsonProperty("isInternal") boolean isInternal,
      @JsonProperty("fieldType") String fieldType,
      @JsonProperty("picklist") Picklist picklist,
      @JsonProperty("lookup") Lookup lookup,
      @JsonProperty("showDefaultOptions") boolean showDefaultOptions,
      @JsonProperty("active") boolean active,
      @JsonProperty("primaryField") String primaryField) {
    this.id = id;
    this.header = header;
    this.isFilterable = isFilterable;
    this.isStandard = isStandard;
    this.isInternal = isInternal;
    this.fieldType = fieldType;
    this.picklist = picklist;
    this.lookup = lookup;
    this.showDefaultOptions = showDefaultOptions;
    this.active = active;
    this.primaryField = primaryField;
  }

  public boolean shouldShowDefaultOptions() {
    return this.showDefaultOptions;
  }

  public Field withUpdatedHeader(String header) {
    this.header = header;
    return this;
  }
}
