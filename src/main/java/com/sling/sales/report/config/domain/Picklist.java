package com.sling.sales.report.config.domain;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

@Getter
public class Picklist implements Serializable {

  private final Long id;
  private final String name;
  private final List<PicklistValue> picklistValues = new ArrayList<>();

  @JsonCreator
  public Picklist(@JsonProperty("id") Long id, @JsonProperty("name") String name) {
    this.id = id;
    this.name = name;
  }

  @Getter
  public static class PicklistValue implements Serializable{
    private final Long id;
    private final String name;
    private final String displayName;

    @JsonCreator
    public PicklistValue(
        @JsonProperty("id") Long id,
        @JsonProperty("name") String name,
        @JsonProperty("displayName") String displayName) {
      this.id = id;
      this.name = name;
      this.displayName = displayName;
    }
  }
}
