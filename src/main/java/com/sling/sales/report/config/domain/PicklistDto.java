package com.sling.sales.report.config.domain;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.mq.event.IdName;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class PicklistDto {

  private final List<IdName> values;

  @JsonCreator
  public PicklistDto(@JsonProperty("values") List<IdName> values) {
    this.values = values;
  }
}
