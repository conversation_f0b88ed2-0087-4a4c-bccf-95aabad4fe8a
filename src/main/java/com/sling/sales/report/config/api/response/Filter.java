package com.sling.sales.report.config.api.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.AccessLevel;
import lombok.Getter;

@Getter
public class Filter implements Serializable {

  private final String id;
  private final String header;
  private final String fieldType;
  private final Lookup lookup;
  private final Picklist picklist;
  private final boolean showDefaultOptions;
  private final boolean filterable;
  private final boolean active;

  @Getter(AccessLevel.NONE)
  private final boolean internal;
  @Getter(AccessLevel.NONE)
  private final boolean standard;
  private final List<String> requiredFilters;
  private final String primaryField;
  private final String property;

  public Filter(
      String id,
      String header,
      String fieldType,
      Lookup lookup,
      Picklist picklist,
      boolean showDefaultOptions,
      boolean filterable, boolean active, boolean isInternal, boolean standard, List<String> requiredFilters, String primaryField,
      String property) {
    this.id = id;
    this.header = header;
    this.fieldType = fieldType;
    this.lookup = lookup;
    this.picklist = picklist;
    this.showDefaultOptions = showDefaultOptions;
    this.filterable = filterable;
    this.active = active;
    this.internal = isInternal;
    this.standard = standard;
    this.requiredFilters = requiredFilters;
    this.primaryField = primaryField;
    this.property = property;
  }

  public boolean shouldShowDefaultOptions() {
    return this.showDefaultOptions;
  }

  @JsonProperty("isInternal")
  public boolean isInternal() {
    return internal;
  }

  @JsonProperty("isStandard")
  public boolean isStandard() {
    return standard;
  }
}
