package com.sling.sales.report.config.api;

import com.sling.sales.report.cache.CacheFacade;
import com.sling.sales.report.config.api.response.EntityConfiguration;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.call.Call;
import com.sling.sales.report.core.domain.report.ReportType;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class ReportConfigurationCacheService {

  private final ReportConfigurationService reportConfigurationService;
  private final CacheFacade cacheFacade;
  private final UserService userService;

  @Autowired
  public ReportConfigurationCacheService(ReportConfigurationService reportConfigurationService, CacheFacade cacheFacade,
      UserService userService) {
    this.reportConfigurationService = reportConfigurationService;
    this.cacheFacade = cacheFacade;
    this.userService = userService;
  }

  public Flux<EntityConfiguration<? extends Fact>> getAllConfigurations() {
    Mono<EntityConfiguration<? extends Fact>> leadConfig = getEntityConfiguration(ReportType.LEAD);
    Mono<EntityConfiguration<? extends Fact>> dealConfig = getEntityConfiguration(ReportType.DEAL);
    Mono<EntityConfiguration<? extends Fact>> contactConfig = getEntityConfiguration(ReportType.CONTACT);
    Mono<EntityConfiguration<? extends Fact>> companyConfig = getEntityConfiguration(ReportType.COMPANY);
    Mono<EntityConfiguration<? extends Fact>> taskConfig = getEntityConfiguration(ReportType.TASK);
    Mono<EntityConfiguration<? extends Fact>> meetingConfig = getEntityConfiguration(ReportType.MEETING);
    Mono<EntityConfiguration<? extends Fact>> callConfig = getEntityConfiguration(ReportType.CALL);
    Mono<EntityConfiguration<? extends Fact>> emailConfig = getEntityConfiguration(ReportType.EMAIL);
    return Flux.concat(leadConfig, dealConfig, contactConfig, companyConfig, taskConfig, meetingConfig, callConfig, emailConfig);
  }

  public Mono<EntityConfiguration<? extends Fact>> getEntityConfiguration(ReportType entityType) {
    Class<? extends Fact> factClass = entityType.getEntityClass();
    User loggedInUser = userService.getLoggedInUser();
    if (!hasAccessToFacts(factClass, loggedInUser)) {
      return Mono.empty();
    }

    EntityConfiguration<? extends Fact> entityConfiguration =
        cacheFacade.getEntityConfiguration(loggedInUser.getTenantId(), entityType.name().toLowerCase());
    if (entityConfiguration != null){
      return Mono.just(entityConfiguration);
    }

    return reportConfigurationService.getEntityConfiguration(factClass)
        .map(configuration -> cacheFacade.putEntityConfiguration(loggedInUser.getTenantId(), entityType.name().toLowerCase(), configuration));
  }

  private <T extends  Fact> boolean hasAccessToFacts(Class<T> factClass, User loggedInUser) {
    if (Call.class.equals(factClass)) {
      return loggedInUser.canReadCall() && !loggedInUser.returnEntitiesHavingCallPermission().isEmpty();
    }
    return loggedInUser.hasAccessToFacts(factClass);
  }

  public void refreshEntityConfiguration(long tenantId, String entity) {
    cacheFacade.refreshEntityConfiguration(tenantId, entity.toLowerCase());
  }
}
