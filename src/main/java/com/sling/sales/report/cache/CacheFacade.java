package com.sling.sales.report.cache;

import com.sling.sales.report.config.api.response.EntityConfiguration;
import com.sling.sales.report.config.api.response.EntityGoalConfiguration;
import com.sling.sales.report.config.api.response.EntityGoalListConfiguration;
import com.sling.sales.report.config.domain.Field;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.dto.HeaderDetail;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CacheFacade {

  @Cacheable(value = "default#21600", key = "{#tenantId+'-report-'+#entityType+'-header-builder-reportId-'+#reportId}")
  public HeaderDetail getReportHeaderDetail(long tenantId, String entityType, long reportId) {
    log.info("Report header for tenantId {} for entityType {} does not present into cache for reportId {}", tenantId, entityType, reportId);
    return null;
  }

  @CachePut(value = "default#21600", key = "{#tenantId+'-report-'+#entityType+'-header-builder-reportId-'+#reportId}")
  public HeaderDetail putReportHeaderDetail(long tenantId, String entityType, HeaderDetail headerDetail, long reportId) {
    log.info("Report header for tenantId {} for entityType {} put into cache for reportId {}", tenantId, entityType, reportId);
    return headerDetail;
  }

  @CacheEvict(value = "default#21600", key = "{#tenantId+'-report-'+#entityType+'-header-builder-reportId-'+#reportId}")
  public void refreshReportHeader(long tenantId, String entityType, long reportId) {
    log.info("Report header for tenantId {} for entityType {} refreshed for reportId {}", tenantId, entityType, reportId);
  }

  @Cacheable(value = "default#21600", key = "{#tenantId+'-report-'+#entity+'-layout-fields'}")
  public EntityConfiguration<? extends Fact> getEntityConfiguration(long tenantId, String entity) {
    log.info("{} layout fields configuration not present into cache for tenant id {}", entity, tenantId);
    return null;
  }

  @CachePut(value = "default#21600", key = "{#tenantId+'-report-'+#entity+'-layout-fields'}")
  public EntityConfiguration<? extends Fact> putEntityConfiguration(long tenantId, String entity, EntityConfiguration<? extends Fact> configuration) {
    log.info("{} layout fields configuration put into cache tenant id {}", entity, tenantId);
    return configuration;
  }

  @CacheEvict(value = "default#21600", key = "{#tenantId+'-report-'+#entity+'-layout-fields'}")
  public void refreshEntityConfiguration(long tenantId, String entity) {
    log.info("{} layout fields configuration refreshed for tenant id {}", entity, tenantId);
  }

  @Cacheable(value = "default#21600", key = "{#tenantId+'-report-'+#entity+'-goal-layout-fields'}")
  public <T extends Fact> EntityGoalConfiguration<T> getGoalConfigurationByEntity(long tenantId, String entity) {
    log.info("{} goal layout fields configuration not present into cache for tenant id {}", entity, tenantId);
    return null;
  }

  @CachePut(value = "default#21600", key = "{#tenantId+'-report-'+#entity+'-goal-layout-fields'}")
  public <T extends Fact> EntityGoalConfiguration<T> putGoalConfigurationByEntity(long tenantId, String entity, EntityGoalConfiguration<T> configuration) {
    log.info("{} goal layout fields configuration put into cache tenant id {}", entity, tenantId);
    return configuration;
  }

  @CacheEvict(value = "default#21600", key = "{#tenantId+'-report-'+#entity+'-goal-layout-fields'}")
  public void refreshGoalConfigurationByEntity(long tenantId, String entity) {
    log.info("{} goal layout fields configuration refreshed for tenant id {}", entity, tenantId);
  }

  @Cacheable(value = "default#21600", key = "{#tenantId+'-report-'+#entity+'-fields'}")
  public List<Field> getFields(long tenantId, String entity) {
    log.info("{} fields not present into cache for tenant id {}", entity, tenantId);
    return null;
  }

  @CachePut(value = "default#21600", key = "{#tenantId+'-report-'+#entity+'-fields'}")
  public List<Field> putFields(long tenantId, String entity, List<Field> fields) {
    log.info("Put fields for entityType {} into cache for tenant id {}", entity, tenantId);
    return fields;
  }

  @CacheEvict(value = "default#21600", key = "{#tenantId+'-report-'+#entity+'-fields'}")
  public void refreshFieldsCache(long tenantId, String entity) {
    log.info("refresh fields for entityType {} from cache for tenant id {}", entity, tenantId);
  }

  @Cacheable(value = "default#21600", key = "{#tenantId+'-'+#entity+'-report-goal-listing'}")
  public EntityGoalListConfiguration getGoalListingConfigurationByEntity(long tenantId, String entity) {
    log.info("goal listing configuration not present into cache for tenant id {} and entity {}", tenantId, entity);
    return null;
  }

  @CachePut(value = "default#21600", key = "{#tenantId+'-'+#entity+'-report-goal-listing'}")
  public EntityGoalListConfiguration putGoalListingConfigurationByEntity(long tenantId, String entity,
      EntityGoalListConfiguration configuration) {
    log.info("goal listing configuration put into cache tenant id {} and entity {}", tenantId, entity);
    return configuration;
  }

  @CacheEvict(value = "default#21600", key = "{#tenantId+'-'+#entity+'-report-goal-listing'}")
  public void refreshGoalListingConfigurationByEntity(long tenantId, String entity) {
    log.info("goal listing configuration refreshed for tenant id {} and entity {}", tenantId, entity);
  }

  @Cacheable(value = "default#21600", key = "{#tenantId+'-'+#entityType+'-report-fields'}")
  public List<com.sling.sales.report.core.domain.field.Field> getFieldsByTenantIdAndEntityType(long tenantId, EntityType entityType) {
    return null;
  }

  @CachePut(value = "default#21600", key = "{#tenantId+'-'+#entityType+'-report-fields'}")
  public List<com.sling.sales.report.core.domain.field.Field> putFieldsByTenantIdAndEntityTypeIntoCache(long tenantId, EntityType entityType,List<com.sling.sales.report.core.domain.field.Field> fields) {
    log.info("Field cached for tenantId {} and entityType {}",tenantId,entityType);
    return fields;
  }

  @CacheEvict(value = "default#21600", key = "{#tenantId+'-'+#entityType+'-report-fields'}")
  public void refreshFieldCache(long tenantId, EntityType entityType) {
    log.info("Field cache evicted  for tenantId {} and entityType {}",tenantId,entityType);
  }

}
