package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.aggregation.dimension.DateCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyBooleanCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyTextCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberCustomFieldFilterDimension;
import com.sling.sales.report.core.domain.meeting.Meeting;
import com.sling.sales.report.core.domain.meeting.MeetingCustomCheckboxValue;
import com.sling.sales.report.core.domain.meeting.MeetingCustomCheckboxValue_;
import com.sling.sales.report.core.domain.meeting.MeetingCustomDatePickerValue;
import com.sling.sales.report.core.domain.meeting.MeetingCustomDatePickerValue_;
import com.sling.sales.report.core.domain.meeting.MeetingCustomDatetimePickerValue;
import com.sling.sales.report.core.domain.meeting.MeetingCustomDatetimePickerValue_;
import com.sling.sales.report.core.domain.meeting.MeetingCustomNumberValue;
import com.sling.sales.report.core.domain.meeting.MeetingCustomNumberValue_;
import com.sling.sales.report.core.domain.meeting.MeetingCustomPicklistValue;
import com.sling.sales.report.core.domain.meeting.MeetingCustomPicklistValue_;
import com.sling.sales.report.core.domain.meeting.MeetingCustomTextValue;
import com.sling.sales.report.core.domain.meeting.MeetingCustomTextValue_;
import com.sling.sales.report.core.domain.meeting.Meeting_;
import com.sling.sales.report.dto.DimensionDetail;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class MeetingCustomFieldFactory {

  public static DimensionDetail<Meeting> createManyIdCustomFieldDimension(String dimensionName, long fieldId) {
    ManyIdCustomFieldDimension<Meeting, MeetingCustomPicklistValue> manyIdCustomFieldDimension = new ManyIdCustomFieldDimension<>(
        dimensionName,
        Meeting_.MEETING_PICKLIST_VALUES,
        Meeting_.meetingPicklistValues,
        join -> join.get(MeetingCustomPicklistValue_.picklistValueId),
        join -> join.get(MeetingCustomPicklistValue_.displayName),
        join -> join.get(MeetingCustomPicklistValue_.fieldId),
        fieldId, MeetingCustomPicklistValue_.DISPLAY_NAME);

    List<Metric<Meeting>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Meeting_.meetingPicklistValues, join -> join.get(MeetingCustomPicklistValue_.fieldId),
              MeetingCustomPicklistValue_.PICKLIST_VALUE_ID, fieldId),
          null));
    }};

    return new DimensionDetail<>(manyIdCustomFieldDimension, manyIdCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Meeting> createManyTextCustomFieldDimension(String dimensionName, long fieldId) {
    ManyTextCustomFieldDimension<Meeting, MeetingCustomTextValue> manyTextCustomFieldDimension = new ManyTextCustomFieldDimension<>(
        dimensionName,
        Meeting_.MEETING_CUSTOM_TEXT_VALUES,
        Meeting_.meetingCustomTextValues,
        join -> join.get(MeetingCustomTextValue_.value),
        join -> join.get(MeetingCustomTextValue_.fieldId),
        fieldId
    );

    List<Metric<Meeting>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Meeting_.meetingCustomTextValues, join -> join.get(MeetingCustomTextValue_.fieldId),
              MeetingCustomTextValue_.VALUE, fieldId),
          null));
    }};

    return new DimensionDetail<>(manyTextCustomFieldDimension, manyTextCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Meeting> createNumberCustomFieldFilterDimension(String dimensionName, long fieldId) {
    NumberCustomFieldFilterDimension<Meeting, MeetingCustomNumberValue> numberCustomFieldFilterDimension = new NumberCustomFieldFilterDimension<>(
        dimensionName,
        Meeting_.MEETING_CUSTOM_NUMBER_VALUES,
        Meeting_.meetingCustomNumberValues,
        join -> join.get(MeetingCustomNumberValue_.value),
        join -> join.get(MeetingCustomNumberValue_.fieldId), fieldId);

    List<Metric<Meeting>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Meeting_.meetingCustomNumberValues, join -> join.get(MeetingCustomNumberValue_.fieldId),
              MeetingCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.SUM,
          getBiFunction(MetricType.SUM, Meeting_.meetingCustomNumberValues, join -> join.get(MeetingCustomNumberValue_.fieldId),
              MeetingCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.AVERAGE,
          getBiFunction(MetricType.AVERAGE, Meeting_.meetingCustomNumberValues, join -> join.get(MeetingCustomNumberValue_.fieldId),
              MeetingCustomNumberValue_.VALUE, fieldId),
          null));
    }};

    return new DimensionDetail<>(null, numberCustomFieldFilterDimension, metrics);
  }

  public static DimensionDetail<Meeting> createDatePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Meeting, MeetingCustomDatePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Meeting_.MEETING_CUSTOM_DATE_PICKER_VALUES,
        Meeting_.meetingCustomDatePickerValues,
        join -> join.get(MeetingCustomDatePickerValue_.value),
        join -> join.get(MeetingCustomDatePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Meeting> createDatetimePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Meeting, MeetingCustomDatetimePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Meeting_.MEETING_CUSTOM_DATETIME_PICKER_VALUES,
        Meeting_.meetingCustomDatetimePickerValues,
        join -> join.get(MeetingCustomDatetimePickerValue_.value),
        join -> join.get(MeetingCustomDatetimePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  private static <T, V> BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>> getBiFunction(MetricType metricType,
      SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> getFieldId, String attributeName, long fieldId) {

    return (root, builder) -> CustomFieldMetricBuilder
        .buildExpressionByMetricType(metricType, root, builder, dimensionField, getFieldId,
            attributeName, fieldId);
  }

  public static DimensionDetail<Meeting> createBooleanCustomFieldDimension(String dimensionName, long fieldId) {
    ManyBooleanCustomFieldDimension<Meeting, MeetingCustomCheckboxValue> manyBooleanCustomFieldDimension = new ManyBooleanCustomFieldDimension<>(
        dimensionName,
        Meeting_.MEETING_CUSTOM_CHECKBOX_VALUES,
        Meeting_.meetingCustomCheckboxValues,
        join -> join.get(MeetingCustomCheckboxValue_.value),
        join -> join.get(MeetingCustomCheckboxValue_.fieldId),
        fieldId
    );
    return new DimensionDetail<>(manyBooleanCustomFieldDimension, manyBooleanCustomFieldDimension, Collections.emptyList());
  }
}
