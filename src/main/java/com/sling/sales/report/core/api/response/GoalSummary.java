package com.sling.sales.report.core.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoalSummary {

  private final long id;
  private final String name;
  private final Long defaultReportId;

  @JsonCreator
  public GoalSummary(@JsonProperty("id") long id, @JsonProperty("name") String name, @JsonProperty("defaultReportId") Long defaultReportId) {
    this.id = id;
    this.name = name;
    this.defaultReportId = defaultReportId;
  }
}
