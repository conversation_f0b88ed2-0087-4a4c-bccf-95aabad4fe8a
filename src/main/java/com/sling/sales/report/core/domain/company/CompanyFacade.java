package com.sling.sales.report.core.domain.company;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.core.domain.field.Field;
import com.sling.sales.report.core.domain.field.FieldFacade;
import com.sling.sales.report.core.domain.field.FieldType;
import com.sling.sales.report.dto.FieldDetail;
import com.sling.sales.report.error.JsonParseException;
import com.sling.sales.report.mq.event.CompanyDeletedEvent;
import com.sling.sales.report.mq.event.CompanyEvent;
import com.sling.sales.report.mq.event.DeleteRecordsEvent;
import com.sling.sales.report.mq.event.IdName;
import com.sling.sales.report.mq.event.PicklistValueDetail;
import com.sling.sales.report.mq.event.Revenue;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.UserFacade;
import java.lang.reflect.Type;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CompanyFacade {

  private final CompanyRepository companyRepository;
  private final CompanyPicklistValueRepository companyPicklistValueRepository;
  private final CompanyMultiPicklistValueRepository companyMultiPicklistValueRepository;
  private final FieldFacade fieldFacade;
  private final UserFacade userFacade;
  private final ObjectMapper objectMapper;

  @Autowired
  public CompanyFacade(CompanyRepository companyRepository,
      CompanyPicklistValueRepository companyPicklistValueRepository,
      CompanyMultiPicklistValueRepository companyMultiPicklistValueRepository, FieldFacade fieldFacade, UserFacade userFacade,
      ObjectMapper objectMapper) {
    this.companyRepository = companyRepository;
    this.companyPicklistValueRepository = companyPicklistValueRepository;
    this.companyMultiPicklistValueRepository = companyMultiPicklistValueRepository;
    this.fieldFacade = fieldFacade;
    this.userFacade = userFacade;
    this.objectMapper = objectMapper;
  }

  public Company createOrUpdateCompany(CompanyEvent companyEvent, Map<String, Object> eventPayload, boolean deleted, boolean isCreateEvent) {
    Company companyToBeCreatedOrUpdated = fromEventToCompanyToBeCreatedOrUpdated(companyEvent, eventPayload, deleted, isCreateEvent);
    return companyRepository.saveAndFlush(companyToBeCreatedOrUpdated);
  }

  private Company fromEventToCompanyToBeCreatedOrUpdated(CompanyEvent companyEvent, Map<String, Object> eventPayload,
      boolean deleted, boolean isCreateEvent) {

    Optional<Company> optionalCompany = getByTenantIdAndCompanyId(companyEvent.getTenantId(), companyEvent.getId());

    if (optionalCompany.isPresent() && (optionalCompany.get().isDeleted() || isCreateEvent ||
        companyEvent.getUpdatedAt().before(optionalCompany.get().getUpdatedAt()))) {
      return optionalCompany.get();
    }

    Map<String, Object> customFieldValues =
        companyEvent.getCustomFieldValues() == null ? Collections.emptyMap() : companyEvent.getCustomFieldValues();
    Map<FieldType, List<FieldDetail>> fieldDetails = getFieldDetailsByTenantId(companyEvent.getTenantId(), customFieldValues);

    Revenue revenueFromEvent = companyEvent.getAnnualRevenue();

    Revenue revenue = revenueFromEvent == null ? new Revenue(null, null) : revenueFromEvent;

    IdName industryFromEvent = companyEvent.getIndustry();
    PickListValue industry = industryFromEvent == null ? new PickListValue(null, null) : new PickListValue(industryFromEvent.getId(),
        industryFromEvent.getName());

    IdName numberOfEmployeesFromEvent = companyEvent.getNumberOfEmployees();
    PickListValue numberOfEmployees =
        numberOfEmployeesFromEvent == null ? new PickListValue(null, null) : new PickListValue(numberOfEmployeesFromEvent.getId(),
            numberOfEmployeesFromEvent.getName());

    IdName businessTypeFromEvent = companyEvent.getBusinessType();
    PickListValue businessType = businessTypeFromEvent == null ? new PickListValue(null, null) : new PickListValue(businessTypeFromEvent.getId(),
        businessTypeFromEvent.getName());

    IdName countryFromEvent = companyEvent.getCountry();
    PickListValue country = countryFromEvent == null ? new PickListValue(null, null) : new PickListValue(countryFromEvent.getId(),
        countryFromEvent.getName());

    User ownedBy = userFacade
        .getExistingOrCreateNewUser(new User(companyEvent.getOwnedBy().getId(), companyEvent.getTenantId(), companyEvent.getOwnedBy().getName()));
    User createdBy = userFacade
        .getExistingOrCreateNewUser(new User(companyEvent.getCreatedBy().getId(), companyEvent.getTenantId(), companyEvent.getCreatedBy().getName()));
    User updatedBy = userFacade
        .getExistingOrCreateNewUser(new User(companyEvent.getUpdatedBy().getId(), companyEvent.getTenantId(), companyEvent.getUpdatedBy().getName()));
    User importedBy;
    if (ObjectUtils.isNotEmpty(companyEvent.getImportedBy())) {
      importedBy = userFacade.getExistingOrCreateNewUser(new User(companyEvent.getImportedBy().getId(),
          companyEvent.getTenantId(), companyEvent.getImportedBy().getName()));
    } else {
      importedBy = null;
    }

    CompanyEventData companyEventData = new CompanyEventData();
    companyEventData.putAll(eventPayload);

    return optionalCompany
        .map(company -> {
          Company updatedCompany = company
              .update(companyEvent.getName(), revenue, industry, numberOfEmployees, businessType, country, companyEvent.getCity(),
                  companyEvent.getState(),
                  companyEvent.getUpdatedAt(),
                  companyEventData, ownedBy, updatedBy, deleted, companyEvent.getUniqueText1(), companyEvent.getUniqueText2());
          addCustomFieldValues(fieldDetails, updatedCompany);
          addPhoneNumbers(companyEvent.getPhoneNumbers(),updatedCompany);
          addEmails(companyEvent.getEmails(),updatedCompany);
          return updatedCompany;
        })
        .orElseGet(() -> {
          Company createdCompany = Company
              .create(companyEvent.getId(), companyEvent.getName(), companyEvent.getTenantId(), ownedBy, revenue, industry, numberOfEmployees,
                  businessType,
                  country,
                  companyEvent.getCity(), companyEvent.getState(), companyEvent.getCreatedAt(), companyEvent.getUpdatedAt(), companyEventData,
                  createdBy,
                  updatedBy, importedBy, deleted, companyEvent.getUniqueText1(), companyEvent.getUniqueText2());
          addCustomFieldValues(fieldDetails, createdCompany);
          addPhoneNumbers(companyEvent.getPhoneNumbers(),createdCompany);
          addEmails(companyEvent.getEmails(),createdCompany);
          return createdCompany;
        });

  }

  private void addPhoneNumbers(CompanyPhoneNumber[] companyPhoneNumbers, Company company) {
    if(ObjectUtils.isEmpty(companyPhoneNumbers)){
      company.addCompanyPhoneNumbers(Collections.emptySet());
      return;
    }
    Set<CompanyPhoneNumber> collect = Arrays.stream(companyPhoneNumbers)
        .map(companyPhoneNumber -> {
          CompanyPhoneNumber companyPhoneNumber1 = new CompanyPhoneNumber(companyPhoneNumber.getId(), companyPhoneNumber.getType(),
              companyPhoneNumber.getCode(), companyPhoneNumber.getValue(), companyPhoneNumber.getDialCode(), companyPhoneNumber.isPrimary());
          companyPhoneNumber1.setCompany(company);
          return companyPhoneNumber1;
        })
        .collect(Collectors.toSet());
    company.addCompanyPhoneNumbers(collect);
  }

  private void addEmails(CompanyEmail[] emails, Company company) {
    company.addCompanyEmails(Collections.emptySet());
    if(ObjectUtils.isEmpty(emails)){
      return;
    }
    Set<CompanyEmail> collect = Arrays.stream(emails)
        .map(email -> {
          CompanyEmail companyEmail = new CompanyEmail(email.getType(),
              email.getValue(),email.isPrimary());
          companyEmail.setCompany(company);
          return companyEmail;
        })
        .collect(Collectors.toSet());
    company.addCompanyEmails(collect);
  }
  private void addCustomFieldValues(Map<FieldType, List<FieldDetail>> fieldDetails, Company company) {
    company.addCompanyPicklistValues(getCompanyPicklistValues(fieldDetails, company));
    company.addCompanyCustomTextValues(getCompanyCustomTextValues(fieldDetails, company));
    company.addCompanyCustomNumberValues(getCompanyCustomNumberValues(fieldDetails, company));
    company.addCompanyCustomDatePickerValues(getCompanyCustomDatePickerValues(fieldDetails, company));
    company.addCompanyCustomDatetimePickerValues(getCompanyCustomDatetimePickerValues(fieldDetails, company));
    company.addCompanyCustomCheckboxValues(getCompanyCustomCheckboxValues(fieldDetails, company));
    company.addCompanyCustomMultiPicklistValues(getCompanyCustomMultiPicklistValues(fieldDetails, company));
  }

  private Map<FieldType, List<FieldDetail>> getFieldDetailsByTenantId(long tenantId, Map<String, Object> customFieldValues) {
    List<Field> fields = fieldFacade.getFieldsByTenantIdAndEntityType(tenantId, EntityType.COMPANY);
    return fields
        .stream()
        .filter(field -> customFieldValues.containsKey(field.getName()))
        .map(field -> new FieldDetail(field, customFieldValues.get(field.getName())))
        .collect(Collectors.groupingBy(fieldDetail -> fieldDetail.getField().getType()));
  }

  private Set<CompanyPicklistValue> getCompanyPicklistValues(Map<FieldType, List<FieldDetail>> fieldDetails, Company company) {
    if (!fieldDetails.containsKey(FieldType.PICK_LIST)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.PICK_LIST)
        .stream()
        .map(fieldDetail -> {
          IdName idName = new ObjectMapper().convertValue(fieldDetail.getValue(), IdName.class);
          return new CompanyPicklistValue(fieldDetail.getField().getId(), idName.getId(), idName.getName(), company);
        }).collect(Collectors.toSet());
  }

  private Set<CompanyCustomTextValue> getCompanyCustomTextValues(Map<FieldType, List<FieldDetail>> fieldDetails, Company company) {
    if (!fieldDetails.containsKey(FieldType.TEXT_FIELD)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.TEXT_FIELD)
        .stream()
        .map(fieldDetail -> {
          String valueToBeSaved = fieldDetail.getValue() == null ? null : String.valueOf(fieldDetail.getValue());
          return new CompanyCustomTextValue(fieldDetail.getField().getId(), valueToBeSaved, company);
        }).collect(Collectors.toSet());
  }

  private Set<CompanyCustomNumberValue> getCompanyCustomNumberValues(Map<FieldType, List<FieldDetail>> fieldDetails, Company company) {
    if (!fieldDetails.containsKey(FieldType.NUMBER)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.NUMBER)
        .stream()
        .map(fieldDetail -> {
          Double valueToBeSaved = fieldDetail.getValue() == null ? null : Double.parseDouble(String.valueOf(fieldDetail.getValue()));
          return new CompanyCustomNumberValue(fieldDetail.getField().getId(), valueToBeSaved, company);
        }).collect(Collectors.toSet());
  }

  private Set<CompanyCustomDatePickerValue> getCompanyCustomDatePickerValues(Map<FieldType, List<FieldDetail>> fieldDetails, Company company) {
    if (!fieldDetails.containsKey(FieldType.DATE_PICKER)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.DATE_PICKER)
        .stream()
        .map(fieldDetail -> getCompanyCustomDatePickerValue(company, fieldDetail))
        .collect(Collectors.toSet());
  }

  private Set<CompanyCustomDatetimePickerValue> getCompanyCustomDatetimePickerValues(Map<FieldType, List<FieldDetail>> fieldDetails,
      Company company) {
    if (!fieldDetails.containsKey(FieldType.DATETIME_PICKER)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.DATETIME_PICKER)
        .stream()
        .map(fieldDetail -> getCompanyCustomDatetimePickerValue(company, fieldDetail))
        .collect(Collectors.toSet());
  }

  private CompanyCustomDatePickerValue getCompanyCustomDatePickerValue(Company company, FieldDetail fieldDetail) {
    if (fieldDetail.getValue() == null) {
      return null;
    }
    try {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString()).toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new CompanyCustomDatePickerValue(fieldDetail.getField().getId(), valueToBeSaved, company);
    } catch (DateTimeParseException pe) {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString().substring(0, 23) + "Z").toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new CompanyCustomDatePickerValue(fieldDetail.getField().getId(), valueToBeSaved, company);
    }
  }

  private CompanyCustomDatetimePickerValue getCompanyCustomDatetimePickerValue(Company company, FieldDetail fieldDetail) {
    if (fieldDetail.getValue() == null) {
      return null;
    }
    try {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString()).toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new CompanyCustomDatetimePickerValue(fieldDetail.getField().getId(), valueToBeSaved, company);
    } catch (DateTimeParseException pe) {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString().substring(0, 23) + "Z").toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new CompanyCustomDatetimePickerValue(fieldDetail.getField().getId(), valueToBeSaved, company);
    }
  }

  private Set<CompanyCustomCheckboxValue> getCompanyCustomCheckboxValues(Map<FieldType, List<FieldDetail>> fieldDetails, Company company) {
    if (!fieldDetails.containsKey(FieldType.CHECKBOX)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.CHECKBOX)
        .stream()
        .map(fieldDetail -> {
          Boolean valueToBeSaved = fieldDetail.getValue() == null ? Boolean.FALSE : Boolean.parseBoolean(String.valueOf(fieldDetail.getValue()));
          return new CompanyCustomCheckboxValue(fieldDetail.getField().getId(), valueToBeSaved, company);
        }).collect(Collectors.toSet());
  }

  private Set<CompanyCustomMultiPicklistValue> getCompanyCustomMultiPicklistValues(Map<FieldType, List<FieldDetail>> fieldDetails, Company company) {
    if (!fieldDetails.containsKey(FieldType.MULTI_PICKLIST)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.MULTI_PICKLIST)
        .stream()
        .map(fieldDetail -> {
          List<IdName> idNames=  new ObjectMapper().convertValue(fieldDetail.getValue(),new TypeReference<>() {
            @Override
            public Type getType() {
              return super.getType();
            }
          });
          return idNames.stream()
              .map(idName -> new CompanyCustomMultiPicklistValue(fieldDetail.getField().getId(),company.getId(), idName.getId(), idName.getName(), company))
              .collect(Collectors.toSet());
        }).flatMap(Set::stream).collect(Collectors.toSet());
  }

  public void delete(CompanyDeletedEvent companyDeletedEvent) {
    getByTenantIdAndCompanyId(companyDeletedEvent.getTenantId(), companyDeletedEvent.getId())
        .ifPresentOrElse(companyRepository::delete, () -> createCompanyOnDeleteIfNotExists(companyDeletedEvent));
  }

  private void createCompanyOnDeleteIfNotExists(CompanyDeletedEvent companyDeletedEvent) {
    CompanyEvent companyEvent = new CompanyEvent();
    companyEvent.setId(companyDeletedEvent.getId());
    companyEvent.setTenantId(companyDeletedEvent.getTenantId());
    companyEvent.setName("deleted company");
    companyEvent.setOwnedBy(new IdName(companyDeletedEvent.getUserId(), "OwnedBy User"));
    companyEvent.setCreatedBy(new IdName(companyDeletedEvent.getUserId(), "CreatedBy User"));
    companyEvent.setUpdatedBy(new IdName(companyDeletedEvent.getUserId(), "UpdatedBy User"));
    companyEvent.setCreatedAt(Date.from(Instant.now()));
    companyEvent.setUpdatedAt(Date.from(Instant.now()));
    try {
      HashMap<String, Object> payload =
          objectMapper.readValue(
              objectMapper.writeValueAsString(companyEvent), new TypeReference<>() {
                @Override
                public Type getType() {
                  return super.getType();
                }
              });
      createOrUpdateCompany(companyEvent, payload, true, false);
    } catch (JsonProcessingException e) {
      throw new JsonParseException(e);
    }
  }

  public Optional<Company> getByTenantIdAndCompanyId(long tenantId, long companyId) {
    return companyRepository.findByTenantIdAndId(tenantId, companyId);
  }

  public void updateDisplayNameByPicklistValueId(PicklistValueDetail picklistValueDetail) {
    companyPicklistValueRepository.updateDisplayNameByPicklistValueId(picklistValueDetail.getId(), picklistValueDetail.getDisplayName());
  }

  public void updateMultiPicklistValueDisplayName(PicklistValueDetail picklistValueDetail) {
    companyMultiPicklistValueRepository.updateDisplayNameByPicklistValueId(picklistValueDetail.getId(), picklistValueDetail.getDisplayName());
  }


  @EventListener
  public void deleteSoftDeletedRecords(DeleteRecordsEvent deleteRecordsEvent) {
    companyRepository.deleteRecordsOlderThan10Days();
  }
}
