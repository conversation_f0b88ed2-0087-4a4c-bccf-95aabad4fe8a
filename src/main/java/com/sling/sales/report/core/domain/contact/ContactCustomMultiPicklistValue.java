package com.sling.sales.report.core.domain.contact;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor
public class ContactCustomMultiPicklistValue implements EntityDimension {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;
  private long fieldId;
  @Column(name = "contact_id", insertable = false, updatable = false)
  private long contactId;
  private Long picklistValueId;
  private String displayName;

  @ManyToOne
  @JoinColumn(name = "contact_id")
  @JsonIgnore
  private Contact contact;

  public ContactCustomMultiPicklistValue(long fieldId, long contactId, Long picklistValueId, String displayName, Contact contact) {
    this.fieldId = fieldId;
    this.contactId = contactId;
    this.picklistValueId = picklistValueId;
    this.displayName = displayName;
    this.contact = contact;
  }
}
