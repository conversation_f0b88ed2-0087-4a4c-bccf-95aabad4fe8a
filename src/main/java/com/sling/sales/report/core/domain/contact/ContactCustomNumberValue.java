package com.sling.sales.report.core.domain.contact;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor
public class ContactCustomNumberValue implements EntityDimension {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private long id;
  @Column(name = "field_id")
  private long fieldId;
  @Column(name = "value")
  private Double value;

  @ManyToOne
  @JoinColumn(name = "contact_id")
  @JsonIgnore
  private Contact contact;

  public ContactCustomNumberValue(long fieldId, Double value, Contact contact) {
    this.fieldId = fieldId;
    this.value = value;
    this.contact = contact;
  }
}
