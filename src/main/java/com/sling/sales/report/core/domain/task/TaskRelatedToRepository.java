package com.sling.sales.report.core.domain.task;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface TaskRelatedToRepository extends JpaRepository<TaskRelatedTo,Long> {
  @Transactional
  @Modifying
  @Query("UPDATE TaskRelatedTo SET entityName = :entityName WHERE tenantId = :TenantId AND entityId = :EntityId AND entityType = :EntityType")
  void updateEntityNameByEntityIdAndEntity(@Param("TenantId") long tenantId, @Param("EntityId") long entityId, @Param("EntityType") String entityType, @Param("entityName") String name);

  @Transactional
  @Modifying
  @Query("UPDATE TaskRelatedTo SET ownerId = :ownerId WHERE tenantId = :TenantId AND entityId = :entityId AND entityType = :entityType")
  void updateOwnerIdByEntityIdAndEntity(@Param("TenantId") long tenantId, @Param("entityId") long entityId, @Param("entityType") String entityType, @Param("ownerId") Long ownerId);

}
