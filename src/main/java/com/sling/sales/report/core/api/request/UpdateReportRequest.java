package com.sling.sales.report.core.api.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.mq.event.IdName;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateReportRequest {

  @NotBlank
  private final String name;
  private final String description;
  @NotBlank
  private final String reportType;
  private final String chartType;
  @Valid
  private final AggregationRequestV3 config;
  private final ReportCategory category;
  private final IdName goal;
  private final boolean prorated;
  private final Long currencyId;

  @JsonCreator
  public UpdateReportRequest(
      @JsonProperty("name") String name,
      @JsonProperty("description") String description,
      @JsonProperty("reportType") String reportType,
      @JsonProperty("chartType") String chartType,
      @JsonProperty("config") AggregationRequestV3 config,
      @JsonProperty("category") ReportCategory category,
      @JsonProperty("goal") IdName goal,
      @JsonProperty("prorated") boolean prorated,
      @JsonProperty("currencyId") Long currencyId) {
    this.name = name;
    this.description = description;
    this.reportType = reportType;
    this.chartType = chartType;
    this.config = config;
    this.category = category;
    this.goal = goal;
    this.prorated = prorated;
    this.currencyId = currencyId;
  }

}
