package com.sling.sales.report.core.domain.aggregation.fact;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.api.response.AggregateRecord;
import com.sling.sales.report.core.api.response.GoalAggregateRecord;
import com.sling.sales.report.core.api.response.GoalAggregateResponse;
import com.sling.sales.report.core.domain.aggregation.dimension.GoalDetail;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyTextDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Operator;
import com.sling.sales.report.core.domain.aggregation.exception.InsufficientEntityPrivilegesException;
import com.sling.sales.report.core.domain.aggregation.fact.field.AbstractFactFieldFactory;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFieldFactory;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import com.sling.sales.report.core.domain.call.Call;
import com.sling.sales.report.core.domain.call.Call_;
import com.sling.sales.report.core.domain.deal.Deal;
import com.sling.sales.report.core.domain.email.Email;
import com.sling.sales.report.core.domain.goal.GoalFieldValue;
import com.sling.sales.report.core.domain.meeting.Meeting;
import com.sling.sales.report.core.domain.sharing.ShareRule;
import com.sling.sales.report.core.domain.sharing.ShareRuleFacade;
import com.sling.sales.report.core.domain.sharing.SharedEntity;
import com.sling.sales.report.dto.ShareRuleUserDetail;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.UserService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class FactAggregationFacade<T extends Fact> {

  private final ShareRuleFacade shareRuleFacade;
  private final UserService userService;
  private final FactAggregationRepository factAggregationRepository;
  private final AbstractFactFieldFactory abstractFactFieldFactory;

  public FactAggregationFacade(ShareRuleFacade shareRuleFacade, UserService userService,
      FactAggregationRepository factAggregationRepository,
      AbstractFactFieldFactory abstractFactFieldFactory) {
    this.shareRuleFacade = shareRuleFacade;
    this.userService = userService;
    this.factAggregationRepository = factAggregationRepository;
    this.abstractFactFieldFactory = abstractFactFieldFactory;
  }

  public List<AggregateRecord> getAggregatedResults(Map<String, GroupByDimension<T>> groupByDimensions, List<FactFilter<T>> factFilters,
      List<GroupByField> groupBy,
      List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> customFieldMetrics, Class<T> clazz,
      FactAccessSpecifications<T> accessSpecifications, String timezone, Long currencyId) {
    var loggedInUser = userService.getLoggedInUser();
    if (clazz.equals(Call.class)) {
      return getCallAggregateRecords(groupByDimensions, factFilters, groupBy, metrics, customFieldMetrics, clazz, accessSpecifications, timezone,
          loggedInUser, currencyId);
    }
    if (clazz.equals(Meeting.class)) {
      return getMeetingAggregateRecords(groupByDimensions, factFilters, groupBy, metrics, customFieldMetrics, clazz, accessSpecifications, timezone,
          loggedInUser, currencyId);
    }
    if (clazz.equals(Email.class)) {
      return getEmailAggregateRecords(groupByDimensions, factFilters, groupBy, metrics, customFieldMetrics, clazz, accessSpecifications, timezone,
          loggedInUser, currencyId);
    }
    if (!loggedInUser.hasAccessToFacts(clazz)) {
      throw new InsufficientEntityPrivilegesException();
    }
    boolean canReadAllFact = loggedInUser.hasAccessToAllFacts(clazz);
    return getAggregateRecords(groupByDimensions, factFilters, groupBy, metrics, customFieldMetrics, clazz, accessSpecifications, timezone,
        loggedInUser, canReadAllFact, currencyId);
  }

  private List<AggregateRecord> getCallAggregateRecords(Map<String, GroupByDimension<T>> groupByDimensions, List<FactFilter<T>> factFilters,
      List<GroupByField> groupBy, List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> customFieldMetrics,
      Class<T> clazz, FactAccessSpecifications<T> accessSpecifications, String timezone, User loggedInUser, Long currencyId) {
    List<String> callEntities = loggedInUser.returnEntitiesHavingCallPermission();
    List<FactFilter<T>> localFactFilters = new ArrayList<>(factFilters);
    boolean hasAccessToCall = (loggedInUser.canReadCall() || loggedInUser.canReadAllCalls()) && !callEntities.isEmpty();
    if (!hasAccessToCall) {
      throw new InsufficientEntityPrivilegesException();
    }
    boolean canReadAllCall = loggedInUser.canReadAllCalls();
    return getAggregateRecords(groupByDimensions, localFactFilters, groupBy, metrics, customFieldMetrics, clazz, accessSpecifications, timezone,
        loggedInUser, canReadAllCall, currencyId);
  }

  private List<AggregateRecord> getEmailAggregateRecords(Map<String, GroupByDimension<T>> groupByDimensions, List<FactFilter<T>> factFilters,
      List<GroupByField> groupBy, List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> customFieldMetrics,
      Class<T> clazz, FactAccessSpecifications<T> accessSpecifications, String timezone, User loggedInUser, Long currencyId) {
    List<String> emailEntities = loggedInUser.returnEntitiesHavingEmailPermission();
    List<FactFilter<T>> localFactFilters = new ArrayList<>(factFilters);
    boolean hasAccessToEmail = (loggedInUser.canReadEmail() || loggedInUser.canReadAllEmails()) && !emailEntities.isEmpty();
    if (!hasAccessToEmail) {
      throw new InsufficientEntityPrivilegesException();
    }
    boolean canReadAllEmails = loggedInUser.canReadAllEmails();
    return getAggregateRecords(groupByDimensions, localFactFilters, groupBy, metrics, customFieldMetrics, clazz, accessSpecifications, timezone,
        loggedInUser, canReadAllEmails, currencyId);
  }

  private List<AggregateRecord> getMeetingAggregateRecords(Map<String, GroupByDimension<T>> groupByDimensions, List<FactFilter<T>> factFilters,
      List<GroupByField> groupBy, List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> customFieldMetrics,
      Class<T> clazz, FactAccessSpecifications<T> accessSpecifications, String timezone, User loggedInUser, Long currencyId) {

    List<FactFilter<T>> localFactFilters = new ArrayList<>(factFilters);
    boolean canReadAllMeeting = loggedInUser.canReadAllMeetings();
    return getAggregateRecords(groupByDimensions, localFactFilters, groupBy, metrics, customFieldMetrics, clazz, accessSpecifications, timezone,
        loggedInUser, canReadAllMeeting, currencyId);
  }

  private List<AggregateRecord> getAggregateRecords(Map<String, GroupByDimension<T>> groupByDimensions, List<FactFilter<T>> factFilters,
      List<GroupByField> groupBy, List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> customFieldMetrics,
      Class<T> clazz, FactAccessSpecifications<T> accessSpecifications, String timezone, User loggedInUser, boolean canReadAllFact, Long currencyId) {

    var privilegeBasedSpecifications = getSpecificationsBasedOnReadPrivileges(loggedInUser, SharedEntity.fromFact(clazz),
        accessSpecifications, canReadAllFact);

    return getAggregateRecords(groupByDimensions, factFilters,
        groupBy, metrics, customFieldMetrics, clazz, timezone, privilegeBasedSpecifications, currencyId);
  }

  private List<AggregateRecord> getAggregateRecords(Map<String, GroupByDimension<T>> groupByDimensions, List<FactFilter<T>> factFilters,
      List<GroupByField> groupBy, List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> customFieldMetrics,
      Class<T> clazz, String timezone, Specification<T> privilegeBasedSpecifications, Long currencyId) {
    FactFieldFactory<T> fieldFactory = abstractFactFieldFactory.getFieldFactory(clazz);

    List<GroupByDimension<T>> groupByFields = fieldFactory.getGroupByFields(groupBy, groupByDimensions);

    boolean isDealProductsPresent =
        clazz.equals(Deal.class) && groupBy.stream().anyMatch(groupByField -> groupByField.getName().equals("products"));

    List<BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>>> metricSelectors = fieldFactory
        .getMetricSelector(isDealProductsPresent, metrics, customFieldMetrics, clazz, currencyId);

    Specification<T> specifications = getSpecification(factFilters, privilegeBasedSpecifications, timezone);
    return factAggregationRepository.groupByMetric(specifications, groupByFields, metricSelectors, clazz, timezone);
  }

  private Specification<T> getSpecification(List<FactFilter<T>> factFilters, Specification<T> privilegeBasedSpecifications, String timezone) {
    return factFilters.stream()
        .map(factFilter -> toSpecification(factFilter))
        .reduce(privilegeBasedSpecifications, Specification::and);
  }

  public List<AggregateRecord> getAggregationResult(List<FactFilter<T>> factFilters,
      List<GroupByDimension<T>> groupByDimensions, List<Metric> metrics,
      List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> customFieldMetrics, FactAccessSpecifications<T> accessSpecifications,
      Class<T> clazz, String timezone, Long currencyId) {
    var loggedInUser = userService.getLoggedInUser();
    if (clazz.equals(Call.class)) {
      return getCallAggregationResult(factFilters, groupByDimensions, metrics, customFieldMetrics, accessSpecifications, clazz, timezone,
          loggedInUser, currencyId);
    }
    if (!loggedInUser.hasAccessToFacts(clazz)) {
      throw new InsufficientEntityPrivilegesException();
    }
    boolean canReadAllFact = loggedInUser.hasAccessToAllFacts(clazz);
    return getAggregationResult(factFilters, groupByDimensions,
        metrics, customFieldMetrics, accessSpecifications, clazz, timezone, loggedInUser, canReadAllFact, currencyId);
  }

  private List<AggregateRecord> getCallAggregationResult(List<FactFilter<T>> factFilters, List<GroupByDimension<T>> groupByDimensions,
      List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> customFieldMetrics,
      FactAccessSpecifications<T> accessSpecifications, Class<T> clazz, String timezone, User loggedInUser, Long currencyId) {
    List<String> callEntities = loggedInUser.returnEntitiesHavingCallPermission();
    List<FactFilter<T>> localFactFilters = new ArrayList<>(factFilters);
    boolean hasAccessToCall = (loggedInUser.canReadCall() || loggedInUser.canReadAllCalls()) && !callEntities.isEmpty();
    if (!hasAccessToCall) {
      throw new InsufficientEntityPrivilegesException();
    }
    addCallEntityFilter(factFilters, timezone, callEntities,
        localFactFilters);
    boolean canReadAllCall = loggedInUser.canReadAllCalls() && !callEntities.isEmpty();
    return getAggregationResult(localFactFilters, groupByDimensions,
        metrics, customFieldMetrics, accessSpecifications, clazz, timezone, loggedInUser, canReadAllCall, currencyId);
  }

  private void addCallEntityFilter(List<FactFilter<T>> factFilters, String timezone, List<String> callEntities,
      List<FactFilter<T>> localFactFilters) {
    ManyTextDimension<Call> entities = new ManyTextDimension<>(
        "entities",
        Call_.ENTITIES,
        Call_.entities
    );
    List<String> formattedEntities = callEntities
        .stream()
        .map(entity -> StringUtils.capitalize(entity.toLowerCase()).trim())
        .collect(Collectors.toList());
    boolean isEntitiesFilterNotPresent = factFilters
        .stream()
        .noneMatch(factFilter -> factFilter.getDimension().getName().equals("entities"));
    if (isEntitiesFilterNotPresent) {
      var factFilter = new FactFilter<>(
          Operator.in,
          entities,
          new Filter("in", "entities", "entities", "string", formattedEntities, null, null, "LOOK_UP", null, null),
          timezone);
      localFactFilters.add((FactFilter<T>) factFilter);
    }
  }

  private List<AggregateRecord> getAggregationResult(List<FactFilter<T>> factFilters, List<GroupByDimension<T>> groupByDimensions,
      List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> customFieldMetrics,
      FactAccessSpecifications<T> accessSpecifications, Class<T> clazz, String timezone, User loggedInUser, boolean canReadAllFact, Long currencyId) {

    FactFieldFactory<T> fieldFactory = abstractFactFieldFactory.getFieldFactory(clazz);

    var privilegeBasedSpecifications = getSpecificationsBasedOnReadPrivileges(loggedInUser, SharedEntity.fromFact(clazz),
        accessSpecifications, canReadAllFact);

    boolean isDealProductsPresent =
        clazz.equals(Deal.class) && groupByDimensions.stream().anyMatch(groupByDimension -> groupByDimension.getName().equals("products"));

    List<BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>>> metricSelectors = fieldFactory
        .getMetricSelector(isDealProductsPresent, metrics, customFieldMetrics, clazz, currencyId);

    Specification<T> specifications = getSpecification(factFilters, privilegeBasedSpecifications, timezone);

    return factAggregationRepository
        .getAggregateResult(specifications, groupByDimensions, metricSelectors, clazz, timezone);
  }

  private Specification<T> toSpecification(FactFilter<T> filter) {
    return filter.getDimension().toSpecification(filter.getOperator(), filter);
  }

  private Specification<T> getSpecificationsBasedOnReadPrivileges(User reader, SharedEntity sharedEntity,
      FactAccessSpecifications<T> accessSpecifications, boolean canReadAllFact) {
    var deletedFalse = accessSpecifications.isDeletedFalse();
    if (canReadAllFact) {
      log.info("User:{} with tenant:{} can read all facts", reader.getId(), reader.getTenantId());
      var tenantSpecification = accessSpecifications.belongsToTenant(reader.getTenantId());
      return ObjectUtils.isEmpty(deletedFalse) ? tenantSpecification : deletedFalse.and(tenantSpecification);
    }

    log.info("get Share rule specification by tenantId {} and userId {} Start", reader.getTenantId(), reader.getId());
    var specificationsBasedOnShareRules = getSpecificationsBasedOnShareRules(reader, sharedEntity, accessSpecifications);

    log.info("get Share rule specification by tenantId {} and userId {} End", reader.getTenantId(), reader.getId());
    var taskAssignToSpec = getTaskAssignToSpec(reader, accessSpecifications, sharedEntity);

    if(ObjectUtils.isNotEmpty(taskAssignToSpec)){
      var specifications = (Objects.requireNonNull(taskAssignToSpec.or(specificationsBasedOnShareRules)).and(
          accessSpecifications.belongsToTenant(reader.getTenantId())));
      return ObjectUtils.isEmpty(deletedFalse) ? specifications : deletedFalse.and(specifications);
    }

    var specifications = (Objects.requireNonNull(specificationsBasedOnShareRules)).and(
        accessSpecifications.belongsToTenant(reader.getTenantId()));
    return ObjectUtils.isEmpty(deletedFalse) ? specifications : deletedFalse.and(specifications);
  }

  private Specification<T> getTaskAssignToSpec(User reader, FactAccessSpecifications<T> accessSpecifications, SharedEntity sharedEntity) {
    if (sharedEntity == SharedEntity.TASK) {
      return accessSpecifications.ownedByWithTaskAssignedTo(reader.getId());
    }
    /*return accessSpecifications.ownedBy(reader.getId());*/
    return null;
  }

  private Specification<T> getSpecificationsBasedOnShareRules(User reader, SharedEntity sharedEntity,
      FactAccessSpecifications<T> accessSpecifications) {
    log.info("Get share rule for entity and owner for tenantId {} start", reader.getTenantId());
    return specificationBySharedEntity(reader, sharedEntity, accessSpecifications);
  }

  private Specification<T> specificationBySharedEntity(User reader, SharedEntity sharedEntity, FactAccessSpecifications<T> accessSpecifications) {
    if (sharedEntity == SharedEntity.CALL) {
      return toCallSpecification(reader, accessSpecifications);
    }
    if (sharedEntity == SharedEntity.MEETING) {
      Specification<T> meetingSpecificationByOtherEntityShareRules = toMeetingSpecification(reader, accessSpecifications);
      List<Long> sharedMeetingIds = shareRuleFacade.getRulesEntityIdApplicableToUser(reader, sharedEntity);
      List<Long> ownerIds = shareRuleFacade.getRulesEntityOwnerIdApplicableToUser(reader, sharedEntity);
      ownerIds.add(reader.getId());
      return Objects.requireNonNull(
          accessSpecifications.haveIdsIn(sharedMeetingIds)
              .or(accessSpecifications.haveFactOwnersIn(ownerIds)
                  .or(accessSpecifications.haveEntityEqualToAndIdsInForMeeting("user", ownerIds)
                      .or(meetingSpecificationByOtherEntityShareRules))));
    }
    if (sharedEntity == SharedEntity.EMAIL) {
      return toEmailSpecification(reader, accessSpecifications);
    }

    Specification<T> specifications = accessSpecifications.isDeletedFalse();

    List<Long> sharedLeadIds = shareRuleFacade.getRulesEntityIdApplicableToUser(reader, sharedEntity);
    List<Long> ownerIds = shareRuleFacade.getRulesEntityOwnerIdApplicableToUser(reader, sharedEntity);
    ownerIds.add(reader.getId());
    Specification<T> entitySpec = accessSpecifications.haveIdsIn(sharedLeadIds);
    Specification<T> ownerSpec = accessSpecifications.haveFactOwnersIn(ownerIds);
    log.info("Get share rule for entity and owner for tenantId {} end", reader.getTenantId());

    if (ObjectUtils.isNotEmpty(sharedLeadIds) && ObjectUtils.isNotEmpty(ownerIds)) {
      return specifications.and(entitySpec.or(ownerSpec));
    }
    if (ObjectUtils.isNotEmpty(sharedLeadIds)){
      return specifications.and(entitySpec);
    }
    if(ObjectUtils.isNotEmpty(ownerIds)){
      return specifications.and(ownerSpec);
    }
    return specifications;
  }

  private Specification<T> toCallSpecification(User reader, FactAccessSpecifications<T> accessSpecifications) {
    List<ShareRuleUserDetail> sharedEntities = shareRuleFacade.getCallRulesEntityIdApplicableToUser(reader);
    List<ShareRuleUserDetail> sharedByOwners = shareRuleFacade.getCallRulesEntityOwnerIdApplicableToUser(reader);

    var loggedInUserSpecification = accessSpecifications.haveLoggedInUserEqualTo(reader.getId());

    Specification<T> entityOwnerSpecification = toCallEntityOwnerSpecification(accessSpecifications, sharedByOwners,
        loggedInUserSpecification);

    Specification<T> entityIdSpecification = toCallEntityIdSpecification(accessSpecifications, sharedEntities,
        entityOwnerSpecification);

    return ObjectUtils.isEmpty(accessSpecifications.isDeletedFalse()) ? entityIdSpecification
        : accessSpecifications.isDeletedFalse().and(entityIdSpecification);

  }

  private Specification<T> toEmailSpecification(User reader, FactAccessSpecifications<T> accessSpecifications) {
    List<ShareRuleUserDetail> sharedEntities = shareRuleFacade.getEmailRulesEntityIdApplicableToUser(reader);
    List<ShareRuleUserDetail> sharedByOwners = shareRuleFacade.getEmailRulesEntityOwnerIdApplicableToUser(reader);

    var loggedInUserSpecification = accessSpecifications.haveLoggedInUserEqualToForEmail(reader.getId());

    Specification<T> entityOwnerSpecification = toEmailOwnerSpecification(accessSpecifications, sharedByOwners,
        loggedInUserSpecification);

    Specification<T> entityIdSpecification = toEmailEntityIdSpecification(accessSpecifications, sharedEntities,
        entityOwnerSpecification);

    return ObjectUtils.isEmpty(accessSpecifications.isDeletedFalse()) ? entityIdSpecification
        : accessSpecifications.isDeletedFalse().and(entityIdSpecification);

  }

  private Specification<T> toMeetingSpecification(User reader, FactAccessSpecifications<T> accessSpecifications) {

    List<ShareRuleUserDetail> sharedEntities = shareRuleFacade.getShareRuleForEntitiesWhereActionIsMeetingByEntityId(reader);

    List<ShareRuleUserDetail> sharedByOwners = shareRuleFacade.getShareRuleForEntitiesWhereActionIsMeetingByOwnerId(reader);

    var loggedInUserSpecification = accessSpecifications.haveLoggedInUserEqualToForMeeting(reader.getId())
        .or(accessSpecifications.haveEntityEqualToAndIdsInForMeeting("user", Collections.singletonList(reader.getId())));

    Specification<T> entityOwnerSpecification = toMeetingEntityOwnerSpecification(accessSpecifications, sharedByOwners,
        loggedInUserSpecification);

    Specification<T> entityIdSpecification = toMeetingEntityIdSpecification(accessSpecifications, sharedEntities,
        entityOwnerSpecification);

    return entityIdSpecification;
  }

  private Specification<T> toMeetingEntityOwnerSpecification(FactAccessSpecifications<T> accessSpecifications,
      List<ShareRuleUserDetail> sharedByOwners, Specification<T> loggedInUserSpecification) {
    User loggedInUser = userService.getLoggedInUser();
    Map<String, List<ShareRuleUserDetail>> entityWiseOwners = sharedByOwners
        .stream()
        .filter(shareRuleUserDetail -> loggedInUser.hasAccessToFacts(shareRuleUserDetail.getSharedEntity()))
        .collect(Collectors.groupingBy(ShareRuleUserDetail::getSharedEntity));

    return entityWiseOwners
        .entrySet()
        .stream()
        .map(entry -> {
          Set<Long> ownerIds = entry.getValue().stream()
              .map(ShareRuleUserDetail::getOwnerId)
              .collect(Collectors.toSet());
          return accessSpecifications.haveEntityEqualToAndOwnerIdsInForMeeting(entry.getKey(), new ArrayList<>(ownerIds));
        }).reduce(loggedInUserSpecification, Specification::or);
  }

  private Specification<T> toMeetingEntityIdSpecification(FactAccessSpecifications<T> accessSpecifications,
      List<ShareRuleUserDetail> sharedEntities, Specification<T> entityOwnerSpecification) {
    User loggedInUser = userService.getLoggedInUser();
    Map<String, List<ShareRuleUserDetail>> entityWiseEntityIds = sharedEntities
        .stream()
        .filter(shareRuleUserDetail -> loggedInUser.hasAccessToFacts(shareRuleUserDetail.getSharedEntity()))
        .collect(Collectors.groupingBy(ShareRuleUserDetail::getSharedEntity));

    return entityWiseEntityIds.entrySet()
        .stream()
        .map(entry -> {
          Set<Long> entityIds = entry.getValue().stream()
              .map(ShareRuleUserDetail::getSharedEntityId)
              .collect(Collectors.toSet());
          return accessSpecifications.haveEntityEqualToAndEntityIdsInForMeeting(entry.getKey(), new ArrayList<>(entityIds));
        }).reduce(entityOwnerSpecification, Specification::or);
  }

  private Specification<T> toCallEntityIdSpecification(FactAccessSpecifications<T> accessSpecifications,
      List<ShareRuleUserDetail> sharedEntities, Specification<T> entityOwnerSpecification) {
    Map<String, List<ShareRuleUserDetail>> entityWiseEntityIds = sharedEntities
        .stream()
        .collect(Collectors.groupingBy(ShareRuleUserDetail::getSharedEntity));

    return entityWiseEntityIds.entrySet()
        .stream()
        .map(entry -> {
          Set<Long> entityIds = entry.getValue().stream()
              .map(ShareRuleUserDetail::getSharedEntityId)
              .collect(Collectors.toSet());
          return accessSpecifications.haveEntityEqualToAndEntityIdsIn(entry.getKey(), new ArrayList<>(entityIds));
        }).reduce(entityOwnerSpecification, Specification::or);
  }

  private Specification<T> toCallEntityOwnerSpecification(FactAccessSpecifications<T> accessSpecifications,
      List<ShareRuleUserDetail> sharedByOwners, Specification<T> loggedInUserSpecification) {
    Map<String, List<ShareRuleUserDetail>> entityWiseOwners = sharedByOwners
        .stream()
        .collect(Collectors.groupingBy(ShareRuleUserDetail::getSharedEntity));

    return entityWiseOwners
        .entrySet()
        .stream()
        .map(entry -> {
          Set<Long> ownerIds = entry.getValue().stream()
              .map(ShareRuleUserDetail::getOwnerId)
              .collect(Collectors.toSet());
          return accessSpecifications.haveEntityEqualToAndOwnerIdsIn(entry.getKey(), new ArrayList<>(ownerIds));
        }).reduce(loggedInUserSpecification, Specification::or);
  }

  private Specification<T> toEmailOwnerSpecification(FactAccessSpecifications<T> accessSpecifications,
      List<ShareRuleUserDetail> sharedByOwners, Specification<T> loggedInUserSpecification) {
    Map<String, List<ShareRuleUserDetail>> entityWiseOwners = sharedByOwners
        .stream()
        .collect(Collectors.groupingBy(ShareRuleUserDetail::getSharedEntity));

    return entityWiseOwners
        .entrySet()
        .stream()
        .map(entry -> {
          Set<Long> ownerIds = entry.getValue().stream()
              .map(ShareRuleUserDetail::getOwnerId)
              .collect(Collectors.toSet());
          return accessSpecifications.haveEntityEqualToAndOwnerIdsInForEmail(entry.getKey(), new ArrayList<>(ownerIds));
        }).reduce(loggedInUserSpecification, Specification::or);
  }

  private Specification<T> toEmailEntityIdSpecification(FactAccessSpecifications<T> accessSpecifications,
      List<ShareRuleUserDetail> sharedEntities, Specification<T> entityOwnerSpecification) {
    Map<String, List<ShareRuleUserDetail>> entityWiseEntityIds = sharedEntities
        .stream()
        .collect(Collectors.groupingBy(ShareRuleUserDetail::getSharedEntity));

    return entityWiseEntityIds.entrySet()
        .stream()
        .map(entry -> {
          Set<Long> entityIds = entry.getValue().stream()
              .map(ShareRuleUserDetail::getSharedEntityId)
              .collect(Collectors.toSet());
          return accessSpecifications.haveEntityEqualToAndEntityIdsInForEmail(entry.getKey(), new ArrayList<>(entityIds));
        }).reduce(entityOwnerSpecification, Specification::or);
  }

  private Predicate<ShareRule> byRulesForSpecificLeadShares() {
    return rule -> rule.getSharedEntityId() != null;
  }

  private Predicate<ShareRule> ByRulesForAllLeads() {
    return rule -> rule.getSharedEntityId() == null;
  }

  public GoalAggregateResponse getGoalAggregateRecords(Map<String, GroupByDimension<T>> groupByDimensions, List<FactFilter<T>> factFilters,
      List<GroupByField> groupBy, List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> customFieldMetrics,
      Class<T> clazz, FactAccessSpecifications<T> accessSpecifications, String timezone, GoalDetail goalDetail, User loggedInUser,
      Set<GoalFieldValue> goalFieldValues, Long currencyId) {
    if (clazz.equals(Call.class)) {
      return getCallGoalAggregateRecords(groupByDimensions, factFilters, groupBy, metrics, customFieldMetrics, clazz, accessSpecifications,
          timezone, goalDetail, goalFieldValues, currencyId, loggedInUser);
    }

    if (!loggedInUser.hasAccessToFacts(clazz)) {
      throw new InsufficientEntityPrivilegesException();
    }
    boolean canReadAllFact = loggedInUser.hasAccessToAllFacts(clazz);
    return getGoalAggregateRecords(loggedInUser, clazz, groupByDimensions, factFilters, groupBy, metrics, customFieldMetrics, timezone, goalDetail,
        goalFieldValues, currencyId, accessSpecifications, canReadAllFact);
  }

  private GoalAggregateResponse getCallGoalAggregateRecords(Map<String, GroupByDimension<T>> groupByDimensions, List<FactFilter<T>> factFilters,
      List<GroupByField> groupBy, List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> customFieldMetrics,
      Class<T> clazz, FactAccessSpecifications<T> accessSpecifications, String timezone, GoalDetail goalDetail, Set<GoalFieldValue> goalFieldValues, Long currencyId,
      User loggedInUser) {
    List<String> callEntities = loggedInUser.returnEntitiesHavingCallPermission();
    boolean hasAccessToCall = (loggedInUser.canReadCall() || loggedInUser.canReadAllCalls()) && !callEntities.isEmpty();
    if (!hasAccessToCall) {
      throw new InsufficientEntityPrivilegesException();
    }
    boolean canReadAllCall = loggedInUser.canReadAllCalls();

    return getGoalAggregateRecords(loggedInUser, clazz, groupByDimensions, factFilters, groupBy, metrics, customFieldMetrics, timezone,
        goalDetail, goalFieldValues, currencyId, accessSpecifications, canReadAllCall);
  }

  private GoalAggregateResponse getGoalAggregateRecords(User loggedInUser, Class<T> clazz, Map<String, GroupByDimension<T>> groupByDimensions,
      List<FactFilter<T>> factFilters, List<GroupByField> groupBy, List<Metric> metrics,
      List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> customFieldMetrics, String timezone, GoalDetail goalDetail,
      Set<GoalFieldValue> goalFieldValues, Long currencyId, FactAccessSpecifications<T> accessSpecifications,
      boolean canReadAllFact) {
    var privilegeBasedSpecifications = getSpecificationsBasedOnReadPrivileges(loggedInUser, SharedEntity.fromFact(clazz),
        accessSpecifications, canReadAllFact);

    GoalAggregateResponse goalAggregateResponse = getGoalAggregateRecordResponse(groupByDimensions, factFilters, groupBy, metrics, customFieldMetrics, clazz, goalDetail,
        goalFieldValues, timezone, currencyId, privilegeBasedSpecifications);

    if(shouldFilterAggregateRecords(loggedInUser, goalDetail)) {
      List<GoalAggregateRecord> individualAggregateRecord = goalAggregateResponse.getResult().stream().filter(goalAggregateRecord -> goalAggregateRecord.getId().equals(loggedInUser.getId()))
          .collect(Collectors.toList());
      return new GoalAggregateResponse(individualAggregateRecord, goalAggregateResponse.getGoalSummary());
    }
    return goalAggregateResponse;
  }

  private GoalAggregateResponse getGoalAggregateRecordResponse(Map<String, GroupByDimension<T>> groupByDimensions, List<FactFilter<T>> factFilters,
      List<GroupByField> groupBy, List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<T>> customFieldMetrics,
      Class<T> clazz, GoalDetail goalDetail, Set<GoalFieldValue> goalFieldValues, String timezone, Long currencyId,
      Specification<T> privilegeBasedSpecifications) {

    FactFieldFactory<T> fieldFactory = abstractFactFieldFactory.getFieldFactory(clazz);
    List<GroupByDimension<T>> groupByFields = fieldFactory.getGroupByFields(groupBy, groupByDimensions);
    List<BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>>> metricSelectors = fieldFactory
        .getMetricSelector(false, metrics, customFieldMetrics, clazz, currencyId);
    Specification<T> specifications = getSpecification(factFilters, privilegeBasedSpecifications, timezone);
    String dimensionName = groupBy.get(0).getName();
    FactFilter<T> dimensionAsFilter = factFilters.stream()
        .filter(factFilter -> {
          var dimension = factFilter.getDimension();
          return dimension.getName().equals(dimensionName);
        })
        .findFirst()
        .orElse(null);

    return factAggregationRepository.groupByGoalMetric(specifications, groupByFields, metricSelectors, clazz, timezone, goalDetail, goalFieldValues,
        dimensionAsFilter, metrics);
  }

  private boolean shouldFilterAggregateRecords(User loggedInUser, GoalDetail goalDetail) {
    boolean isNotCreator = !goalDetail.getCreatedBy().getId().equals(loggedInUser.getId());
    boolean isNotOwner = goalDetail.getOwners().stream()
        .noneMatch(owner -> owner.getId().equals(loggedInUser.getId()));
    boolean cannotQueryAllGoals = !loggedInUser.canQueryAllGoals();
    boolean hideOthersProgress = !goalDetail.isShowOthersProgress();

    return hideOthersProgress && cannotQueryAllGoals && isNotCreator && isNotOwner;
  }

}
