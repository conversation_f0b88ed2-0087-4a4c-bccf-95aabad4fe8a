package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.in;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_in;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.util.Arrays;
import java.util.List;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.ListAttribute;
import org.springframework.data.jpa.domain.Specification;

public class ManyTextDimension<T extends Fact> implements FilterDimension<T> {

  private final List<Operator> allowedOperators = asList(equal, not_equal, is_null, is_not_null, in, not_in);
  private final String name;
  private final String dimensionName;
  private final ListAttribute<T, String> dimensionField;

  public ManyTextDimension(
      String name,
      String dimensionName,
      ListAttribute<T, String> dimensionField) {
    this.name = name;
    this.dimensionName = dimensionName;
    this.dimensionField = dimensionField;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    switch (operator) {
      case equal:
        return hasText(String.valueOf(value));
      case not_equal:
        return doesNotHaveText(String.valueOf(value)).or(isNull());
      case is_null:
        return isNull();
      case is_not_null:
        return isNotNull();
      case in:
        return in(getInParameters(value));
      case not_in:
        return notIn(getInParameters(value)).or(isNull());
      default:
        return null;
    }
  }

  private Specification<T> isNotNull() {
    return ((root, query, builder) -> builder.isNotNull(getOrCreateLeftJoin(root)));
  }

  private Specification<T> hasText(String value) {
    return ((root, query, builder) -> builder.equal(getOrCreateLeftJoin(root), value));
  }

  Specification<T> doesNotHaveText(String value) {
    return ((root, query, builder) -> builder.notEqual(getOrCreateLeftJoin(root), value));
  }

  Specification<T> isNull() {
    return ((root, query, builder) -> builder.isNull(getOrCreateLeftJoin(root)));
  }

  private List<String> getInParameters(Object value) {
    if (value instanceof List) {
      return (List<String>) value;
    }
    return Arrays.stream(String.valueOf(value).split(","))
        .map(String::trim)
        .collect(toList());
  }

  private Specification<T> in(List<String> values) {
    return ((root, query, builder) -> {
      Expression<String> jsonbExpression = getOrCreateLeftJoin(root);
      return jsonbExpression.in(values);
    });
  }

  private Specification<T> notIn(List<String> values) {
    return ((root, query, builder) -> {
      Expression<String> jsonbExpression = getOrCreateLeftJoin(root);
      return jsonbExpression.in(values).not();
    });
  }

  private Join<T, String> getOrCreateLeftJoin(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<T, String>) j))
        .findFirst()
        .orElseGet(() -> root.join(dimensionField, JoinType.LEFT));
  }
}
