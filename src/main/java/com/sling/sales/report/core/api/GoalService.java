package com.sling.sales.report.core.api;

import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;

import com.sling.sales.report.core.api.request.AggregationRequestV3;
import com.sling.sales.report.core.api.request.AggregationRequestV3.ColorCode;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.CreateGoalRequest;
import com.sling.sales.report.core.api.request.FilterRequest;
import com.sling.sales.report.core.api.request.GoalAggregationRequest;
import com.sling.sales.report.core.api.request.GoalSummaryRequest;
import com.sling.sales.report.core.api.request.GoalSummaryRequestDetail;
import com.sling.sales.report.core.api.request.NudgeUserRequest;
import com.sling.sales.report.core.api.request.UpdateGoalRequest;
import com.sling.sales.report.core.api.response.Action;
import com.sling.sales.report.core.api.response.GoalAchievementResponse;
import com.sling.sales.report.core.api.response.GoalAchievementResponse.Achievement;
import com.sling.sales.report.core.api.response.GoalAggregateRecord;
import com.sling.sales.report.core.api.response.GoalAggregateResponse;
import com.sling.sales.report.core.api.response.GoalCountSummary;
import com.sling.sales.report.core.api.response.GoalResponse;
import com.sling.sales.report.core.api.response.GoalSummary;
import com.sling.sales.report.core.api.response.IdName;
import com.sling.sales.report.core.api.response.ReportResponse;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.GoalDetail;
import com.sling.sales.report.core.domain.aggregation.dimension.Operator;
import com.sling.sales.report.core.domain.aggregation.exception.InsufficientGoalPrivilegesException;
import com.sling.sales.report.core.domain.aggregation.exception.InvalidFactForReporting;
import com.sling.sales.report.core.domain.aggregation.exception.UnsupportedFilterException;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.AbstractFactFieldFactory;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import com.sling.sales.report.core.domain.call.Call;
import com.sling.sales.report.core.domain.call.CallAggregationFacade;
import com.sling.sales.report.core.domain.deal.Deal;
import com.sling.sales.report.core.domain.deal.DealAggregationFacade;
import com.sling.sales.report.core.domain.goal.Frequency;
import com.sling.sales.report.core.domain.goal.Goal;
import com.sling.sales.report.core.domain.goal.GoalFacade;
import com.sling.sales.report.core.domain.goal.GoalFieldType;
import com.sling.sales.report.core.domain.lead.Lead;
import com.sling.sales.report.core.domain.lead.LeadAggregationFacade;
import com.sling.sales.report.core.domain.meeting.Meeting;
import com.sling.sales.report.core.domain.meeting.MeetingAggregationFacade;
import com.sling.sales.report.core.domain.report.Report;
import com.sling.sales.report.core.domain.report.exception.InvalidReportRequestException;
import com.sling.sales.report.core.domain.service.CustomFieldService;
import com.sling.sales.report.dto.DimensionDetail;
import com.sling.sales.report.dto.GoalDimension;
import com.sling.sales.report.dto.GoalFieldValue;
import com.sling.sales.report.dto.GoalFilter;
import com.sling.sales.report.dto.GoalFrequency;
import com.sling.sales.report.dto.GoalMetric;
import com.sling.sales.report.error.ErrorCode;
import com.sling.sales.report.error.InvalidPipelineOperatorException;
import com.sling.sales.report.error.ResourceNotFoundException;
import com.sling.sales.report.mq.event.MeetingLookup;
import com.sling.sales.report.mq.event.UsageLimitChangedEvent;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.UserFacade;
import com.sling.sales.report.security.domain.UserService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class GoalService {

  private final GoalFacade goalFacade;
  private final UserService userService;
  private final AbstractFactFieldFactory abstractFactFieldFactory;
  private final LeadAggregationFacade leadAggregationFacade;
  private final DealAggregationFacade dealAggregationFacade;
  private final MeetingAggregationFacade meetingAggregationFacade;
  private final CallAggregationFacade callAggregationFacade;
  private final CustomFieldService customFieldService;
  private final ReportService reportService;
  private final UserFacade userFacade;
  private static final String INDIVIDUAL_GOAL = "individual_goal";

  @Autowired
  public GoalService(GoalFacade goalFacade, UserService userService,
      AbstractFactFieldFactory abstractFactFieldFactory,
      LeadAggregationFacade leadAggregationFacade,
      DealAggregationFacade dealAggregationFacade,
      MeetingAggregationFacade meetingAggregationFacade, CallAggregationFacade callAggregationFacade,
      CustomFieldService customFieldService, ReportService reportService, UserFacade userFacade) {
    this.goalFacade = goalFacade;
    this.userService = userService;
    this.abstractFactFieldFactory = abstractFactFieldFactory;
    this.leadAggregationFacade = leadAggregationFacade;
    this.dealAggregationFacade = dealAggregationFacade;
    this.meetingAggregationFacade = meetingAggregationFacade;
    this.callAggregationFacade = callAggregationFacade;
    this.customFieldService = customFieldService;
    this.reportService = reportService;
    this.userFacade = userFacade;
  }

  public Mono<GoalSummary> createGoal(CreateGoalRequest createGoalRequest) {
    User loggedInUser = userService.getLoggedInUser();
    if (!loggedInUser.canCreateGoal()) {
      throw new InsufficientGoalPrivilegesException();
    }
    var userDetails = userService.getUserDetails(loggedInUser.getId(), userService.getAuthenticationToken());
    return goalFacade.createGoal(loggedInUser, userDetails, createGoalRequest)
        .map(goal -> {
          Report reportFromGoal = reportService.createReportFromGoal(goal, loggedInUser);
          goalFacade.updateDefaultReportIdByGoalIdAndTenantId(goal.getId(), goal.getTenantId(), reportFromGoal.getId());
          return new GoalSummary(goal.getId(), goal.getName(), reportFromGoal.getId());
        });
  }

  public Mono<GoalResponse> updateGoal(UpdateGoalRequest updateGoalRequest, long goalId) {
    User loggedInUser = userService.getLoggedInUser();
    if (!loggedInUser.canUpdateHisGoal() && !loggedInUser.canUpdateAllGoal()) {
      throw new InsufficientGoalPrivilegesException();
    }
    var userDetails = userService.getUserDetails(loggedInUser.getId(), userService.getAuthenticationToken());
    return goalFacade.updateGoal(updateGoalRequest, goalId, loggedInUser, userDetails)
        .map(goal -> toGoalResponse(goal, loggedInUser));
  }

  public GoalResponse getGoalById(long goalId) {
    User loggedInUser = userService.getLoggedInUser();
    Goal existingGoal = goalFacade.getGoalByTenantIdAndId(loggedInUser, goalId);
    return toGoalResponse(existingGoal, loggedInUser);
  }

  public Page<GoalResponse> search(Optional<FilterRequest> filterRequest, Pageable pageable) {
    User loggedInUser = userService.getLoggedInUser();
    var goalPageResponse = getSearchResponse(filterRequest, pageable, loggedInUser);
    List<GoalResponse> goalResponses = goalPageResponse
        .getContent()
        .stream()
        .map(goal -> toGoalResponse(goal, loggedInUser))
        .collect(Collectors.toList());
    return new PageImpl<>(goalResponses, pageable, goalPageResponse.getTotalElements());
  }

  private Page<Goal> getSearchResponse(Optional<FilterRequest> filterRequest, Pageable pageable, User loggedInUser) {
    if (filterRequest.isPresent()) {
      Set<com.sling.sales.report.core.api.response.GoalFilter> goalFilters = filterRequest.get()
          .getJsonRules()
          .stream()
          .map(jsonRule -> new com.sling.sales.report.core.api.response.GoalFilter(jsonRule.getOperator(), jsonRule.getField(), jsonRule.getType(),
              jsonRule.getValue()))
          .collect(Collectors.toSet());
      return goalFacade.search(goalFilters, loggedInUser, pageable);
    }
    return goalFacade.search(Collections.emptySet(), loggedInUser, pageable);
  }

  public List<IdName> getGoalsByUser(String entityType) {
    User loggedInUser = userService.getLoggedInUser();
    return goalFacade.getGoalsByUser(loggedInUser, entityType);
  }

  private GoalResponse toGoalResponse(Goal goal, User loggedInUser) {
    com.sling.sales.report.core.domain.goal.GoalDimension goalDimension = goal.getDimensions().get(0);
    com.sling.sales.report.core.domain.goal.GoalMetric goalMetric = goal.getMetrics().get(0);
    Frequency frequency = goal.getFrequency();
    GoalDimension newGoalDimension = new GoalDimension(goalDimension.getField(), goalDimension.getHeader(), goalDimension.getFormat(),
        goalDimension.getFieldType().name(), goalDimension.getLookupEntity(), goalDimension.getPrimaryField(), goalDimension.getProperty());
    GoalMetric newGoalMetric = new GoalMetric(goalMetric.getField(), goalMetric.getHeader(), goalMetric.getType().name(),
        goalMetric.getFieldType());
    GoalFrequency goalFrequency = new GoalFrequency(frequency.getType().name(), frequency.getStart(), frequency.getEnd(), frequency.getHeader());
    com.sling.sales.report.core.domain.goal.Filter goalDateRange = goal.getDateRange();
    GoalFilter dateRange = new GoalFilter(goalDateRange.getOperator(), goalDateRange.getField(), goalDateRange.getHeader(), goalDateRange.getType(),
        goalDateRange.getValue(),
        goalDateRange.getFieldType(), goalDateRange.getPrimaryField(), goalDateRange.getProperty());
    List<GoalFilter> filters = toFilters(goal);
    List<GoalFieldValue> fieldValues = toFieldValues(goal);
    Action recordActions = getRecordActions(goal.getEntityType().getEntityClass(), loggedInUser, goal.getCreatedBy(), goal.getOwners());
    List<IdName> ownersIdName = Collections.emptyList();
    if (ObjectUtils.isNotEmpty(goal.getOwners())) {
      ownersIdName = goal.getOwners().stream().map(user -> new IdName(user.getId(), user.getName()))
          .collect(toList());
    }

    return new GoalResponse(goal.getId(), goal.getName(), goal.getDescription(), goal.getEntityType().name(), goal.getValue(), newGoalDimension,
        newGoalMetric, goalFrequency, filters, fieldValues, goal.getCreatedAt(), goal.getUpdatedAt(), goal.getCreatedBy(), goal.getUpdatedBy(),
        recordActions, goal.isActive(), goal.getDefaultReportId(), dateRange, ownersIdName, goal.isShowOthersProgress());
  }

  private List<GoalFieldValue> toFieldValues(Goal goal) {
    return goal.getFieldValues()
        .stream()
        .map(fieldValue -> new GoalFieldValue(fieldValue.getFieldName(), fieldValue.getFieldType().name(), fieldValue.getFieldValueId(),
            fieldValue.getFieldValueName(), fieldValue.getValue(), fieldValue.getLookupEntity()))
        .collect(Collectors.toList());
  }

  private List<GoalFilter> toFilters(Goal goal) {
    if (ObjectUtils.isEmpty(goal.getFilters())) {
      return Collections.emptyList();
    }
    return goal.getFilters()
        .stream()
        .map(goalFilter -> new GoalFilter(goalFilter.getOperator(), goalFilter.getField(), goalFilter.getHeader(), goalFilter.getType(),
            goalFilter.getValue(), goalFilter.getFieldType(), goalFilter.getPrimaryField(), goalFilter.getProperty()))
        .collect(Collectors.toList());
  }

  private <T extends Fact> Action getRecordActions(Class<T> entityClass, User loggedInUser, User creator, Set<User> goalOwners) {
    Action recordActions = new Action();
    if (Call.class.equals(entityClass) && loggedInUser.canReadCall() && !loggedInUser.returnEntitiesHavingCallPermission().isEmpty()) {
      getRecordActions(loggedInUser, creator, recordActions, goalOwners);
    }
    if (loggedInUser.hasAccessToFacts(entityClass)) {
      getRecordActions(loggedInUser, creator, recordActions, goalOwners);
    }
    return recordActions;
  }

  private void getRecordActions(User loggedInUser, User creator, Action recordActions, Set<User> goalOwners) {
    if (loggedInUser.canQueryAllGoals() || (loggedInUser.canQueryHisGoals() && isCreatorOrGoalOwner(loggedInUser, creator, goalOwners))) {
      recordActions.setRead(true);
    }
    if (loggedInUser.canUpdateAllGoal() || (loggedInUser.canUpdateHisGoal() && isCreatorOrGoalOwner(loggedInUser, creator, goalOwners))) {
      recordActions.setUpdate(true);
    }
  }

  private boolean isCreatorOrGoalOwner(User loggedInUser, User creator, Set<User> goalOwners) {
    return (ObjectUtils.isNotEmpty(creator) && loggedInUser.getId().equals(creator.getId()))
        || isGoalOwner(loggedInUser, goalOwners);
  }

  private boolean isGoalOwner(User loggedInUser, Set<User> goalOwners) {
    if (ObjectUtils.isEmpty(goalOwners)) {
      return false;
    }
    Set<Long> goalOwnerIds = goalOwners.stream().map(User::getId).collect(Collectors.toSet());
    return goalOwnerIds.contains(loggedInUser.getId());
  }

  private <T extends Fact> List<FactFilter<T>> createFactFilters(AggregationRequestV3 aggregationRequest, String timezone,
      Map<String, FilterDimension<T>> customFilterDimensions,
      Class<T> factClass) {
    List<Filter> aggregateFilters = new ArrayList<>();
    if (aggregationRequest.getDateRange() != null) {
      aggregateFilters.add(aggregationRequest.getDateRange());
    }
    aggregateFilters.addAll(aggregationRequest.getFilters());

    return aggregateFilters.stream()
        .map(
            filter -> {
              var operator = tryGetOperator(filter.getOperator());
              var dimension = getFilterDimension(filter, customFilterDimensions, factClass, operator);
              if (ObjectUtils.isEmpty(dimension)) {
                return null;
              }
              return new FactFilter<>(
                  operator,
                  dimension,
                  filter,
                  timezone);
            })
        .filter(Objects::nonNull)
        .collect(toList());
  }

  private <T extends Fact> FilterDimension<T> getFilterDimension(Filter filter, Map<String, FilterDimension<T>> customFilterDimensions,
      Class<T> factClass, Operator operator) {
    if (ObjectUtils.isEmpty(filter.getPrimaryField()) && ObjectUtils.isEmpty(filter.getProperty())) {
      return abstractFactFieldFactory.getFieldFactory(factClass)
          .getFilterField(filter.getFieldName(), operator, customFilterDimensions);
    }
    return abstractFactFieldFactory.getFieldFactory(factClass).createUserPropertyFilterIdDimension(filter);
  }

  private Operator tryGetOperator(String operatorString) {
    try {
      return Operator.valueOf(operatorString);
    } catch (IllegalArgumentException e) {
      log.error("Unsupported operator requested", e);
      throw new UnsupportedFilterException();
    }
  }

  public GoalAggregateResponse goalAggregateResultsForDeal(long goalId, GoalAggregationRequest goalAggregationRequest, String timezone,
      boolean prorated, Long currencyId) {
    validatePipelineAndPipelineStage(goalAggregationRequest);
    validateGoalMetricAndDimension(goalAggregationRequest);
    User loggedInUser = userService.getLoggedInUser();
    Optional<Goal> optionalGoal = goalFacade.getOptionalGoal(loggedInUser, goalId);
    validateOptionalGoal(optionalGoal);
    Goal goal = optionalGoal.get();
    DimensionDetail<Deal> dimensionDetail = customFieldService.getDealDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Deal>> factFilters = createFactFilters(goalAggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Deal.class);
    return dealAggregationFacade
        .getGoalAggregateRecords(dimensionDetail.getGroupByDimensions(), factFilters, goalAggregationRequest.getGroupBy(),
            goalAggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
            new GoalDetail(goalId, goal.getValue(), prorated, goalAggregationRequest.getDateRange(), goal.getFrequency().getType(), timezone,
                goal.getDimensions().get(0).getField(), goal.getOwners(), goal.getCreatedBy(), goal.isShowOthersProgress()),
            loggedInUser,
            goal.getFieldValues(), currencyId);
  }

  public GoalAggregateResponse goalAggregateResultsForMeeting(long goalId, GoalAggregationRequest goalAggregationRequest, String timezone,
      boolean prorated, Long currencyId) {
    validateGoalMetricAndDimension(goalAggregationRequest);
    User loggedInUser = userService.getLoggedInUser();
    Optional<Goal> optionalGoal = goalFacade.getOptionalGoal(loggedInUser, goalId);
    validateOptionalGoal(optionalGoal);
    Goal goal = optionalGoal.get();
    DimensionDetail<Meeting> dimensionDetail = customFieldService.getMeetingDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Meeting>> factFilters = createFactFilters(goalAggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Meeting.class);
    return meetingAggregationFacade
        .getGoalAggregateRecords(dimensionDetail.getGroupByDimensions(), factFilters, goalAggregationRequest.getGroupBy(),
            goalAggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
            new GoalDetail(goalId, goal.getValue(), prorated, goalAggregationRequest.getDateRange(), goal.getFrequency().getType(), timezone,
                goal.getDimensions().get(0).getField(), goal.getOwners(), goal.getCreatedBy(), goal.isShowOthersProgress()),
            loggedInUser,
            goal.getFieldValues(), currencyId);
  }

  public GoalAggregateResponse goalAggregateResultsForLead(long goalId, GoalAggregationRequest goalAggregationRequest, String timezone,
      boolean prorated, Long currencyId) {
    validatePipelineAndPipelineStage(goalAggregationRequest);
    validateGoalMetricAndDimension(goalAggregationRequest);
    User loggedInUser = userService.getLoggedInUser();
    Optional<Goal> optionalGoal = goalFacade.getOptionalGoal(loggedInUser, goalId);
    validateOptionalGoal(optionalGoal);
    Goal goal = optionalGoal.get();
    DimensionDetail<Lead> dimensionDetail = customFieldService.getLeadDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Lead>> factFilters = createFactFilters(goalAggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Lead.class);
    return leadAggregationFacade
        .getGoalAggregateRecords(dimensionDetail.getGroupByDimensions(), factFilters, goalAggregationRequest.getGroupBy(),
            goalAggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
            new GoalDetail(goalId, goal.getValue(), prorated, goalAggregationRequest.getDateRange(), goal.getFrequency().getType(), timezone,
                goal.getDimensions().get(0).getField(), goal.getOwners(), goal.getCreatedBy(), goal.isShowOthersProgress()),
            loggedInUser, goal.getFieldValues(), currencyId);
  }

  public GoalAggregateResponse goalAggregateResultsForCall(long goalId, GoalAggregationRequest goalAggregationRequest, String timezone,
      boolean prorated, Long currencyId) {
    validateGoalMetricAndDimension(goalAggregationRequest);
    User loggedInUser = userService.getLoggedInUser();
    Optional<Goal> optionalGoal = goalFacade.getOptionalGoal(loggedInUser, goalId);
    validateOptionalGoal(optionalGoal);
    Goal goal = optionalGoal.get();
    DimensionDetail<Call> dimensionDetail = customFieldService.getCallDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Call>> factFilters = createFactFilters(goalAggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Call.class);
    return callAggregationFacade
        .getGoalAggregateRecords(dimensionDetail.getGroupByDimensions(), factFilters, goalAggregationRequest.getGroupBy(),
            goalAggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
            new GoalDetail(goalId, goal.getValue(), prorated, goalAggregationRequest.getDateRange(), goal.getFrequency().getType(), timezone,
                goal.getDimensions().get(0).getField(), goal.getOwners(), goal.getCreatedBy(), goal.isShowOthersProgress()),
            loggedInUser, goal.getFieldValues(), currencyId);
  }

  private static void validateOptionalGoal(Optional<Goal> optionalGoal) {
    if (optionalGoal.isEmpty()) {
      throw new ResourceNotFoundException(ErrorCode.GOAL_NOT_FOUND);
    }
    if (!optionalGoal.get().isActive()) {
      throw new InvalidReportRequestException(ErrorCode.INACTIVE_GOAL_ERROR);
    }
  }

  private void validatePipelineAndPipelineStage(GoalAggregationRequest aggregationRequest) {
    boolean isInvalidPipeline = aggregationRequest.getFilters()
        .stream()
        .anyMatch(filter -> filter.getFieldName().equals("pipeline") && (filter.getOperator().equals(Operator.in.name()) || filter.getOperator()
            .equals(Operator.not_in.name())));
    boolean isInvalidPipelineStage = aggregationRequest.getFilters()
        .stream()
        .anyMatch(filter -> filter.getFieldName().equals("pipelineStage") && (filter.getOperator().equals(Operator.in.name()) || filter.getOperator()
            .equals(Operator.not_in.name())));
    if (isInvalidPipeline && isInvalidPipelineStage) {
      throw new InvalidPipelineOperatorException();
    }
  }

  private void validateGoalMetricAndDimension(GoalAggregationRequest goalAggregationRequest) {
    if (goalAggregationRequest.getMetrics().size() > 1) {
      throw new InvalidReportRequestException(ErrorCode.INVALID_GOAL_METRICS);
    }
    if (goalAggregationRequest.getGroupBy().size() > 1) {
      throw new InvalidReportRequestException(ErrorCode.INVALID_GOAL_DIMENSIONS);
    }
  }

  public Mono<Boolean> activate(long goalId) {
    User loggedInUser = userService.getLoggedInUser();
    if (!loggedInUser.canUpdateHisGoal() && !loggedInUser.canUpdateAllGoal()) {
      throw new InsufficientGoalPrivilegesException();
    }
    var userDetails = userService.getUserDetails(loggedInUser.getId(), userService.getAuthenticationToken());
    return goalFacade.activate(loggedInUser, goalId, userDetails).map(Goal::isActive);
  }

  public Mono<Boolean> deactivate(long goalId) {
    User loggedInUser = userService.getLoggedInUser();
    if (!loggedInUser.canUpdateHisGoal() && !loggedInUser.canUpdateAllGoal()) {
      throw new InsufficientGoalPrivilegesException();
    }
    var userDetails = userService.getUserDetails(loggedInUser.getId(), userService.getAuthenticationToken());
    return goalFacade.deactivate(loggedInUser, goalId, userDetails).map(Goal::isActive);
  }

  public void handleGoalsPlanChanges(UsageLimitChangedEvent usageLimitChangedEvent) {
    User loggedInUser = userService.getLoggedInUser();
    var userDetails = userService.getUserDetails1(loggedInUser.getId(), userService.getAuthenticationToken());
    goalFacade.handleGoalsPlanChanges(usageLimitChangedEvent, loggedInUser, userDetails);
  }

  public GoalAchievementResponse getMyGoalAchievement(long goalId, String timezone) {
    User loggedInUser = userService.getLoggedInUser();
    Optional<Goal> optionalGoal = goalFacade.getOptionalGoal(loggedInUser, goalId);
    if (optionalGoal.isEmpty()) {
      throw new ResourceNotFoundException(ErrorCode.GOAL_NOT_FOUND);
    }
    var goal = optionalGoal.get();
    return getMyGoalAchievementResponse(goalId, timezone, loggedInUser, goal, false);
  }

  private GoalAchievementResponse getMyGoalAchievementForSummary(long goalId, String timezone) {
    User loggedInUser = userService.getLoggedInUser();
    Goal goal = goalFacade.getGoalByIdAndTenantIdWithAllFields(loggedInUser.getTenantId(), goalId);
    if (!goal.isActive()) {
      return toGoalAchievementResponse(goal, null, emptyList(), goal.getDateRange(), emptyList(), INDIVIDUAL_GOAL, null);
    }
    try {
      return getMyGoalAchievementResponse(goalId, timezone, loggedInUser, goal, true);
    } catch (Exception e) {
      log.error("exception while getting my goal achievement", e);
      return toGoalAchievementResponse(goal, null, emptyList(), goal.getDateRange(), emptyList(), INDIVIDUAL_GOAL, null);
    }
  }

  private GoalAchievementResponse getMyGoalAchievementResponse(long goalId, String timezone, User loggedInUser, Goal goal, boolean isGoalSummary) {
    ReportResponse report = reportService.getReport(goal.getDefaultReportId());
    ArrayList<Filter> filters = new ArrayList<>(report.getConfig().getFilters());
    AggregationRequestV3 config = report.getConfig();
    com.sling.sales.report.core.domain.goal.GoalDimension goalDimension = goal.getDimensions().get(0);
    filters.removeIf(filter -> filter.getFieldName().equals(goalDimension.getField()));

    Set<Long> fieldValueIds = goal.getFieldValues().stream().map(com.sling.sales.report.core.domain.goal.GoalFieldValue::getFieldValueId)
        .collect(Collectors.toSet());
    if (isUserLookUp(goal.getDimensions()) && fieldValueIds.contains(loggedInUser.getId())) {
      filters.add(getCurrenUserFilter(loggedInUser, goalDimension));
    }

    GoalAggregationRequest goalAggregationRequest = new GoalAggregationRequest(
        filters,
        config.getGroupBy(),
        config.getDateRange(),
        config.getMetrics(),
        config.getColorCodes()
    );

    // get currentUser goal Achievement
    Long currencyId = ObjectUtils.isEmpty(report.getCurrency()) ? null : report.getCurrency().getId();
    GoalDetail goalDetail = new GoalDetail(goalId, goal.getValue(), true, goalAggregationRequest.getDateRange(),
        goal.getFrequency().getType(), timezone, goal.getDimensions().get(0).getField(), goal.getOwners(), goal.getCreatedBy(), goal.isShowOthersProgress());

    Achievement achievement = null;
    if (isUserLookUp(goal.getDimensions()) && fieldValueIds.contains(loggedInUser.getId())) {
      GoalAggregateResponse goalAggregateRecords = getGoalAggregateRecordsByEntity(loggedInUser, goal, goalDetail,
          goalAggregationRequest, currencyId, timezone);
      GoalAggregateRecord goalAggregateRecord = ObjectUtils.isEmpty(goalAggregateRecords.getResult()) ? null : goalAggregateRecords.getResult().get(0);
      achievement = goalAggregateRecord != null ? Achievement.from(goalAggregateRecord.getValue().get(0)) : null;
    }

    Achievement totalAchievement = null;
    if (!isGoalSummary && (loggedInUser.canQueryAllGoals() || isCreatorOrGoalOwner(loggedInUser, goal.getCreatedBy(), goal.getOwners()))) {
      totalAchievement = getTotalGoalAchievement(report, goal, goalDetail, timezone, loggedInUser);
    }
    return toGoalAchievementResponse(goal, achievement, filters, goal.getDateRange(), report.getConfig().getColorCodes(), INDIVIDUAL_GOAL, totalAchievement);
  }

  private Filter getCurrenUserFilter(User loggedInUser, com.sling.sales.report.core.domain.goal.GoalDimension goalDimension) {
    if (GoalFieldType.MEETING_INVITEES.equals(goalDimension.getFieldType())) {
      Optional<User> optionalUser = userFacade.getUserByIdAndTenantId(loggedInUser.getId(), loggedInUser.getTenantId());
      return new Filter("equal", goalDimension.getField(), goalDimension.getField(), "participants_lookup",
          new MeetingLookup(loggedInUser.getId(), "user", optionalUser.map(User::getName).orElse(null)),
          null, null, goalDimension.getFieldType().name(), null, null);
    }
    return new Filter("equal", goalDimension.getField(), goalDimension.getField(), "long",
        loggedInUser.getId(), null, null, goalDimension.getFieldType().name(), null, null);
  }

  private GoalAggregateResponse getGoalAggregateRecordsByEntity(User loggedInUser, Goal goal, GoalDetail goalDetail,
      AggregationRequestV3 goalAggregationRequest, Long currencyId, String timezone) {
    switch (goal.getEntityType()) {
      case LEAD:
        return getLeadGoalAggregateRecords(loggedInUser, goal, goalDetail,
            goalAggregationRequest, currencyId, timezone);
      case DEAL:
        return getDealGoalAggregateRecords(loggedInUser, goal, goalDetail,
            goalAggregationRequest, currencyId, timezone);
      case MEETING:
        return getMeetingGoalAggregateRecords(loggedInUser, goal, goalDetail,
            goalAggregationRequest, currencyId, timezone);
      case CALL:
        return getCallGoalAggregateRecords(loggedInUser, goal, goalDetail,
            goalAggregationRequest, currencyId, timezone);
    }
    throw new InvalidFactForReporting();
  }

  private GoalAggregateResponse getLeadGoalAggregateRecords(User loggedInUser, Goal goal, GoalDetail goalDetail,
      AggregationRequestV3 goalAggregationRequest, Long currencyId, String timezone) {
    DimensionDetail<Lead> dimensionDetail = customFieldService.getLeadDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Lead>> factFilters = createFactFilters(goalAggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Lead.class);
    return leadAggregationFacade.getGoalAggregateRecords(
        dimensionDetail.getGroupByDimensions(), factFilters, goalAggregationRequest.getGroupBy(),
        goalAggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
        goalDetail, loggedInUser, goal.getFieldValues(), currencyId);
  }

  private GoalAggregateResponse getDealGoalAggregateRecords(User loggedInUser, Goal goal, GoalDetail goalDetail,
      AggregationRequestV3 goalAggregationRequest, Long currencyId, String timezone) {
    DimensionDetail<Deal> dimensionDetail = customFieldService.getDealDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Deal>> factFilters = createFactFilters(goalAggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Deal.class);
    return dealAggregationFacade.getGoalAggregateRecords(
        dimensionDetail.getGroupByDimensions(), factFilters, goalAggregationRequest.getGroupBy(),
        goalAggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
        goalDetail, loggedInUser, goal.getFieldValues(), currencyId);
  }

  private GoalAggregateResponse getMeetingGoalAggregateRecords(User loggedInUser, Goal goal, GoalDetail goalDetail,
      AggregationRequestV3 goalAggregationRequest, Long currencyId, String timezone) {
    DimensionDetail<Meeting> dimensionDetail = customFieldService.getMeetingDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Meeting>> factFilters = createFactFilters(goalAggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Meeting.class);
    return meetingAggregationFacade.getGoalAggregateRecords(
        dimensionDetail.getGroupByDimensions(), factFilters, goalAggregationRequest.getGroupBy(),
        goalAggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
        goalDetail, loggedInUser, goal.getFieldValues(), currencyId);
  }

  private GoalAggregateResponse getCallGoalAggregateRecords(User loggedInUser, Goal goal, GoalDetail goalDetail,
      AggregationRequestV3 goalAggregationRequest, Long currencyId, String timezone) {
    DimensionDetail<Call> dimensionDetail = customFieldService.getCallDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Call>> factFilters = createFactFilters(goalAggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Call.class);
    return callAggregationFacade.getGoalAggregateRecords(
        dimensionDetail.getGroupByDimensions(), factFilters, goalAggregationRequest.getGroupBy(),
        goalAggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
        goalDetail, loggedInUser, goal.getFieldValues(), currencyId);
  }

  private boolean isUserLookUp(List<com.sling.sales.report.core.domain.goal.GoalDimension> dimensions) {
    if (ObjectUtils.isEmpty(dimensions)) {
      return false;
    }
    com.sling.sales.report.core.domain.goal.GoalDimension goalDimension = dimensions.get(0);
    return List.of(GoalFieldType.LOOK_UP, GoalFieldType.MEETING_INVITEES).contains(goalDimension.getFieldType())
        && "USER".equals(goalDimension.getLookupEntity());
  }

  private Achievement getTotalGoalAchievement(ReportResponse report, Goal goal, GoalDetail goalDetail,
      String timezone, User loggedInUser) {
    try {
      AggregationRequestV3 goalAggregationRequest = report.getConfig();
      Long currencyId = ObjectUtils.isEmpty(report.getCurrency()) ? null : report.getCurrency().getId();

      GoalAggregateResponse goalAggregateRecords = getGoalAggregateRecordsByEntity(loggedInUser, goal, goalDetail,
          goalAggregationRequest, currencyId, timezone);

      GoalCountSummary goalCountSummary = goalAggregateRecords.getGoalSummary();
      return goalCountSummary != null ? Achievement.from(goalCountSummary) : null;
    } catch (Exception e) {
      log.error("Exception while getting total goal achievement for user {}, tenantId: {}, report: {}, goal: {}",
          loggedInUser.getId(), loggedInUser.getTenantId(), report.getId(), goal.getId(), e);
      return null;
    }
  }

  private GoalAchievementResponse getTotalGoalAchievement(long goalId, String timezone) {
    User loggedInUser = userService.getLoggedInUser();
    Goal goal = goalFacade.getGoalByIdAndTenantIdWithAllFields(loggedInUser.getTenantId(), goalId);
    if (!goal.isActive()) {
      return toGoalAchievementResponse(goal, null, emptyList(), goal.getDateRange(), emptyList(), "total_goal", null);
    }
    try {
      ReportResponse report = reportService.getReport(goal.getDefaultReportId());
      AggregationRequestV3 goalAggregationRequest = report.getConfig();
      Long currencyId = ObjectUtils.isEmpty(report.getCurrency()) ? null : report.getCurrency().getId();
      GoalAggregateResponse goalAggregateRecords = getGoalAggregateRecordsByEntity(loggedInUser, goal,
          new GoalDetail(goalId, goal.getValue(), true, goalAggregationRequest.getDateRange(),
              goal.getFrequency().getType(), timezone, goal.getDimensions().get(0).getField(), goal.getOwners(), goal.getCreatedBy(), goal.isShowOthersProgress()),
          goalAggregationRequest, currencyId, timezone);
      GoalCountSummary goalCountSummary = goalAggregateRecords.getGoalSummary();
      Achievement achievement = goalCountSummary != null ? Achievement.from(goalCountSummary) : null;
      return toGoalAchievementResponse(goal, achievement, goalAggregationRequest.getFilters(), goal.getDateRange(),
          report.getConfig().getColorCodes(),
          "total_goal", null);
    } catch (Exception e) {
      log.error("exception while getting my total achievement", e);
      return toGoalAchievementResponse(goal, null, emptyList(), goal.getDateRange(), emptyList(), "total_goal", null);
    }
  }

  public List<GoalAchievementResponse> getGoalsSummaries(GoalSummaryRequest goalSummaryRequest, String timezone) {
    return goalSummaryRequest.getGoals()
        .stream()
        .map(goalSummaryRequestDetail -> goalAchievementByDashletType(goalSummaryRequestDetail, timezone))
        .collect(toList());
  }

  private GoalAchievementResponse goalAchievementByDashletType(GoalSummaryRequestDetail goalSummaryRequestDetail, String timezone) {
    if (goalSummaryRequestDetail.getDashletType().equals(INDIVIDUAL_GOAL)) {
      return getMyGoalAchievementForSummary(goalSummaryRequestDetail.getId(), timezone);
    }
    return getTotalGoalAchievement(goalSummaryRequestDetail.getId(), timezone);
  }

  private GoalAchievementResponse toGoalAchievementResponse(Goal goal, Achievement achievement, List<Filter> updatedFilters,
      com.sling.sales.report.core.domain.goal.Filter dateRange, List<ColorCode> colorCodes, String dashletType, Achievement totalAchievement) {
    com.sling.sales.report.core.domain.goal.GoalDimension goalDimension = goal.getDimensions().get(0);
    com.sling.sales.report.core.domain.goal.GoalMetric goalMetric = goal.getMetrics().get(0);
    Frequency frequency = goal.getFrequency();
    GoalDimension newGoalDimension = new GoalDimension(goalDimension.getField(), goalDimension.getHeader(), goalDimension.getFormat(),
        goalDimension.getFieldType().name(), goalDimension.getLookupEntity(), goalDimension.getPrimaryField(), goalDimension.getProperty());
    GoalMetric newGoalMetric = new GoalMetric(goalMetric.getField(), goalMetric.getHeader(), goalMetric.getType().name(),
        goalMetric.getFieldType());
    GoalFrequency goalFrequency = new GoalFrequency(frequency.getType().name(), frequency.getStart(), frequency.getEnd(), frequency.getHeader());
    List<GoalFilter> filters = updatedFilters.stream()
        .map(goalFilter -> new GoalFilter(goalFilter.getOperator(), goalFilter.getFieldName(), goalFilter.getFieldName(), goalFilter.getFieldType(),
            goalFilter.getValue(), goalFilter.getFieldInputType(), goalFilter.getPrimaryField(), goalFilter.getProperty()))
        .collect(Collectors.toList());
    List<GoalFieldValue> fieldValues = toFieldValues(goal);
    List<IdName> owners = goal.getOwners().stream().map(goal1 -> new IdName(goal1.getId(), goal1.getName())).collect(toList());
    return new GoalAchievementResponse(goal.getId(), goal.getName(), goal.getDescription(), goal.getEntityType().name(), goal.getValue(),
        newGoalDimension,
        newGoalMetric, goalFrequency, filters, fieldValues, goal.getCreatedBy(), goal.getUpdatedBy(),
        goal.isActive(), goal.getDefaultReportId(), achievement, dateRange, colorCodes, dashletType, totalAchievement, owners);
  }

  public void nudgeUser(long goalId, NudgeUserRequest nudgeUserRequest) {
    User loggedInUser = userService.getLoggedInUser();
    goalFacade.nudgeUser(goalId, nudgeUserRequest, loggedInUser);
  }
}
