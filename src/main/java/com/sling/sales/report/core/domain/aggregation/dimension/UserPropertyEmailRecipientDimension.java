package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.in;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_in;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.Format;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import com.sling.sales.report.core.domain.email.Email;
import com.sling.sales.report.core.domain.email.EmailRecipient;
import com.sling.sales.report.core.domain.email.EmailRecipient_;
import com.sling.sales.report.core.domain.email.Email_;
import com.sling.sales.report.security.domain.Team;
import com.sling.sales.report.security.domain.Team_;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.User_;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import org.springframework.data.jpa.domain.Specification;

public class UserPropertyEmailRecipientDimension<T extends Fact, V extends EntityDimension> implements FilterDimension<T>, GroupByDimension<T> {

  private final List<Operator> allowedOperators = asList(equal, not_equal, is_null, is_not_null, in, not_in);
  private final String name;
  private final String dimensionName;
  private final SetAttribute<T, V> dimensionField;
  private final Function<Join<T, V>, Path<Long>> getTeamId;
  private final Function<Join<T, V>, Path<String>> getTeamName;
  private final Function<Join<T, V>, Path<String>> getRecipientEntity;
  private final List<FilterDimension<T>> requiredFilters;
  private Format format;

  public UserPropertyEmailRecipientDimension(
      String name,
      String dimensionName,
      SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> pathToDimensionId,
      Function<Join<T, V>, Path<String>> pathToDimensionName,
      Function<Join<T, V>, Path<String>> pathToRecipientEntity,
      List<FilterDimension<T>> requiredFilters
  ) {
    this.name = name;
    this.dimensionName = dimensionName;
    this.dimensionField = dimensionField;
    this.getTeamId = pathToDimensionId;
    this.getTeamName = pathToDimensionName;
    this.getRecipientEntity = pathToRecipientEntity;
    this.requiredFilters = requiredFilters;
  }

  @Override
  public UserPropertyEmailRecipientDimension<T, V> withFormat(Format format) {
    this.format = format;
    return this;
  }

  @Override
  public Format getFormat() {
    return this.format;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return this.allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return this.requiredFilters;
  }

  @Override
  public String getName() {
    return this.name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    switch (operator) {
      case equal:
        return hasId(Long.parseLong(String.valueOf(value)));
      case not_equal:
        return doesNotHaveId(Long.parseLong(String.valueOf(value))).or(isNull());
      case is_null:
        return isNull();
      case is_not_null:
        return isNotNull();
      case in:
        return in(getParameters(value));
      case not_in:
        return notIn(getParameters(value)).or(isNull());
      default:
        return null;
    }
  }

  Specification<T> hasId(Long value) {
    return ((root, query, builder) -> builder.equal(getTeamId.apply(getOrCreateLeftJoin(root, builder)), value));
  }

  Specification<T> doesNotHaveId(Long value) {
    return ((root, query, builder) -> builder.notEqual(getTeamId.apply(getOrCreateLeftJoin(root, builder)), value));
  }

  Specification<T> isNull() {
    return ((root, query, builder) -> builder.isNull(getTeamId.apply(getOrCreateLeftJoin(root, builder))));
  }

  Specification<T> isNotNull() {
    return ((root, query, builder) -> builder.isNotNull(getTeamId.apply(getOrCreateLeftJoin(root, builder))));
  }

  private Specification<T> in(List<Long> values) {
    return ((root, query, builder) -> {
      Expression<Long> jsonbExpression = getTeamId.apply(getOrCreateLeftJoin(root, builder));
      return jsonbExpression.in(values);
    });
  }

  private Specification<T> notIn(List<Long> values) {
    return ((root, query, builder) -> {
      Expression<Long> jsonbExpression = getTeamId.apply(getOrCreateLeftJoin(root, builder));
      return jsonbExpression.in(values).not();
    });
  }

  private List<Long> getParameters(Object value) {
    if (value instanceof List) {
      return (List<Long>) value;
    }
    return Arrays.stream(String.valueOf(value).split(","))
        .map(String::trim)
        .map(Long::valueOf)
        .collect(toList());
  }

  @Override
  public List<Expression<?>> getGroupByPath(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, String timezone) {
    Join<T, V> join = getOrCreateLeftJoin(root, criteriaBuilder);
    return asList(getTeamId.apply(join), getTeamName.apply(join));
  }

  @Override
  public Aggregate toResultKey(Tuple tuple, int columnNumber) {
    int nextColumnNumber = columnNumber + 2;
    return new Aggregate(tuple.get(columnNumber, Long.class), tuple.get(columnNumber + 1, String.class), nextColumnNumber, null);
  }

  private Join<T, V> getOrCreateLeftJoin(Root<T> root, CriteriaBuilder builder) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> createJoin((Join<T, V>) j, builder))
        .findFirst()
        .orElseGet(() -> {
          Join<T, V> join = root.join(dimensionField, JoinType.INNER);
          return createJoin(join, builder);
        });
  }

  private Join<T, V> createJoin(Join<T, V> join, CriteriaBuilder builder) {
    join.on(builder.equal(getRecipientEntity.apply(join), "user"));
    return join;
  }

  public static UserPropertyEmailRecipientDimension<Email, EmailRecipient> getInstance(String name, String userProperty) {
    return new UserPropertyEmailRecipientDimension<>(
        name,
        Email_.EMAIL_RECIPIENTS,
        Email_.emailRecipients,
        join -> getOrCreateEmailInviteeUserLeftJoin(join, userProperty).get(Team_.id),
        join -> getOrCreateEmailInviteeUserLeftJoin(join, userProperty).get(Team_.name),
        join -> join.get(EmailRecipient_.entity),
        emptyList()
    );
  }

  private static Join<User, Team> getOrCreateEmailInviteeUserLeftJoin(Join<Email, EmailRecipient> emailRecipientJoin, String property) {
    Join<EmailRecipient, User> emailRecipientAndUserJoin = emailRecipientJoin.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals("userRecipient"))
        .map(j -> ((Join<EmailRecipient, User>) j))
        .findFirst()
        .orElseGet(() -> emailRecipientJoin.join(EmailRecipient_.userRecipient, JoinType.LEFT));

    return getOrCreateTeamsLeftJoin(emailRecipientAndUserJoin, property);
  }

  private static Join<User, Team> getOrCreateTeamsLeftJoin(Join<EmailRecipient, User> emailRecipientUserJoin, String dimensionName) {
    return emailRecipientUserJoin.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<User, Team>) j))
        .findFirst()
        .orElseGet(() -> emailRecipientUserJoin.join(User_.teams, JoinType.LEFT));
  }
}