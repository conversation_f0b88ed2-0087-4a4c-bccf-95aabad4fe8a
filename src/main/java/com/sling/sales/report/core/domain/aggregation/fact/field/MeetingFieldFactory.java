package com.sling.sales.report.core.domain.aggregation.fact.field;

import static java.util.Collections.emptyList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.domain.aggregation.dimension.DateDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.IdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.IdMeetingFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdAssociatedEntityFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdMeetingInviteeFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.TextDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.UserPropertyIdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.UserPropertyManyIdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.UserPropertyMeetingInviteeDimension;
import com.sling.sales.report.core.domain.meeting.Meeting;
import com.sling.sales.report.core.domain.meeting.MeetingInvitee;
import com.sling.sales.report.core.domain.meeting.MeetingInvitee_;
import com.sling.sales.report.core.domain.meeting.MeetingRelatedTo;
import com.sling.sales.report.core.domain.meeting.MeetingRelatedTo_;
import com.sling.sales.report.core.domain.meeting.MeetingUserAttendance;
import com.sling.sales.report.core.domain.meeting.MeetingUserAttendance_;
import com.sling.sales.report.core.domain.meeting.Meeting_;
import com.sling.sales.report.core.domain.meeting.Organizer;
import com.sling.sales.report.core.domain.meeting.Organizer_;
import com.sling.sales.report.dto.DimensionDetail;
import com.sling.sales.report.security.domain.Team;
import com.sling.sales.report.security.domain.Team_;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.User_;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;

@Service
class MeetingFieldFactory extends FactFieldFactory<Meeting> {

  private final static Map<String, MeetingUserMapping> MAPPED_PRIMARY_PROPERTIES = new HashMap<>() {{
    put("owner", new MeetingUserMapping(Meeting_.OWNED_BY, Meeting_.ownedBy));
    put("createdBy", new MeetingUserMapping(Meeting_.CREATED_BY, Meeting_.createdBy));
    put("updatedBy", new MeetingUserMapping(Meeting_.UPDATED_BY, Meeting_.updatedBy));
    put("conductedBy", new MeetingUserMapping(Meeting_.CONDUCTED_BY, Meeting_.conductedBy));
    put("cancelledBy", new MeetingUserMapping(Meeting_.CANCELLED_BY, Meeting_.cancelledBy));
  }};

  private final TextDimension<Meeting> status = new TextDimension<>(
      "status",
      root -> root.get(Meeting_.status),
      emptyList());

  private final TextDimension<Meeting> location = new TextDimension<>(
      "location",
      root -> root.get(Meeting_.location),
      emptyList());

  private final IdDimension<Meeting, User> owner = new IdDimension<>(
      "owner",
      Meeting_.OWNED_BY,
      Meeting_.ownedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final ManyIdMeetingInviteeFilterDimension<Meeting, MeetingInvitee> invitees = new ManyIdMeetingInviteeFilterDimension<>(
      "participants",
      Meeting_.MEETING_INVITEES,
      Meeting_.meetingInvitees,
      join -> join.get(MeetingInvitee_.entityId),
      join -> join.get(MeetingInvitee_.entity),
      join -> join.get(MeetingInvitee_.entityName),
      join -> join.get(MeetingInvitee_.email)
  );

  private final IdDimension<Meeting, User> createdBy = new IdDimension<>(
      "createdBy",
      Meeting_.CREATED_BY,
      Meeting_.createdBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdDimension<Meeting, User> updatedBy = new IdDimension<>(
      "updatedBy",
      Meeting_.UPDATED_BY,
      Meeting_.updatedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdDimension<Meeting, User> conductedBy = new IdDimension<>(
      "conductedBy",
      Meeting_.CONDUCTED_BY,
      Meeting_.conductedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdDimension<Meeting, User> cancelledBy = new IdDimension<>(
      "cancelledBy",
      Meeting_.CANCELLED_BY,
      Meeting_.cancelledBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final ManyIdAssociatedEntityFilterDimension<Meeting, MeetingRelatedTo> associatedLeads = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedLeads",
      Meeting_.MEETING_RELATED_TOS,
      Meeting_.meetingRelatedTos,
      join -> join.get(MeetingRelatedTo_.entityId),
      join -> join.get(MeetingRelatedTo_.entity),
      join -> join.get(MeetingRelatedTo_.entityName),
      "lead"
  );

  private final ManyIdAssociatedEntityFilterDimension<Meeting, MeetingRelatedTo> associatedContacts = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedContacts",
      Meeting_.MEETING_RELATED_TOS,
      Meeting_.meetingRelatedTos,
      join -> join.get(MeetingRelatedTo_.entityId),
      join -> join.get(MeetingRelatedTo_.entity),
      join -> join.get(MeetingRelatedTo_.entityName),
      "contact"
  );

  private final ManyIdAssociatedEntityFilterDimension<Meeting, MeetingRelatedTo> associatedDeals = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedDeals",
      Meeting_.MEETING_RELATED_TOS,
      Meeting_.meetingRelatedTos,
      join -> join.get(MeetingRelatedTo_.entityId),
      join -> join.get(MeetingRelatedTo_.entity),
      join -> join.get(MeetingRelatedTo_.entityName),
      "deal"
  );

  private final ManyIdAssociatedEntityFilterDimension<Meeting, MeetingRelatedTo> associatedCompanies = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedCompanies",
      Meeting_.MEETING_RELATED_TOS,
      Meeting_.meetingRelatedTos,
      join -> join.get(MeetingRelatedTo_.entityId),
      join -> join.get(MeetingRelatedTo_.entity),
      join -> join.get(MeetingRelatedTo_.entityName),
      "company"
  );

  private final IdMeetingFilterDimension<Meeting, Organizer> organizer = new IdMeetingFilterDimension<>(
      "organizer",
      Meeting_.ORGANIZER,
      Meeting_.organizer,
      join -> join.get(Organizer_.entityId),
      join -> join.get(Organizer_.entity)
  );

  private final ManyIdDimension<Meeting, MeetingUserAttendance> checkedInOutBy = new ManyIdDimension<>(
      "checkedInOutBy",
      Meeting_.MEETING_USER_ATTENDANCES,
      Meeting_.meetingUserAttendances,
      join -> getOrCreateMeetingAttendancesUserLeftJoin(join).get(User_.id),
      join -> getOrCreateMeetingAttendancesUserLeftJoin(join).get(User_.name),
      emptyList()
  );

  private final DateDimension<Meeting> createdAt = new DateDimension<>("createdAt", root -> root.get(Meeting_.createdAt));
  private final DateDimension<Meeting> updatedAt = new DateDimension<>("updatedAt", root -> root.get(Meeting_.updatedAt));
  private final DateDimension<Meeting> scheduledAt = new DateDimension<>("from", root -> root.get(Meeting_.scheduledFrom));
  private final DateDimension<Meeting> scheduledTo = new DateDimension<>("to", root -> root.get(Meeting_.scheduledTo));
  private final DateDimension<Meeting> conductedAt = new DateDimension<>("conductedAt", root -> root.get(Meeting_.conductedAt));
  private final DateDimension<Meeting> cancelledAt = new DateDimension<>("cancelledAt", root -> root.get(Meeting_.cancelledAt));
  private final DateDimension<Meeting> checkedInAt = new DateDimension<>(
      "checkedInAt",
      root -> getOrCreateMeetingAttendancesLeftJoin(root).get(MeetingUserAttendance_.checkedInAt)
  );
  private final DateDimension<Meeting> checkedOutAt = new DateDimension<>(
      "checkedOutAt",
      root -> getOrCreateMeetingAttendancesLeftJoin(root).get(MeetingUserAttendance_.checkedOutAt)
  );

  private final Map<String, GroupByDimension<Meeting>> SUPPORTED_GROUP_BY_DIMENSIONS = new HashMap<>() {
    {
      put(status.getName(), status);
      put(location.getName(), location);
      put(owner.getName(), owner);
      put(createdAt.getName(), createdAt);
      put(updatedAt.getName(), updatedAt);
      put(scheduledAt.getName(), scheduledAt);
      put(scheduledTo.getName(), scheduledTo);
      put(conductedAt.getName(), conductedAt);
      put(cancelledAt.getName(), cancelledAt);
      put(createdBy.getName(), createdBy);
      put(updatedBy.getName(), updatedBy);
      put(conductedBy.getName(), conductedBy);
      put(cancelledBy.getName(), cancelledBy);
      put(associatedLeads.getName(), associatedLeads);
      put(associatedContacts.getName(), associatedContacts);
      put(associatedDeals.getName(), associatedDeals);
      put(associatedCompanies.getName(), associatedCompanies);
      put(invitees.getName(), invitees);
      put(checkedInOutBy.getName(), checkedInOutBy);
      put(checkedInAt.getName(), checkedInAt);
      put(checkedOutAt.getName(), checkedOutAt);
    }
  };

  private final Map<String, FilterDimension<Meeting>> SUPPORTED_FILTER_DIMENSIONS = new HashMap<>() {
    {
      put(status.getName(), status);
      put(location.getName(), location);
      put(owner.getName(), owner);
      put(createdAt.getName(), createdAt);
      put(updatedAt.getName(), updatedAt);
      put(scheduledAt.getName(), scheduledAt);
      put(scheduledTo.getName(), scheduledTo);
      put(conductedAt.getName(), conductedAt);
      put(cancelledAt.getName(), cancelledAt);
      put(invitees.getName(), invitees);
      put(createdBy.getName(), createdBy);
      put(updatedBy.getName(), updatedBy);
      put(conductedBy.getName(), conductedBy);
      put(cancelledBy.getName(), cancelledBy);
      put(associatedLeads.getName(), associatedLeads);
      put(associatedContacts.getName(), associatedContacts);
      put(associatedDeals.getName(), associatedDeals);
      put(associatedCompanies.getName(), associatedCompanies);
      put(organizer.getName(), organizer);
      put(checkedInOutBy.getName(), checkedInOutBy);
      put(checkedInAt.getName(), checkedInAt);
      put(checkedOutAt.getName(), checkedOutAt);
    }
  };

  private final List<Metric<Meeting>> SUPPORTED_METRICS = new ArrayList<>() {
    {
      add(new Metric<>(
          "id",
          MetricType.COUNT,
          (root, builder) -> builder.countDistinct(root),
          null));
    }
  };

  @Override
  public DimensionDetail<Meeting> getDimensionDetail() {
    return new DimensionDetail<>(SUPPORTED_GROUP_BY_DIMENSIONS, SUPPORTED_FILTER_DIMENSIONS, SUPPORTED_METRICS);
  }

  @Override
  public GroupByDimension<Meeting> createUserPropertyGroupByIdDimension(GroupByField groupByField) {
    MeetingUserMapping meetingUserMapping = MAPPED_PRIMARY_PROPERTIES.get(groupByField.getPrimaryField());
    if ("teams".equals(groupByField.getProperty())) {
      if ("participants".equals(groupByField.getPrimaryField())) {
        return UserPropertyMeetingInviteeDimension.getInstance(groupByField.getName(), groupByField.getProperty());
      }
      if ("checkedInOutBy".equals(groupByField.getPrimaryField())) {
        return getCheckedInOutByUserPropertyDimension(groupByField.getName(), groupByField.getProperty());
      }
      return new UserPropertyIdDimension<>(
          groupByField.getName(),
          meetingUserMapping.getMappedColumnFieldName(),
          meetingUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateTeamsLeftJoin(join, groupByField.getProperty()).get(Team_.id),
          join -> getOrCreateTeamsLeftJoin(join, groupByField.getProperty()).get(Team_.name),
          emptyList(),
          groupByField.getPrimaryField(),
          groupByField.getProperty()
      );
    }
    return null;
  }

  @Override
  public FilterDimension<Meeting> createUserPropertyFilterIdDimension(Filter filter) {
    MeetingUserMapping meetingUserMapping = MAPPED_PRIMARY_PROPERTIES.get(filter.getPrimaryField());
    if ("teams".equals(filter.getProperty())) {
      if ("participants".equals(filter.getPrimaryField())) {
        return UserPropertyMeetingInviteeDimension.getInstance(filter.getFieldName(), filter.getProperty());
      }
      if ("checkedInOutBy".equals(filter.getPrimaryField())) {
        return getCheckedInOutByUserPropertyDimension(filter.getFieldName(), filter.getProperty());
      }
      return new UserPropertyIdDimension<>(
          filter.getFieldName(),
          meetingUserMapping.getMappedColumnFieldName(),
          meetingUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateTeamsLeftJoin(join, filter.getProperty()).get(Team_.id),
          join -> getOrCreateTeamsLeftJoin(join, filter.getProperty()).get(Team_.name),
          emptyList(),
          filter.getPrimaryField(),
          filter.getProperty()
      );
    }
    return null;
  }

  private UserPropertyManyIdDimension<Meeting, MeetingUserAttendance> getCheckedInOutByUserPropertyDimension(
      String fieldName, String userProperty
  ) {
    return new UserPropertyManyIdDimension<>(
        fieldName,
        Meeting_.MEETING_USER_ATTENDANCES,
        Meeting_.meetingUserAttendances,
        join -> getOrCreateTeamsLeftJoin(getOrCreateMeetingAttendancesUserLeftJoin(join), userProperty).get(Team_.id),
        join -> getOrCreateTeamsLeftJoin(getOrCreateMeetingAttendancesUserLeftJoin(join), userProperty).get(Team_.name),
        emptyList()
    );
  }

  private Join<User, Team> getOrCreateTeamsLeftJoin(Join<?, User> meetingUserJoin, String dimensionName) {
    return meetingUserJoin.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<User, Team>) j))
        .findFirst()
        .orElseGet(() -> meetingUserJoin.join(User_.teams, JoinType.LEFT));
  }

  private Join<MeetingUserAttendance, User> getOrCreateMeetingAttendancesUserLeftJoin(Join<Meeting, MeetingUserAttendance> meetingUserAttendanceJoin) {
    return meetingUserAttendanceJoin.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(MeetingUserAttendance_.USER))
        .map(j -> ((Join<MeetingUserAttendance, User>) j))
        .findFirst()
        .orElseGet(() -> meetingUserAttendanceJoin.join(MeetingUserAttendance_.user, JoinType.LEFT));
  }

  private Join<Meeting, MeetingUserAttendance> getOrCreateMeetingAttendancesLeftJoin(Root<Meeting> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(Meeting_.MEETING_USER_ATTENDANCES))
        .map(j -> ((Join<Meeting, MeetingUserAttendance>) j))
        .findFirst()
        .orElseGet(() -> root.join(Meeting_.meetingUserAttendances, JoinType.LEFT));
  }
}
