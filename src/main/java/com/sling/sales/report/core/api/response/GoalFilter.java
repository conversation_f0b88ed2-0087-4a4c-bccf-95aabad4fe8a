package com.sling.sales.report.core.api.response;

import com.sling.sales.report.core.domain.aggregation.exception.InvalidFilterRuleException;
import com.sling.sales.report.core.domain.goal.FrequencyType;
import com.sling.sales.report.core.domain.goal.Goal;
import com.sling.sales.report.core.domain.goal.GoalSpecification;
import com.sling.sales.report.core.domain.report.ReportType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

@Slf4j
public class GoalFilter {

  private final String operator;
  private final String field;
  private final String type;
  private final Object value;
  private static final List<String> validOperators = Arrays.asList("is_null", "is_not_null", "is_empty", "is_not_empty");

  public GoalFilter(String operator, String field, String type, Object value) {
    verify(operator, field, type, value);
    this.operator = operator;
    this.field = field;
    this.type = type;
    this.value = convertValue(field, value);
  }

  private Object convertValue(String field, Object value) {
    List<String> values = new ArrayList<>();
    boolean isList = value instanceof List;
    if (isList) {
      values.addAll((List<String>) value);
    }
    try {
      if ("entityType".equalsIgnoreCase(field) && value != null) {
        return isList ?
            values.stream()
                .map(entityType -> ReportType.valueOf(entityType.toUpperCase()))
                .collect(Collectors.toList()) :
            ReportType.valueOf(value.toString().toUpperCase());
      }
      if ("frequency".equalsIgnoreCase(field) && value != null) {
        return isList ?
            values.stream()
                .map(frequencyType -> FrequencyType.valueOf(frequencyType.toUpperCase()))
                .collect(Collectors.toList()) :
            FrequencyType.valueOf(value.toString().toUpperCase());
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new InvalidFilterRuleException();
    }
    return value;
  }

  private void verify(String operator, String field, String type, Object value) {

    if (StringUtils.isBlank(operator)
        || StringUtils.isBlank(field)
        || StringUtils.isBlank(type)) {
      log.info(
          "Search job operator {}, fieldName {}, type {} can not be blank",
          operator,
          field,
          type);
      throw new InvalidFilterRuleException();
    }
    if (validOperators.contains(operator)) {
      return;
    }
    if (value == null) {
      log.info("Search job operator {} value can not be blank", operator);
      throw new InvalidFilterRuleException();
    }
    if ("between".equalsIgnoreCase(operator) || "not_between".equalsIgnoreCase(operator)) {
      if (!(value instanceof List)) {
        log.info(
            "Search job operator {} and value {}  is mismatch value must be of list",
            operator,
            value);
        throw new InvalidFilterRuleException();
      }
      List values = (List) value;
      if (values.size() != 2) {
        log.info(
            "Search job operator {} and value {}  is mismatch size is 2 expected",
            operator,
            value);
        throw new InvalidFilterRuleException();
      }
      if (StringUtils.isBlank(String.valueOf(values.get(0)))
          || StringUtils.isBlank(String.valueOf(values.get(1)))) {
        log.info(
            "Search job operator {} and value {}  both value must not be blank",
            operator,
            value);
        throw new InvalidFilterRuleException();
      }
    }
    if ("".equals(value)) {
      log.info("Search job operator {} value {} must not be blank", operator, value);
      throw new InvalidFilterRuleException();
    }
  }

  public Specification<Goal> toSpecification() {
    switch (this.operator) {
      case "equal":
        return GoalSpecification.isFieldEqualTo(this.field, this.value);
      case "not_equal":
        return GoalSpecification.isFieldNotEqualTo(this.field, this.value);
      case "greater":
        return GoalSpecification.isFieldGraterThan(this.field, this.value);
      case "greater_or_equal":
        return GoalSpecification.isFieldGraterThanOrEqualTo(this.field, this.value);
      case "less":
        return GoalSpecification.isFieldLessThan(this.field, this.value);
      case "less_or_equal":
        return GoalSpecification.isFieldLessThanOrEqualTo(this.field, this.value);
      case "between":
        return GoalSpecification.isFieldBetween(this.field, this.type, this.value);
      case "not_between":
        return GoalSpecification.isFieldNotBetween(this.field, this.type, this.value);
      case "in":
        return GoalSpecification.isFieldIn(this.field, this.type, this.value);
      case "not_in":
        return GoalSpecification.isFieldNotIn(this.field, this.type, this.value);
      case "is_not_null":
      case "is_not_empty":
        return GoalSpecification.isFieldNotNull(this.field);
      case "is_null":
      case "is_empty":
        return GoalSpecification.isFieldNull(this.field);
      case "begins_with":
        return GoalSpecification.isFieldBeginsWith(this.field, this.value);
      case "contains":
        return GoalSpecification.isFieldContains(this.field, this.value);
      case "not_contains":
        return GoalSpecification.isFieldNotContains(this.field, this.value);
      case "multi_field":
        return GoalSpecification.isMultiFieldsLike(this.value);
      default:
        throw new InvalidFilterRuleException();
    }
  }
}
