package com.sling.sales.report.core.domain.aggregation.fact.field;

import static java.util.Collections.emptyList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.domain.aggregation.dimension.DateDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.IdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdAssociatedEntityFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.TextDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.UserPropertyIdDimension;
import com.sling.sales.report.core.domain.task.Task;
import com.sling.sales.report.core.domain.task.TaskPriority;
import com.sling.sales.report.core.domain.task.TaskPriority_;
import com.sling.sales.report.core.domain.task.TaskRelatedTo;
import com.sling.sales.report.core.domain.task.TaskRelatedTo_;
import com.sling.sales.report.core.domain.task.TaskStatus;
import com.sling.sales.report.core.domain.task.TaskStatus_;
import com.sling.sales.report.core.domain.task.TaskType;
import com.sling.sales.report.core.domain.task.TaskType_;
import com.sling.sales.report.core.domain.task.Task_;
import com.sling.sales.report.dto.DimensionDetail;
import com.sling.sales.report.security.domain.Team;
import com.sling.sales.report.security.domain.Team_;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.User_;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
class TaskFieldFactory extends FactFieldFactory<Task> {

  private final static Map<String, TaskUserMapping> MAPPED_PRIMARY_PROPERTIES = new HashMap<>() {{
    put("ownerId", new TaskUserMapping(Task_.OWNER, Task_.owner));
    put("assignedTo", new TaskUserMapping(Task_.ASSIGNED_TO, Task_.assignedTo));
    put("createdBy", new TaskUserMapping(Task_.CREATED_BY, Task_.createdBy));
    put("updatedBy", new TaskUserMapping(Task_.UPDATED_BY, Task_.updatedBy));
  }};
  
  private final IdDimension<Task, User> owner = new IdDimension<>(
      "ownerId",
      Task_.OWNER,
      Task_.owner,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());
  private final IdDimension<Task, User> assignedTo = new IdDimension<>(
      "assignedTo",
      Task_.ASSIGNED_TO,
      Task_.assignedTo,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());
  private final IdDimension<Task, User> createdBy = new IdDimension<>(
      "createdBy",
      Task_.CREATED_BY,
      Task_.createdBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());
  private final IdDimension<Task, User> updatedBy = new IdDimension<>(
      "updatedBy",
      Task_.UPDATED_BY,
      Task_.updatedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());
  private final IdDimension<Task, TaskType> taskType = new IdDimension<>(
      "type",
      Task_.TASK_TYPE,
      Task_.taskType,
      join -> join.get(TaskType_.id),
      join -> join.get(TaskType_.name),
      emptyList());
  private final IdDimension<Task, TaskPriority> taskPriority = new IdDimension<>(
      "priority",
      Task_.TASK_PRIORITY,
      Task_.taskPriority,
      join -> join.get(TaskPriority_.id),
      join -> join.get(TaskPriority_.name),
      emptyList());
  private final IdDimension<Task, TaskStatus> taskStatus = new IdDimension<>(
      "status",
      Task_.TASK_STATUS,
      Task_.taskStatus,
      join -> join.get(TaskStatus_.id),
      join -> join.get(TaskStatus_.name),
      emptyList());
  private final NumberFilterDimension<Task, Long> id = new NumberFilterDimension<>(
      "id",
      taskRoot -> taskRoot.get(Task_.id),
      Long::valueOf
  );
  private final TextDimension<Task> name = new TextDimension<>(
      "name",
      root -> root.get(Task_.name),
      emptyList());

  private final ManyIdAssociatedEntityFilterDimension<Task, TaskRelatedTo> associatedLeads = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedLeads",
      Task_.RELATED_TO,
      Task_.relatedTo,
      join -> join.get(TaskRelatedTo_.entityId),
      join -> join.get(TaskRelatedTo_.entityType),
      join -> join.get(TaskRelatedTo_.entityName),
      "LEAD"
  );

  private final ManyIdAssociatedEntityFilterDimension<Task, TaskRelatedTo> associatedContacts = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedContacts",
      Task_.RELATED_TO,
      Task_.relatedTo,
      join -> join.get(TaskRelatedTo_.entityId),
      join -> join.get(TaskRelatedTo_.entityType),
      join -> join.get(TaskRelatedTo_.entityName),
      "CONTACT"
  );

  private final ManyIdAssociatedEntityFilterDimension<Task, TaskRelatedTo> associatedDeals = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedDeals",
      Task_.RELATED_TO,
      Task_.relatedTo,
      join -> join.get(TaskRelatedTo_.entityId),
      join -> join.get(TaskRelatedTo_.entityType),
      join -> join.get(TaskRelatedTo_.entityName),
      "DEAL"
  );

  private final ManyIdAssociatedEntityFilterDimension<Task, TaskRelatedTo> associatedCompanies = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedCompanies",
      Task_.RELATED_TO,
      Task_.relatedTo,
      join -> join.get(TaskRelatedTo_.entityId),
      join -> join.get(TaskRelatedTo_.entityType),
      join -> join.get(TaskRelatedTo_.entityName),
      "COMPANY"
  );

  private final DateDimension<Task> dueDate = new DateDimension<>("dueDate", root -> root.get(Task_.dueDate));
  private final DateDimension<Task> createdAt = new DateDimension<>("createdAt", root -> root.get(Task_.createdAt));
  private final DateDimension<Task> updatedAt = new DateDimension<>("updatedAt", root -> root.get(Task_.updatedAt));
  private final DateDimension<Task> originalDueDate = new DateDimension<>("originalDueDate", root -> root.get(Task_.originalDueDate));
  private final DateDimension<Task> completedAt = new DateDimension<>("completedAt", root -> root.get(Task_.completedAt));

  private final Map<String, GroupByDimension<Task>> GROUP_BY_DIMENSIONS =
      new HashMap() {
        {
          put(assignedTo.getName(), assignedTo);
          put(taskType.getName(), taskType);
          put(taskPriority.getName(), taskPriority);
          put(taskStatus.getName(), taskStatus);
          put(owner.getName(), owner);
          put(createdBy.getName(), createdBy);
          put(updatedBy.getName(), updatedBy);
          put(dueDate.getName(), dueDate);
          put(createdAt.getName(), createdAt);
          put(updatedAt.getName(), updatedAt);
          put(originalDueDate.getName(), originalDueDate);
          put(completedAt.getName(), completedAt);
          put(associatedLeads.getName(), associatedLeads);
          put(associatedContacts.getName(), associatedContacts);
          put(associatedDeals.getName(), associatedDeals);
          put(associatedCompanies.getName(), associatedCompanies);
        }
      };


  private final HashMap<String, FilterDimension<Task>> DIMENSIONS =
      new HashMap<>() {{
        put(id.getName(), id);
        put(name.getName(), name);
        put(owner.getName(), owner);
        put(assignedTo.getName(), assignedTo);
        put(taskType.getName(), taskType);
        put(taskPriority.getName(), taskPriority);
        put(taskStatus.getName(), taskStatus);
        put(createdBy.getName(), createdBy);
        put(updatedBy.getName(), updatedBy);
        put(dueDate.getName(), dueDate);
        put(createdAt.getName(), createdAt);
        put(updatedAt.getName(), updatedAt);
        put(originalDueDate.getName(), originalDueDate);
        put(completedAt.getName(), completedAt);
        put(associatedLeads.getName(), associatedLeads);
        put(associatedContacts.getName(), associatedContacts);
        put(associatedDeals.getName(), associatedDeals);
        put(associatedCompanies.getName(), associatedCompanies);
      }};

  private final List<Metric<Task>> SUPPORTED_METRICS =
      new ArrayList<>() {
        {
          add(new Metric<>(
              "id",
              MetricType.COUNT,
              (root, builder) -> builder.countDistinct(root),
              null));
        }
      };

  @Override
  public DimensionDetail<Task> getDimensionDetail() {
    return new DimensionDetail<>(GROUP_BY_DIMENSIONS, DIMENSIONS, SUPPORTED_METRICS);
  }

  @Override
  public GroupByDimension<Task> createUserPropertyGroupByIdDimension(GroupByField groupByField) {
    TaskUserMapping taskUserMapping = MAPPED_PRIMARY_PROPERTIES.get(groupByField.getPrimaryField());
    if ("teams".equals(groupByField.getProperty())) {
      return new UserPropertyIdDimension<>(
          groupByField.getName(),
          taskUserMapping.getMappedColumnFieldName(),
          taskUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateLeftJoin(join, groupByField.getProperty()).get(Team_.id),
          join -> getOrCreateLeftJoin(join, groupByField.getProperty()).get(Team_.name),
          emptyList(),
          groupByField.getPrimaryField(),
          groupByField.getProperty()
      );
    }
    return null;
  }

  @Override
  public FilterDimension<Task> createUserPropertyFilterIdDimension(Filter filter) {
    TaskUserMapping taskUserMapping = MAPPED_PRIMARY_PROPERTIES.get(filter.getPrimaryField());
    if ("teams".equals(filter.getProperty())) {
      return new UserPropertyIdDimension<>(
          filter.getFieldName(),
          taskUserMapping.getMappedColumnFieldName(),
          taskUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateLeftJoin(join, filter.getProperty()).get(Team_.id),
          join -> getOrCreateLeftJoin(join, filter.getProperty()).get(Team_.name),
          emptyList(),
          filter.getPrimaryField(),
          filter.getProperty()
      );
    }
    return null;
  }

  private Join<User, Team> getOrCreateLeftJoin(Join<Task, User> taskUserJoin, String dimensionName) {
    return taskUserJoin.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<User, Team>) j))
        .findFirst()
        .orElseGet(() -> taskUserJoin.join(User_.teams, JoinType.LEFT));
  }
}
