package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.begins_with;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.contains;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.in;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_empty;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_empty;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_contains;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_in;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.toList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.Format;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.query.criteria.internal.expression.LiteralExpression;
import org.hibernate.query.criteria.internal.predicate.ComparisonPredicate;
import org.springframework.data.jpa.domain.Specification;

public class ManyTextCustomFieldDimension<T extends Fact, V extends EntityDimension> implements FilterDimension<T>, GroupByDimension<T> {

  private final List<Operator> allowedOperators = asList(equal, not_equal, contains, not_contains, in, not_in, is_empty, is_not_empty, begins_with,
      is_null, is_not_null);
  private final String name;
  private final String dimensionName;
  private final SetAttribute<T, V> dimensionField;
  private final Function<Join<T, V>, Path<String>> pathToColumn;
  private final Function<Join<T, V>, Path<Long>> getFieldId;
  private final long fieldId;
  private Format format;

  public ManyTextCustomFieldDimension(
      String name,
      String dimensionName,
      SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<String>> getName,
      Function<Join<T, V>, Path<Long>> getFieldId, long fieldId) {
    this.name = name;
    this.dimensionName = dimensionName;
    this.dimensionField = dimensionField;
    this.pathToColumn = getName;
    this.getFieldId = getFieldId;
    this.fieldId = fieldId;
  }

  @Override
  public ManyTextCustomFieldDimension<T, V> withFormat(Format format) {
    this.format = format;
    return this;
  }

  @Override
  public Format getFormat() {
    return this.format;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    switch (operator) {
      case equal:
        return hasText(String.valueOf(value));
      case not_equal:
        return doesNotHaveText(String.valueOf(value)).or(isDimensionEmpty());
      case contains:
        return containsText(String.valueOf(value));
      case not_contains:
        return doesNotContainText(String.valueOf(value)).or(isDimensionEmpty());
      case in:
        return in(getInParameters(value));
      case not_in:
        return notIn(getInParameters(value)).or(isDimensionEmpty());
      case is_empty:
      case is_null:
        return isDimensionEmpty();
      case is_not_null:
      case is_not_empty:
        return isDimensionPresent();
      case begins_with:
        return beginsWith(String.valueOf(value));
      default:
        return null;
    }
  }

  private Specification<T> isFieldIdNull(Join<T, V> join) {
    return ((root, query, builder) -> builder.isNull(getFieldId.apply(join)));
  }

  private Specification<T> beginsWith(String value) {
    return ((root, query, builder) ->
        builder.like(
            builder.lower(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder))),
            value.toLowerCase() + "%"
        )
    );
  }

  private Specification<T> hasFieldId(Join<T, V> join) {
    return ((root, query, builder) -> builder.equal(getFieldId.apply(join), fieldId));
  }

  private Optional<Specification<T>> getSpecification(Join<T, V> join) {
    return Optional.ofNullable(hasFieldId(join).or(isFieldIdNull(join)));
  }

  private Specification<T> isDimensionPresent() {
    return ((root, query, builder) -> pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)).isNotNull());
  }

  private Specification<T> isDimensionEmpty() {
    return ((root, query, builder) -> pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)).isNull());
  }

  private Specification<T> notIn(List<String> values) {
    return ((root, query, builder) -> {
      Expression<String> jsonbExpression = pathToColumn.apply(getOrCreateLeftJoin(root, query, builder));
      return jsonbExpression.in(values).not();
    });
  }

  private List<String> getInParameters(Object value) {
    if (value instanceof List) {
      return (List<String>) value;
    }
    return Arrays.stream(String.valueOf(value).split(","))
        .map(String::trim)
        .collect(toList());
  }

  private Specification<T> in(List<String> values) {
    return ((root, query, builder) -> {
      Expression<String> jsonbExpression = pathToColumn.apply(getOrCreateLeftJoin(root, query, builder));
      return jsonbExpression.in(values);
    });
  }

  private Specification<T> doesNotContainText(String value) {
    return ((root, query, builder) ->
        builder.notLike(
            builder.lower(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder))),
            "%" + value.toLowerCase() + "%"
        )
    );
  }

  private Specification<T> containsText(String value) {
    return ((root, query, builder) ->
        builder.like(
            builder.lower(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder))),
            "%" + value.toLowerCase() + "%"
        )
    );
  }

  private Specification<T> hasText(String value) {
    return ((root, query, builder) ->
        builder.equal(
            pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)),
            value
        )
    );
  }

  private Specification<T> doesNotHaveText(String value) {
    return ((root, query, builder) ->
        builder.notEqual(
            pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)),
            value
        )
    );
  }

  @Override
  public List<Expression<?>> getGroupByPath(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, String timezone) {
    return singletonList(criteriaBuilder.lower(pathToColumn.apply(createJoin(root, query, criteriaBuilder))));
  }

  @Override
  public Aggregate toResultKey(Tuple tuple, int columnNumber) {
    int nextColumnNumber = columnNumber + 1;
    String nameWithCamelCase = toCamelCase(tuple.get(columnNumber, String.class));
    return new Aggregate(null, nameWithCamelCase, nextColumnNumber, null);
  }

  private String toCamelCase(String name) {
    if (StringUtils.isNotEmpty(name)) {
      return Arrays.stream(StringUtils.splitPreserveAllTokens(name, " "))
          .map(this::capitalize)
          .collect(Collectors.joining(" "));
    }
    return name;
  }

  private String capitalize(String word) {
    return word.isEmpty() ? "" : StringUtils.capitalize(word);
  }

  protected Join<T, V> createJoin(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
    Join<T, V> join = root.join(dimensionField, JoinType.LEFT);
    if (getSpecification(join).isPresent()) {
      join.on(getSpecification(join).get().toPredicate(root, query, builder));
    }
    return join;
  }

  private Join<T, V> getOrCreateLeftJoin(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
    return root.getJoins()
        .stream()
        .filter(this::isFieldIdAlreadyPresent)
        .map(j -> ((Join<T, V>) j))
        .findFirst()
        .orElseGet(() -> createJoin(root, query, builder));
  }

  private boolean isFieldIdAlreadyPresent(Join<T, ?> j) {
    if (ObjectUtils.isEmpty(j.getOn()) || ObjectUtils.isEmpty(j.getOn().getExpressions()) || j.getOn().getExpressions().size() < 2) {
      return false;
    }
    ComparisonPredicate comparisonPredicate = (ComparisonPredicate) j.getOn().getExpressions().get(1);
    LiteralExpression<Long> literalExpression = (LiteralExpression<Long>) comparisonPredicate.getRightHandOperand();
    return literalExpression.getLiteral().equals(this.fieldId);
  }
}
