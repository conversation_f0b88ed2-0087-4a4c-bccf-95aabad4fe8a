package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.in;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_in;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.Format;
import com.sling.sales.report.core.api.response.RecordDetails;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import com.sling.sales.report.core.domain.goal.GoalFieldValue;
import com.sling.sales.report.core.domain.meeting.MeetingInvitee;
import com.sling.sales.report.mq.event.MeetingLookup;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import javax.persistence.metamodel.SetAttribute;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.jpa.domain.Specification;

public class ManyIdMeetingInviteeFilterDimension<T extends Fact, V extends EntityDimension> implements FilterDimension<T>, GroupByDimension<T> {

  private final List<Operator> allowedOperators = asList(equal, not_equal, is_null, is_not_null, in, not_in);
  private final String name;
  private final String dimensionName;
  private final SetAttribute<T, V> dimensionField;
  private final Function<Join<T, V>, Path<Long>> getId;
  private final Function<Join<T, V>, Path<String>> getEntity;
  private final Function<Join<T, V>, Path<String>> getName;
  private final Function<Join<T, V>, Path<String>> getEmail;
  private Format format;

  public ManyIdMeetingInviteeFilterDimension(
      String name,
      String dimensionName,
      SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> getId,
      Function<Join<T, V>, Path<String>> getEntity,
      Function<Join<T, V>, Path<String>> getName,
      Function<Join<T, V>, Path<String>> getEmail) {
    this.name = name;
    this.dimensionName = dimensionName;
    this.dimensionField = dimensionField;
    this.getId = getId;
    this.getEntity = getEntity;
    this.getName = getName;
    this.getEmail = getEmail;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    switch (operator) {
      case equal:
        return isEntityIdAndEntityEqualTo(value);
      case not_equal:
        return isEntityIdAndEntityNotEqualTo(value).or(isNull());
      case is_null:
        return isNull();
      case is_not_null:
        return isNotNull();
      case in:
        return inOrNotInEntityIdsAndEntities(value, in);
      case not_in:
        return inOrNotInEntityIdsAndEntities(value, not_in);
      default:
        return null;
    }
  }

  private Specification<T> isEntityIdAndEntityEqualTo(Object value) {
    MeetingLookup meetingLookup = new ObjectMapper().convertValue(value, MeetingLookup.class);
    return ((root, query, builder) -> {
      var join = getOrCreateJoin(root);
      return builder.and(builder.equal(getId.apply(join), meetingLookup.getId()), builder.equal(getEntity.apply(join), meetingLookup.getEntity()));
    });
  }

  private Specification<T> isEntityIdAndEntityNotEqualTo(Object value) {
    MeetingLookup meetingLookup = new ObjectMapper().convertValue(value, MeetingLookup.class);
    return ((root, query, builder) -> {
      String lookUp = String.join("_", meetingLookup.getEntity(), String.valueOf(meetingLookup.getId()));
      Subquery<Long> subQuery = query.subquery(Long.class);
      Root<MeetingInvitee> subRoot = subQuery.from(MeetingInvitee.class);
      subQuery.select(subRoot.get("meetingId")).distinct(true);
      var stringExpression = builder.concat(builder.concat(subRoot.get("entity"), "_"), subRoot.get("entityId"));
      subQuery.where(builder.equal(stringExpression, lookUp));
      return root.get("id").in(subQuery).not();
    });
  }

  Specification<T> isNull() {
    return ((root, query, builder) -> builder.isNull(getId.apply(getOrCreateJoin(root))));
  }

  Specification<T> isNotNull() {
    return ((root, query, builder) -> builder.isNotNull(getId.apply(getOrCreateJoin(root))));
  }

  private Specification<T> inOrNotInEntityIdsAndEntities(Object value, Operator operator) {
    List<MeetingLookup> meetingLookups = new ObjectMapper().convertValue(value, new TypeReference<>() {
      @Override
      public Type getType() {
        return super.getType();
      }
    });

    List<String> lookups = meetingLookups
        .stream()
        .map(meetingLookup -> String.join("_", meetingLookup.getEntity(), String.valueOf(meetingLookup.getId())))
        .collect(Collectors.toList());

    return (root, query, builder) -> {
      if (Operator.not_in == operator) {
        Subquery<Long> subQuery = query.subquery(Long.class);
        Root<MeetingInvitee> subRoot = subQuery.from(MeetingInvitee.class);
        subQuery.select(subRoot.get("meetingId")).distinct(true);
        var stringExpressions = builder.concat(builder.concat(subRoot.get("entity"), "_"), subRoot.get("entityId"));
        subQuery.where(stringExpressions.in(lookups));
        return root.get("id").in(subQuery).not();
      }
      var join = getOrCreateJoin(root);
      Expression<String> stringExpressions = builder.concat(builder.concat(getEntity.apply(join), "_"), getId.apply(join).as(String.class));
      return stringExpressions.in(lookups);
    };
  }

  protected Join<T, V> getOrCreateJoin(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<T, V>) j))
        .findFirst()
        .orElseGet(() -> root.join(dimensionField, JoinType.INNER));
  }

  @Override
  public List<Expression<?>> getGroupByPath(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, String timezone) {
    Join<T, V> join = getOrCreateJoin(root);
    return asList(getId.apply(join), getName.apply(join), getEntity.apply(join), getEmail.apply(join));
  }

  @Override
  public Aggregate toResultKey(Tuple tuple, int columnNumber) {
    int nextColumnNumber = columnNumber + 4;
    RecordDetails recordDetails = new RecordDetails(tuple.get(columnNumber + 2, String.class), tuple.get(columnNumber + 3, String.class));
    return new Aggregate(tuple.get(columnNumber, Long.class), tuple.get(columnNumber + 1, String.class), nextColumnNumber, null)
        .withRecordDetails(recordDetails);
  }

  @Override
  public ManyIdMeetingInviteeFilterDimension<T, V> withFormat(Format format) {
    this.format = format;
    return this;
  }

  @Override
  public Format getFormat() {
    return this.format;
  }

  @Override
  public GoalGroupByDimensionDetail<T> getGoalGroupByDimensionDetail(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder,
      String timezone,
      GoalDetail goalDetail) {
    Join<T, V> join = getOrCreateJoin(root);
    Expression<Long> idExpression = getId.apply(join);
    return new GoalGroupByDimensionDetail<>(asList(idExpression, getName.apply(join)),
        inFieldValues(goalDetail.getFieldValues(), idExpression, join),
        false);
  }

  private Specification<T> inFieldValues(Set<Long> values, Expression<Long> idExpression, Join<T, V> join) {
    return ((root, query, builder) -> builder.and(idExpression.in(values), builder.equal(getEntity.apply(join), "user")));
  }

  @Override
  public GoalAggregate toGoalResultKey(Tuple tuple, int columnNumber, Map<Long, GoalFieldValue> goalFieldValuesByFieldValueId) {
    int nextColumnNumber = columnNumber + 2;
    Long fieldValueId = tuple.get(columnNumber, Long.class);
    Number goal = getGoalValue(fieldValueId, goalFieldValuesByFieldValueId);
    long goalFieldValueId = getFieldValueId(fieldValueId, goalFieldValuesByFieldValueId);
    return new GoalAggregate(fieldValueId, tuple.get(columnNumber + 1, String.class),
        goal, goalFieldValueId, nextColumnNumber);
  }

  private long getFieldValueId(Long fieldValueId, Map<Long, GoalFieldValue> goalFieldValuesByFieldValueId) {
    GoalFieldValue goalFieldValue = goalFieldValuesByFieldValueId.getOrDefault(fieldValueId, null);
    if (ObjectUtils.isEmpty(goalFieldValue)) {
      return 0L;
    }
    return goalFieldValue.getId();
  }

  private Number getGoalValue(Long fieldValueId, Map<Long, GoalFieldValue> goalFieldValuesByFieldValueId) {
    GoalFieldValue goalFieldValue = goalFieldValuesByFieldValueId.getOrDefault(fieldValueId, null);
    if (ObjectUtils.isEmpty(goalFieldValue)) {
      return 0;
    }
    return goalFieldValue.getValue();
  }

}
