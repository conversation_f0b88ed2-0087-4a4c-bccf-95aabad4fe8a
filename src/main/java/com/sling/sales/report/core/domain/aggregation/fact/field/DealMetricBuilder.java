package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.deal.Deal;
import com.sling.sales.report.core.domain.deal.DealProduct;
import com.sling.sales.report.core.domain.deal.DealProduct_;
import com.sling.sales.report.core.domain.deal.Deal_;
import com.sling.sales.report.core.domain.deal.Discount.Type;
import com.sling.sales.report.core.domain.deal.Discount_;
import com.sling.sales.report.core.domain.deal.Money;
import com.sling.sales.report.core.domain.deal.Money_;
import com.sling.sales.report.core.domain.deal.ProductMoney_;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

@Service
public class DealMetricBuilder {

  public static List<Metric<Deal>> buildDealMetrics(Long currencyId) {
    return new ArrayList<>() {{
      add(new Metric<>(
          "id",
          MetricType.COUNT,
          (root, builder) -> builder.countDistinct(root),
          null));

      add(new Metric<>(
          "estimatedValue",
          MetricType.SUM,
          (root, builder) -> {
            Path<Money> moneyPath = root.get(Deal_.estimatedValue);
            Path<Double> valuePath = moneyPath.get(Money_.baseAmount);
            Expression<Double> valueWithAppliedExchangeRate = builder.prod(valuePath, applyCurrencyExchangeRate(moneyPath, builder, currencyId));
            return builder.sum(valueWithAppliedExchangeRate);
          },
          (root, builder) -> {
            Path<Money> moneyPath = root.get(Deal_.estimatedValue);
            Join<Deal, DealProduct> join = getOrCreateLeftJoin(root);
            Path<Double> valuePath = join.get(DealProduct_.price).get(ProductMoney_.baseAmount);
            Expression<Double> valueWithAppliedExchangeRate = builder.prod(valuePath, applyCurrencyExchangeRate(moneyPath, builder, currencyId));
            Expression<? extends Number> multiplicationResult = builder
                .prod(valueWithAppliedExchangeRate, join.get(DealProduct_.quantityInDecimal));
            return builder.sum(multiplicationResult);
          }));

      add(new Metric<>(
          "estimatedValue",
          MetricType.AVERAGE,
          (root, builder) -> {
            Path<Money> moneyPath = root.get(Deal_.estimatedValue);
            Path<Double> valuePath = moneyPath.get(Money_.baseAmount);
            Expression<Double> valueWithAppliedExchangeRate = builder.prod(valuePath, applyCurrencyExchangeRate(moneyPath, builder, currencyId));
            return builder.avg(valueWithAppliedExchangeRate);
          },
          (root, builder) -> {
            Path<Money> moneyPath = root.get(Deal_.estimatedValue);
            Join<Deal, DealProduct> join = getOrCreateLeftJoin(root);
            Path<Double> valuePath = join.get(DealProduct_.price).get(ProductMoney_.baseAmount);
            Expression<Double> valueWithAppliedExchangeRate = builder.prod(valuePath, applyCurrencyExchangeRate(moneyPath, builder, currencyId));
            Expression<? extends Number> multiplicationResult = builder
                .prod(valueWithAppliedExchangeRate, join.get(DealProduct_.quantityInDecimal));
            return builder.avg(multiplicationResult);
          }));

      add(new Metric<>(
          "actualValue",
          MetricType.SUM,
          (root, builder) -> {
            Path<Money> moneyPath = root.get(Deal_.actualValue);
            Path<Double> valuePath = moneyPath.get(Money_.baseAmount);
            Expression<Double> valueWithAppliedExchangeRate = builder.prod(valuePath, applyCurrencyExchangeRate(moneyPath, builder, currencyId));
            return builder.sum(valueWithAppliedExchangeRate);
          },
          (root, builder) -> {
            Path<Money> moneyPath = root.get(Deal_.actualValue);
            Join<Deal, DealProduct> join = getOrCreateLeftJoin(root);
            Path<Double> valuePath = join.get(DealProduct_.price).get(ProductMoney_.baseAmount);
            Expression<Double> valueWithAppliedExchangeRate = builder.prod(valuePath, applyCurrencyExchangeRate(moneyPath, builder, currencyId));
            var discountValuePath = join.get(DealProduct_.discount).get(Discount_.value);
            Expression<? extends Number> multiplicationResult = builder
                .prod(valueWithAppliedExchangeRate, join.get(DealProduct_.quantityInDecimal));
            var discount = builder.prod(builder.quot(multiplicationResult, 100), discountValuePath);
            return builder.sum(
                builder.<Number>selectCase()
                    .when(builder.equal(join.get(DealProduct_.discount).get(Discount_.type), Type.FIXED),
                        builder.diff(multiplicationResult, discountValuePath))
                    .otherwise(builder.diff(multiplicationResult, discount))
            );
          }));

      add(new Metric<>(
          "actualValue",
          MetricType.AVERAGE,
          (root, builder) -> {
            Path<Money> moneyPath = root.get(Deal_.actualValue);
            Path<Double> valuePath = moneyPath.get(Money_.baseAmount);
            Expression<Double> valueWithAppliedExchangeRate = builder.prod(valuePath, applyCurrencyExchangeRate(moneyPath, builder, currencyId));
            return builder.avg(valueWithAppliedExchangeRate);
          },
          (root, builder) -> {
            Path<Money> moneyPath = root.get(Deal_.actualValue);
            Join<Deal, DealProduct> join = getOrCreateLeftJoin(root);
            Path<Double> valuePath = join.get(DealProduct_.price).get(ProductMoney_.baseAmount);
            Expression<Double> valueWithAppliedExchangeRate = builder.prod(valuePath, applyCurrencyExchangeRate(moneyPath, builder, currencyId));
            var discountValuePath = join.get(DealProduct_.discount).get(Discount_.value);
            Expression<? extends Number> multiplicationResult = builder
                .prod(valueWithAppliedExchangeRate, join.get(DealProduct_.quantityInDecimal));
            var discount = builder.prod(builder.quot(multiplicationResult, 100), discountValuePath);
            return builder.avg(
                builder.<Number>selectCase()
                    .when(builder.equal(join.get(DealProduct_.discount).get(Discount_.type), Type.FIXED),
                        builder.diff(multiplicationResult, discountValuePath))
                    .otherwise(builder.diff(multiplicationResult, discount))
            );
          }));

      add(new Metric<>(
          "numberOfProducts",
          MetricType.COUNT,
          (root, builder) -> {
            Join<Deal, DealProduct> join = getOrCreateLeftJoin(root);
            return builder.sum(join.get(DealProduct_.quantityInDecimal));
          },
          null));
      add(new Metric<>(
          "score",
          MetricType.SUM,
          (root, builder) -> builder.sum(root.get(Deal_.score)),
          null));
      add(new Metric<>(
          "score",
          MetricType.AVERAGE,
          (root, builder) -> builder.avg(root.get(Deal_.score)),
          null));
    }};

  }

  private static Join<Deal, DealProduct> getOrCreateLeftJoin(Root<Deal> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(Deal_.PRODUCTS))
        .map(j -> ((Join<Deal, DealProduct>) j))
        .findFirst()
        .orElseGet(() -> root.join(Deal_.products, JoinType.LEFT));
  }

  private static Expression<Double> applyCurrencyExchangeRate(Path<Money> moneyPath, CriteriaBuilder criteriaBuilder, Long currencyId) {
    String currencyIdInString = ObjectUtils.isEmpty(currencyId) ? "currency" : String.valueOf(currencyId);
    Expression<Double> exchangeRate = criteriaBuilder.coalesce(
        criteriaBuilder.function("jsonb_extract_path_text", String.class, moneyPath.get(Money_.exchangeRate),
            criteriaBuilder.literal(currencyIdInString)), "1.0").as(Double.class);
    return criteriaBuilder.coalesce(exchangeRate, 1.0);
  }
}
