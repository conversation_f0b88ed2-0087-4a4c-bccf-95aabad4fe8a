package com.sling.sales.report.core.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.http.ResponseEntity.ok;

import com.sling.sales.report.config.api.response.EntityGoalConfiguration;
import com.sling.sales.report.config.api.response.EntityGoalListConfiguration;
import com.sling.sales.report.core.api.request.CreateGoalRequest;
import com.sling.sales.report.core.api.request.FilterRequest;
import com.sling.sales.report.core.api.request.GoalAggregationRequest;
import com.sling.sales.report.core.api.request.GoalSummaryRequest;
import com.sling.sales.report.core.api.request.NudgeUserRequest;
import com.sling.sales.report.core.api.request.UpdateGoalRequest;
import com.sling.sales.report.core.api.response.GoalAchievementResponse;
import com.sling.sales.report.core.api.response.GoalAggregateResponse;
import com.sling.sales.report.core.api.response.GoalResponse;
import com.sling.sales.report.core.api.response.GoalSummary;
import com.sling.sales.report.core.api.response.IdName;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.report.ReportType;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Extension;
import io.swagger.annotations.ExtensionProperty;
import java.util.List;
import java.util.Optional;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping(value = "/v1/reports")
public class GoalController {

  private final GoalService goalService;
  private final GoalConfigCacheService goalConfigCacheService;

  @Autowired
  public GoalController(GoalService goalService, GoalConfigCacheService goalConfigCacheService) {
    this.goalService = goalService;
    this.goalConfigCacheService = goalConfigCacheService;
  }

  @ApiOperation(value = "Create goal", code = 201, response = GoalSummary.class,
      extensions = {
          @Extension(name = "access-policy", properties = {
              @ExtensionProperty(name = "action", value = "create"),
              @ExtensionProperty(name = "policy", value = "goals"),
              @ExtensionProperty(name = "resource", value = "goal")
          })
      }
  )
  @PostMapping(
      value = "goals",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public Mono<ResponseEntity<GoalSummary>> createGoal(@RequestBody @Valid CreateGoalRequest createGoalRequest) {
    return goalService.createGoal(createGoalRequest)
        .map(ResponseEntity::ok);
  }

  @ApiOperation(value = "Update existing goal", response = GoalResponse.class)
  @PutMapping(
      value = "goals/{id}",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public Mono<ResponseEntity<GoalResponse>> updateGoal(@PathVariable("id") long goalId,
      @RequestBody @Valid UpdateGoalRequest updateGoalRequest) {
    return goalService
        .updateGoal(updateGoalRequest, goalId)
        .map(ResponseEntity::ok);
  }

  @ApiOperation(
      value = "Get entity goal configurations",
      code = 200,
      response = EntityGoalConfiguration[].class)
  @GetMapping(value = "goals/config", produces = APPLICATION_JSON_VALUE)
  public List<EntityGoalConfiguration<?>> getAllGoalConfigurations() {
    return goalConfigCacheService.getAllGoalConfigurations();
  }

  @ApiOperation(
      value = "Get entity deal goal configuration",
      code = 200,
      response = EntityGoalConfiguration.class)
  @GetMapping(value = "goals/config/deals", produces = APPLICATION_JSON_VALUE)
  public Optional<EntityGoalConfiguration<? extends Fact>> getDealGoalConfiguration() {
    return goalConfigCacheService.getGoalConfigurationByEntity(ReportType.DEAL);
  }

  @ApiOperation(
      value = "Get entity meeting goal configuration",
      code = 200,
      response = EntityGoalConfiguration.class)
  @GetMapping(value = "goals/config/meetings", produces = APPLICATION_JSON_VALUE)
  public Optional<EntityGoalConfiguration<? extends Fact>> getMeetingGoalConfiguration() {
    return goalConfigCacheService.getGoalConfigurationByEntity(ReportType.MEETING);
  }

  @ApiOperation(
      value = "Get entity lead goal configuration",
      code = 200,
      response = EntityGoalConfiguration.class)
  @GetMapping(value = "goals/config/leads", produces = APPLICATION_JSON_VALUE)
  public Optional<EntityGoalConfiguration<? extends Fact>> getLeadGoalConfiguration() {
    return goalConfigCacheService.getGoalConfigurationByEntity(ReportType.LEAD);
  }

  @ApiOperation(
      value = "Get entity call goal configuration",
      code = 200,
      response = EntityGoalConfiguration.class)
  @GetMapping(value = "goals/config/calls", produces = APPLICATION_JSON_VALUE)
  public Optional<EntityGoalConfiguration<? extends Fact>> getCallGoalConfiguration() {
    return goalConfigCacheService.getGoalConfigurationByEntity(ReportType.CALL);
  }

  @ApiOperation(value = "Get existing goal", response = GoalResponse.class)
  @GetMapping(
      value = "goals/{id}",
      produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<GoalResponse> getGoal(@PathVariable("id") long goalId) {
    return ok(goalService.getGoalById(goalId));
  }

  @ApiOperation(value = "Search Goals", response = GoalResponse.class)
  @PostMapping(
      value = "goals/search",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public Page<GoalResponse> search(@RequestBody(required = false) FilterRequest filterRequest, Pageable pageable) {
    return goalService.search(Optional.ofNullable(filterRequest), pageable);
  }

  @ApiOperation(value = "Get goals by user", response = GoalResponse.class)
  @GetMapping(value = "goals", produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<List<IdName>> getGoalsByUser(@RequestParam(value = "entityType",required = false) String entityType) {
    return ResponseEntity.ok(goalService.getGoalsByUser(entityType));
  }

  @ApiOperation(value = "Get goal deal report", response = GoalAggregateResponse.class)
  @PostMapping(
      value = "deals/goals/{id}",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<GoalAggregateResponse> getDealReportForGoal(@PathVariable("id") long goalId,
      @RequestBody GoalAggregationRequest goalAggregationRequest,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "prorated", required = false, defaultValue = "false") boolean prorated,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    return ok(goalService.goalAggregateResultsForDeal(goalId, goalAggregationRequest, timezone, prorated, currencyId));
  }

  @ApiOperation(value = "Get goal meeting report", response = GoalAggregateResponse.class)
  @PostMapping(
      value = "meetings/goals/{id}",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<GoalAggregateResponse> getMeetingReportForGoal(@PathVariable("id") long goalId,
      @RequestBody GoalAggregationRequest goalAggregationRequest,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "prorated", required = false, defaultValue = "false") boolean prorated,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    return ok(goalService.goalAggregateResultsForMeeting(goalId, goalAggregationRequest, timezone, prorated, currencyId));
  }

  @ApiOperation(value = "Get goal lead report", response = GoalAggregateResponse.class)
  @PostMapping(
      value = "leads/goals/{id}",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<GoalAggregateResponse> getLeadReportForGoal(@PathVariable("id") long goalId,
      @RequestBody GoalAggregationRequest goalAggregationRequest,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "prorated", required = false, defaultValue = "false") boolean prorated,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    return ok(goalService.goalAggregateResultsForLead(goalId, goalAggregationRequest, timezone, prorated, currencyId));
  }

  @ApiOperation(value = "Get goal call report", response = GoalAggregateResponse.class)
  @PostMapping(
      value = "calls/goals/{id}",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<GoalAggregateResponse> getCallReportForGoal(@PathVariable("id") long goalId,
      @RequestBody GoalAggregationRequest goalAggregationRequest,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "prorated", required = false, defaultValue = "false") boolean prorated,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    return ok(goalService.goalAggregateResultsForCall(goalId, goalAggregationRequest, timezone, prorated, currencyId));
  }

  @ApiOperation(value = "Activate existing goal by id",
      extensions = {
          @Extension(name = "access-policy", properties = {
              @ExtensionProperty(name = "action", value = "activate"),
              @ExtensionProperty(name = "policy", value = "goals"),
              @ExtensionProperty(name = "resource", value = "goal")
          })
      }
  )
  @PutMapping(value = "goals/{id}/activate", produces = APPLICATION_JSON_VALUE)
  public Mono<Boolean> activate(@PathVariable("id") long goalId) {
    return goalService.activate(goalId);
  }

  @ApiOperation(value = "Deactivate existing goal by id")
  @PutMapping(value = "goals/{id}/deactivate", produces = APPLICATION_JSON_VALUE)
  public Mono<Boolean> deactivate(@PathVariable("id") long goalId) {
    return goalService.deactivate(goalId);
  }

  @ApiOperation(
      value = "Get list config by entity type",
      code = 200,
      response = EntityGoalListConfiguration.class)
  @GetMapping(value = "goals/list/{entityTypePlural}/config", produces = APPLICATION_JSON_VALUE)
  public Optional<EntityGoalListConfiguration> getGoalListingConfigurationByEntity(@PathVariable("entityTypePlural") String entityTypePlural) {
    return goalConfigCacheService.getGoalListingConfigurationByEntity1(entityTypePlural);
  }

  @ApiOperation(
      value = "Get current user's achievement for given goal id",
      code = 200,
      response = GoalAchievementResponse.class)
  @GetMapping(value = "goals/{id}/achievement", produces = APPLICATION_JSON_VALUE)
  public GoalAchievementResponse getAchievementForGoalId(@PathVariable("id") Long goalId, @RequestParam(value = "timezone") String timezone) {
    return goalService.getMyGoalAchievement(goalId, timezone);
  }

  @ApiOperation(
      value = "get goals summaries",
      code = 200,
      response = GoalAchievementResponse.class)
  @PostMapping(value = "goals/summary", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public List<GoalAchievementResponse> getGoalsSummaries(@RequestBody GoalSummaryRequest goalSummaryRequest,
      @RequestParam(value = "timezone", defaultValue = "Asia/Calcutta", required = false) String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId) {
    return goalService.getGoalsSummaries(goalSummaryRequest, timezone);
  }

  @ApiOperation(
      value = "nudge user to complete the goal",
      code= 200)
  @PostMapping(value = "/goals/{id}/nudge-user", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public void nudgeUserToCompleteGoal(@PathVariable("id") long goalId,@RequestBody NudgeUserRequest nudgeUserRequest){
    goalService.nudgeUser(goalId,nudgeUserRequest);
  }
}
