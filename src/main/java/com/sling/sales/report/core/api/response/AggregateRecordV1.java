package com.sling.sales.report.core.api.response;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

@Getter
public class AggregateRecordV1 {

  private final Long id;
  private final String name;
  private final Number value;
  private List<AggregateRecordV1> dimensions = new ArrayList<>();

  public AggregateRecordV1(Long id, String name, Number value) {
    this.id = id;
    this.name = name;
    this.value = value;
  }

  public void addDimension(AggregateRecordV1 aggregateRecordV1) {
    this.dimensions.add(aggregateRecordV1);
  }

  public void addAllDimensions(List<AggregateRecordV1> aggregateRecordV1List) {
    this.dimensions.addAll(aggregateRecordV1List);
  }
}

