package com.sling.sales.report.core.domain.task;

import static javax.persistence.FetchType.EAGER;
import static javax.persistence.FetchType.LAZY;

import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.security.domain.User;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

@Entity
@Getter
@Setter
@TypeDefs({@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)})
public class Task implements Fact {

  @Id
  private Long id;

  private long tenantId;

  private String name;

  private Date dueDate;

  @ManyToOne(fetch = EAGER)
  @JoinColumn(name = "assigned_to")
  private User assignedTo;

  @ManyToOne(fetch = EAGER)
  @JoinColumn(name = "owner")
  private User owner;

  @ManyToOne(fetch = EAGER)
  @JoinColumn(name = "created_by")
  private User createdBy;

  @ManyToOne(fetch = EAGER)
  @JoinColumn(name = "updated_by")
  private User updatedBy;

  private Date createdAt;

  private Date updatedAt;

  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  @NotNull
  private TaskEventData eventPayload;

  @ManyToOne(fetch = EAGER)
  @JoinColumn(name = "task_type")
  private TaskType taskType;

  @ManyToOne(fetch = EAGER)
  @JoinColumn(name = "task_status")
  private TaskStatus taskStatus;

  @ManyToOne(fetch = EAGER)
  @JoinColumn(name = "task_priority")
  private TaskPriority taskPriority;

  private Date originalDueDate;
  private Date completedAt;
  private boolean deleted;

  @OneToMany(fetch = LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
  @Setter(AccessLevel.NONE)
  @JoinColumn(name = "task_id")
  private Set<TaskRelatedTo> relatedTo = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, mappedBy = "task", orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<TaskCustomPicklistValue> taskPicklistValues = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, mappedBy = "task", orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<TaskCustomTextValue> taskCustomTextValues = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, mappedBy = "task", cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<TaskCustomNumberValue> taskCustomNumberValues = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, mappedBy = "task", cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<TaskCustomDatePickerValue> taskCustomDatePickerValues = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, mappedBy = "task", cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<TaskCustomDatetimePickerValue> taskCustomDatetimePickerValues = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, mappedBy = "task", cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<TaskCustomCheckboxValue> taskCustomCheckboxValues = new HashSet<>();

  public void addTaskRelatedTo(Set<TaskRelatedTo> relatedTo) {
    this.relatedTo.clear();
    this.relatedTo.addAll(relatedTo);
  }

  public void addTaskPicklistValues(Set<TaskCustomPicklistValue> taskPicklistValues) {
    this.taskPicklistValues.clear();
    this.taskPicklistValues.addAll(taskPicklistValues);
  }

  public Set<TaskCustomPicklistValue> getTaskPicklistValues() {
    return Collections.unmodifiableSet(this.taskPicklistValues);
  }

  public void addTaskCustomTextValues(Set<TaskCustomTextValue> taskCustomTextValues) {
    this.taskCustomTextValues.clear();
    this.taskCustomTextValues.addAll(taskCustomTextValues);
  }

  public Set<TaskCustomTextValue> getTaskCustomTextValues() {
    return Collections.unmodifiableSet(this.taskCustomTextValues);
  }

  public void addTaskCustomNumberValues(Set<TaskCustomNumberValue> taskCustomNumberValues) {
    this.taskCustomNumberValues.clear();
    this.taskCustomNumberValues.addAll(taskCustomNumberValues);
  }

  public Set<TaskCustomNumberValue> getTaskCustomNumberValues() {
    return Collections.unmodifiableSet(this.taskCustomNumberValues);
  }

  public void addTaskCustomDatePickerValues(Set<TaskCustomDatePickerValue> taskCustomDatePickerValues) {
    this.taskCustomDatePickerValues.clear();
    this.taskCustomDatePickerValues.addAll(taskCustomDatePickerValues);
  }

  public Set<TaskCustomDatePickerValue> getTaskCustomDatePickerValues() {
    return Collections.unmodifiableSet(this.taskCustomDatePickerValues);
  }

  public void addTaskCustomDatetimePickerValues(Set<TaskCustomDatetimePickerValue> taskCustomDatetimePickerValues) {
    this.taskCustomDatetimePickerValues.clear();
    this.taskCustomDatetimePickerValues.addAll(taskCustomDatetimePickerValues);
  }

  public Set<TaskCustomDatetimePickerValue> getTaskCustomDatetimePickerValues() {
    return Collections.unmodifiableSet(this.taskCustomDatetimePickerValues);
  }

  public void addTaskCustomCheckboxValues(Set<TaskCustomCheckboxValue> taskCustomCheckboxValues) {
    this.taskCustomCheckboxValues.clear();
    this.taskCustomCheckboxValues.addAll(taskCustomCheckboxValues);
  }

  public Set<TaskCustomCheckboxValue> getTaskCustomCheckboxValues() {
    return Collections.unmodifiableSet(this.taskCustomCheckboxValues);
  }
}
