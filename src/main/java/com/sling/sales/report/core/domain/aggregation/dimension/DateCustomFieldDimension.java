package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.DateTimeParser.parseDate;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.after_current_date_and_time;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.before_current_date_and_time;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.between;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.current_month;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.current_quarter;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.current_week;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.current_year;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.greater;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.greater_or_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_fifteen_days;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_month;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_quarter;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_seven_days;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_thirty_days;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_week;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_year;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.less;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.less_or_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.month_to_date;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_fifteen_days;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_month;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_quarter;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_seven_days;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_thirty_days;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_week;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_year;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_between;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.quarter_to_date;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.today;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.tomorrow;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.week_to_date;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.year_to_date;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.yesterday;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.Format;
import com.sling.sales.report.core.domain.aggregation.dimension.RollingDateFilter.RollingDate;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.query.criteria.internal.expression.LiteralExpression;
import org.hibernate.query.criteria.internal.predicate.ComparisonPredicate;
import org.springframework.data.jpa.domain.Specification;

public class DateCustomFieldDimension<T extends Fact, V extends EntityDimension> implements FilterDimension<T>, GroupByDimension<T> {

  private final List<Operator> allowedOperators =
      asList(greater, greater_or_equal, less, less_or_equal, between, not_between, before_current_date_and_time, after_current_date_and_time, today,
          current_week, last_week, current_month, last_month,
          current_quarter, last_quarter, current_year, last_year, next_week, next_month, next_quarter, next_year, last_seven_days, next_seven_days,
          last_fifteen_days, next_fifteen_days, last_thirty_days, next_thirty_days, week_to_date, month_to_date, quarter_to_date, year_to_date,
          is_null, is_not_null, yesterday, tomorrow);

  private final String name;
  private final String dimensionName;
  private final SetAttribute<T, V> dimensionField;
  private final Function<Join<T, V>, Path<Date>> pathToColumn;
  private final Function<Join<T, V>, Path<Long>> getFieldId;
  private final long fieldId;
  private Format format;

  public DateCustomFieldDimension(String name, String dimensionName, SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Date>> pathToColumn,
      Function<Join<T, V>, Path<Long>> getFieldId, long fieldId) {
    this.name = name;
    this.dimensionName = dimensionName;
    this.dimensionField = dimensionField;
    this.pathToColumn = pathToColumn;
    this.getFieldId = getFieldId;
    this.fieldId = fieldId;
  }

  @Override
  public DateCustomFieldDimension<T, V> withFormat(Format format) {
    this.format = format;
    return this;
  }

  @Override
  public Format getFormat() {
    return this.format;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return this.allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  @Override
  public String getName() {
    return this.name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    return getSpecification(operator, value, factFilter.getTimezone(), filter.getFrom(), filter.getTo());
  }

  private Specification<T> getSpecification(Operator operator, Object value, String timezone, String from, String to) {
    switch (operator) {
      case greater:
        return hasDateGreaterThan(value, timezone);
      case greater_or_equal:
        return hasDateGreaterThanOrEqual(value, timezone);
      case less:
        return hasDateLesserThan(value, timezone);
      case less_or_equal:
        return hasDateLesserThanOrEqual(value, timezone);
      case between:
        return hasDateInBetween(value, from, to, timezone);
      case not_between:
        return hasDateNotInBetween(value, from, to, timezone);
      case is_null:
        return isNull();
      case is_not_null:
        return isNotNull();

      case before_current_date_and_time:
      case after_current_date_and_time:
      case today:
      case current_week:
      case last_week:
      case current_month:
      case last_month:
      case current_quarter:
      case last_quarter:
      case current_year:
      case last_year:
      case next_week:
      case next_month:
      case next_quarter:
      case next_year:
      case last_seven_days:
      case next_seven_days:
      case last_fifteen_days:
      case next_fifteen_days:
      case last_thirty_days:
      case next_thirty_days:
      case week_to_date:
      case month_to_date:
      case quarter_to_date:
      case year_to_date:
      case yesterday:
      case tomorrow:
        RollingDate rollingDate = new RollingDateFilter().from(operator, Instant.now(), ZoneId.of(String.valueOf(timezone)), from, to);
        return hasDateInBetween(rollingDate.getStartDate(), rollingDate.getEndDate());

      default:
        return null;
    }
  }

  private Specification<T> hasFieldId(Join<T, V> join) {
    return ((root, query, builder) -> builder.equal(getFieldId.apply(join), fieldId));
  }

  private Specification<T> isFieldIdNull(Join<T, V> join) {
    return ((root, query, builder) -> builder.isNull(getFieldId.apply(join)));
  }

  private Optional<Specification<T>> getSpecification(Join<T, V> join) {
    return Optional.ofNullable(hasFieldId(join).or(isFieldIdNull(join)));
  }

  Specification<T> hasDateGreaterThan(Object value, String timezone) {
    var date = parseDate(value);
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) ->
        builder.greaterThan(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), date));
  }

  Specification<T> hasDateGreaterThanOrEqual(Object value, String timezone) {
    var date = parseDate(value);
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) ->
        builder.greaterThanOrEqualTo(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), date));
  }

  Specification<T> hasDateLesserThan(Object value, String timezone) {
    var date = parseDate(value);
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) ->
        builder.lessThan(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), date));
  }

  Specification<T> hasDateLesserThanOrEqual(Object value, String timezone) {
    var date = parseDate(value);
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) ->
        builder.lessThanOrEqualTo(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), date));
  }

  Specification<T> hasDateInBetween(Object valuesRange, String from, String to, String timezone) {
    var boundedValues = new BoundedValue(valuesRange, from, to, timezone);
    return hasDateInBetween(boundedValues.lowerBound, boundedValues.upperBound);
  }

  Specification<T> isNull() {
    return ((root, query, builder) -> builder.isNull(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder))));
  }

  Specification<T> isNotNull() {
    return ((root, query, builder) -> builder.isNotNull(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder))));
  }

  Specification<T> hasDateInBetween(Date startDate, Date endDate) {
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) ->
        (startDate == null) ? builder.lessThan(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), endDate) :
            (endDate == null) ? builder.greaterThanOrEqualTo(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), startDate) :
                builder.between(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), startDate, endDate));

  }

  Specification<T> hasDateNotInBetween(Object valuesRange, String from, String to, String timezone) {
    var boundedValues = new BoundedValue(valuesRange, from, to, timezone);
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) ->
        builder
            .between(
                pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), boundedValues.getLowerBound(), boundedValues.getUpperBound())
            .not());
  }

  @Override
  public List<Expression<?>> getGroupByPath(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, String timezone) {
    Join<T, V> join = createJoin(root, query, criteriaBuilder);
    if (ObjectUtils.isEmpty(format)) {
      return Collections.singletonList(join.get("value"));
    }
    var dateInUtcTimezone = criteriaBuilder.function("timezone", Date.class, criteriaBuilder.literal("UTC"), join.get("value"));
    var dateInUserTimeZone = criteriaBuilder.function("timezone", Date.class, criteriaBuilder.literal(timezone), dateInUtcTimezone);
    var toChar = criteriaBuilder.function("to_char", String.class, dateInUserTimeZone, criteriaBuilder.literal(format.getRangePattern()));
    var toDate = criteriaBuilder.function("to_date", Date.class, toChar, criteriaBuilder.literal(format.getRangePattern()));
    return List.of(toChar, toDate);
  }

  @Override
  public Aggregate toResultKey(Tuple tuple, int columnNumber) {
    if (ObjectUtils.isEmpty(format)) {
      return new Aggregate(null, tuple.get(columnNumber, String.class), columnNumber + 1, null);
    }
    return new Aggregate(null, tuple.get(columnNumber, String.class), columnNumber + 2, tuple.get(columnNumber + 1, Date.class));
  }

  protected Join<T, V> createJoin(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
    Join<T, V> join = root.join(dimensionField, JoinType.LEFT);
    if (getSpecification(join).isPresent()) {
      join.on(getSpecification(join).get().toPredicate(root, query, builder));
    }
    return join;
  }

  private Join<T, V> getOrCreateLeftJoin(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
    return root.getJoins()
        .stream()
        .filter(this::isFieldIdAlreadyPresent)
        .map(j -> ((Join<T, V>) j))
        .findFirst()
        .orElseGet(() -> createJoin(root, query, builder));
  }

  private boolean isFieldIdAlreadyPresent(Join<T, ?> j) {
    if (ObjectUtils.isEmpty(j.getOn()) || ObjectUtils.isEmpty(j.getOn().getExpressions()) || j.getOn().getExpressions().size() < 2) {
      return false;
    }
    ComparisonPredicate comparisonPredicate = (ComparisonPredicate) j.getOn().getExpressions().get(1);
    LiteralExpression<Long> literalExpression = (LiteralExpression<Long>) comparisonPredicate.getRightHandOperand();
    return literalExpression.getLiteral().equals(this.fieldId);
  }

  @Getter
  static class BoundedValue {

    private final Date lowerBound;
    private final Date upperBound;

    private BoundedValue(Object valuesRange, String from, String to, String timezone) {
      var stringArray = (ArrayList) valuesRange;
      this.lowerBound = parseDate(stringArray.get(0));
      this.upperBound = parseDate(stringArray.get(1));
    }
  }
}
