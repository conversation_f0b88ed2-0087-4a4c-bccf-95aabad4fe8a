package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.meeting.Meeting;
import com.sling.sales.report.security.domain.User;
import javax.persistence.metamodel.SingularAttribute;
import lombok.Getter;

@Getter
public class MeetingUserMapping {

  private final String mappedColumnFieldName;
  private final SingularAttribute<Meeting, User> mappedColumnFieldPath;

  public MeetingUserMapping(String mappedColumnFieldName, SingularAttribute<Meeting, User> mappedColumnFieldPath) {
    this.mappedColumnFieldName = mappedColumnFieldName;
    this.mappedColumnFieldPath = mappedColumnFieldPath;
  }
}
