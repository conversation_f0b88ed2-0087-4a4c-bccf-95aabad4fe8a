package com.sling.sales.report.core.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.http.ResponseEntity.ok;

import com.sling.sales.report.core.api.request.AggregationRequestV3;
import com.sling.sales.report.core.api.request.CreateReportRequest;
import com.sling.sales.report.core.api.request.FilterRequest;
import com.sling.sales.report.core.api.request.FilterRequest.Filter;
import com.sling.sales.report.core.api.request.UpdateReportRequest;
import com.sling.sales.report.core.api.response.AggregateRecord;
import com.sling.sales.report.core.api.response.ReportResponse;
import com.sling.sales.report.core.api.response.ReportSearchResponse;
import com.sling.sales.report.core.api.response.ReportSummaries;
import com.sling.sales.report.core.api.response.ReportSummary;
import com.sling.sales.report.core.domain.report.ReportType;
import com.sling.sales.report.dto.DownloadDetail;
import com.sling.sales.report.security.domain.UserFacade;
import com.sling.sales.report.security.domain.UserService;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping(value = "/v3/reports")
@Slf4j
public class ReportControllerV3 {

  private final ReportService reportService;
  private final UserService userService;
  @Autowired
  public ReportControllerV3(ReportService reportService, UserService userService) {
    this.reportService = reportService;
    this.userService = userService;
  }

  @ApiOperation(value = "Create report", code = 201, response = Long.class)
  @PostMapping(
      value = "",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<ReportSummary> createReport(@Valid @RequestBody CreateReportRequest createReportRequest) {
    ReportSummary summary = reportService.createReport(createReportRequest);
    var reportDetailsUri =
        UriComponentsBuilder.fromPath("/{id}")
            .buildAndExpand(summary.getId())
            .toUri();
    return ResponseEntity.created(reportDetailsUri).body(summary);

  }

  @ApiOperation(value = "Get existing report", response = ReportResponse.class)
  @GetMapping(
      value = "/{id}",
      produces = APPLICATION_JSON_VALUE)
  public ResponseEntity getReport(@PathVariable("id") long reportId) {
    return ok(reportService.getReport(reportId));
  }

  @ApiOperation(value = "Update existing report", response = ReportResponse.class)
  @PutMapping(
      value = "/{id}",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public Mono<ResponseEntity<ReportResponse>> updateReport(@PathVariable("id") long reportId,
      @Valid @RequestBody UpdateReportRequest updateReportRequest) {
    return reportService
        .updateReport(reportId, updateReportRequest)
        .map(ResponseEntity::ok);
  }

  @ApiOperation(value = "Search reports", response = ReportResponse.class)
  @GetMapping(
      value = "/search",
      produces = APPLICATION_JSON_VALUE)
  public Page<ReportSearchResponse> search(@RequestParam(value = "reportType", required = false) Optional<ReportType> reportType,
      Pageable pageable) {
    if (reportType.isPresent()) {
      FilterRequest.Filter reportTypeFilter = new Filter("equal", "reportType", "string", reportType.get());
      FilterRequest filterRequest = new FilterRequest(Collections.singletonList(reportTypeFilter));
      return reportService.search(Optional.of(filterRequest), pageable);
    }
    return reportService.search(Optional.empty(), pageable);
  }

  @ApiOperation(value = "Search reports", response = ReportResponse.class)
  @PostMapping(
      value = "/search",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public Page<ReportSearchResponse> search(@RequestParam(value = "reportType", required = false) Optional<ReportType> reportType,
      @RequestBody(required = false) FilterRequest filterRequest,
      Pageable pageable, @RequestHeader(value = "User-Agent", required = false) String userAgent) {
    if (!isMobile(userAgent)) {
      return reportService.search(Optional.ofNullable(filterRequest), pageable);
    }

    ArrayList<Filter> jsonRules = new ArrayList<>();
    if (ObjectUtils.isNotEmpty(filterRequest) && ObjectUtils.isNotEmpty(filterRequest.getJsonRules())) {
      jsonRules.addAll(filterRequest.getJsonRules());
    }
    FilterRequest.Filter reportCategoryFilter = new Filter("not_equal", "category", "string", "HIERARCHY");
    jsonRules.add(reportCategoryFilter);
    FilterRequest updatedFilterRequest = new FilterRequest(jsonRules);
    return reportService.search(Optional.of(updatedFilterRequest), pageable);
  }

  @DeleteMapping(value = "/{id}")
  public void deleteReport(@PathVariable("id") long reportId) {
    reportService.deleteReport(reportId);
  }

  @ApiOperation(value = "Get lead by", response = AggregateRecord.class)
  @PostMapping(
      value = "leads",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public ResponseEntity getLeadReport(@RequestBody AggregationRequestV3 aggregationRequest,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start = System.currentTimeMillis();
    List<AggregateRecord> body = reportService.aggregateResultsForLead(aggregationRequest, timezone, currencyId);
    log.info("v3 leads report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequest.toString());
    return ok(body);
  }

  @ApiOperation(value = "Get deal by", response = AggregateRecord.class)
  @PostMapping(
      value = "deals",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public ResponseEntity getDealReport(@RequestBody AggregationRequestV3 aggregationRequest,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start = System.currentTimeMillis();
    List<AggregateRecord> body = reportService.aggregateResultsForDeal(aggregationRequest, timezone, currencyId);
    log.info("v3 deals report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequest.toString());
    return ok(body);
  }

  @ApiOperation(value = "Get calls by", response = AggregateRecord.class)
  @PostMapping(value = "calls", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public ResponseEntity getCallReport(
      @RequestBody AggregationRequestV3 aggregationRequest,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start = System.currentTimeMillis();
    List<AggregateRecord> body = reportService.aggregateResultsForCall(aggregationRequest, timezone, currencyId);
    log.info("v3 calls report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequest.toString());
    return ok(body);
  }

  @ApiOperation(value = "Get tasks by", response = List.class)
  @PostMapping(value = "/tasks", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<List<AggregateRecord>> getTaskReport(@RequestBody AggregationRequestV3 aggregationRequest,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start = System.currentTimeMillis();
    List<AggregateRecord> body = reportService.aggregateResultsForTask(aggregationRequest, timezone, currencyId);
    log.info("v3 tasks report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequest.toString());
    return ok(body);
  }

  @ApiOperation(value = "Get company aggregated records", response = List.class)
  @PostMapping(value = "/companies", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<List<AggregateRecord>> getCompanyReport(@RequestBody AggregationRequestV3 aggregationRequest,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId") Long baseCurrencyId, @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start = System.currentTimeMillis();
    List<AggregateRecord> body = reportService.aggregateResultsForCompany(aggregationRequest, timezone, baseCurrencyId, currencyId);
    log.info("v3 companies report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequest.toString());
    return ok(body);
  }

  @ApiOperation(value = "Get meeting aggregated records", response = List.class)
  @PostMapping(value = "/meetings", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<List<AggregateRecord>> getMeetingReport(@RequestBody AggregationRequestV3 aggregationRequest,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start = System.currentTimeMillis();
    List<AggregateRecord> body = reportService.aggregateResultsForMeeting(aggregationRequest, timezone, baseCurrencyId, currencyId);
    log.info("v3 meetings report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequest.toString());
    return ok(body);
  }

  @ApiOperation(value = "Get contact aggregated records", response = List.class)
  @PostMapping(value = "/contacts", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<List<AggregateRecord>> getContactReport(@RequestBody AggregationRequestV3 aggregationRequest,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start = System.currentTimeMillis();
    List<AggregateRecord> body = reportService.aggregateResultsForContact(aggregationRequest, timezone, baseCurrencyId, currencyId);
    log.info("v3 contacts report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequest.toString());
    return ok(body);
  }

  @ApiOperation(value = "Get email aggregated records", response = List.class)
  @PostMapping(value = "/emails", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<List<AggregateRecord>> getEmailReport(@RequestBody AggregationRequestV3 aggregationRequest,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start = System.currentTimeMillis();
    List<AggregateRecord> body = reportService.aggregateResultsForEmail(aggregationRequest, timezone, currencyId);
    log.info("v3 emails report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequest.toString());
    return ok(body);
  }

  @ApiOperation(value = "Get leads for given report ids", response = AggregateRecord.class)
  @GetMapping(
      value = "leads/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummaries getSummaryForLeads(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    return reportService.aggregateResultsForReportType(ReportType.LEAD, ids, timezone, null, currencyId);
  }

  @ApiOperation(value = "Get deals for given report ids", response = AggregateRecord.class)
  @GetMapping(
      value = "deals/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummaries getSummaryForDeals(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId") Long baseCurrencyId, @RequestParam(value = "currencyId", required = false) Long currencyId) {
    return reportService.aggregateResultsForReportType(ReportType.DEAL, ids, timezone, baseCurrencyId, currencyId);
  }

  @ApiOperation(value = "Get tasks for given report ids", response = AggregateRecord.class)
  @GetMapping(
      value = "tasks/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummaries getSummaryForTasks(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    return reportService.aggregateResultsForReportType(ReportType.TASK, ids, timezone, null, currencyId);
  }

  @ApiOperation(value = "Get calls for given report ids", response = AggregateRecord.class)
  @GetMapping(
      value = "calls/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummaries getSummaryForCalls(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    return reportService.aggregateResultsForReportType(ReportType.CALL, ids, timezone, null, currencyId);
  }

  @ApiOperation(value = "Get companies for given report ids", response = AggregateRecord.class)
  @GetMapping(
      value = "companies/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummaries getSummaryForCompanies(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId") Long baseCurrencyId, @RequestParam(value = "currencyId", required = false) Long currencyId) {
    return reportService.aggregateResultsForReportType(ReportType.COMPANY, ids, timezone, baseCurrencyId, currencyId);
  }

  @ApiOperation(value = "Get meetings for given report ids", response = AggregateRecord.class)
  @GetMapping(
      value = "meetings/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummaries getSummaryForMeetings(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    return reportService.aggregateResultsForReportType(ReportType.MEETING, ids, timezone, baseCurrencyId, currencyId);
  }

  @ApiOperation(value = "Get contacts summary by report ids", response = AggregateRecord.class)
  @GetMapping(
      value = "contacts/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummaries getSummaryForContacts(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    return reportService.aggregateResultsForReportType(ReportType.CONTACT, ids, timezone, baseCurrencyId, currencyId);
  }

  @ApiOperation(value = "Get emails summary by report ids", response = AggregateRecord.class)
  @GetMapping(
      value = "emails/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummaries getSummaryForEmails(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    return reportService.aggregateResultsForReportType(ReportType.EMAIL, ids, timezone, baseCurrencyId, currencyId);
  }

  @ApiOperation("download report by id")
  @GetMapping(value = "/{id}/download")
  public ResponseEntity<Resource> downloadReport(@PathVariable("id") long id,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Calcutta") String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId,
      @RequestParam(value = "prorated", required = false) boolean prorated, @RequestParam("baseCurrency") String baseCurrency,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    DownloadDetail downloadDetail = reportService.downloadReport(id, timezone, baseCurrency, currencyId);
    HttpHeaders headers = new HttpHeaders();
    headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; " + downloadDetail.getFileName() + ".csv");
    headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-Disposition");
    return ResponseEntity.ok().headers(headers).body(downloadDetail.getResource());
  }

  @ApiOperation("download report with applied changes")
  @PostMapping(value = "/{id}/download", consumes = APPLICATION_JSON_VALUE)
  public ResponseEntity<Resource> downloadReportWithAppliedChanges(@PathVariable("id") long id,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Calcutta") String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId,
      @RequestParam(value = "prorated", required = false, defaultValue = "false") boolean prorated, @RequestParam("baseCurrency") String baseCurrency,
      @RequestBody AggregationRequestV3 aggregationRequestV3, @RequestParam(value = "currencyId", required = false) Long currencyId) {
    DownloadDetail downloadDetail = reportService.downloadReport(id, timezone, baseCurrency, aggregationRequestV3, prorated, currencyId);
    HttpHeaders headers = new HttpHeaders();
    headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; " + downloadDetail.getFileName() + ".csv");
    headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-Disposition");
    return ResponseEntity.ok().headers(headers).body(downloadDetail.getResource());
  }

  private boolean isMobile(String userAgent) {
    log.info("User agent is in report: {} ", userAgent);
    if (ObjectUtils.isNotEmpty(userAgent)) {
      String agent = userAgent.split("/")[0];
      return agent.equals("Dart");
    }
    return false;
  }

}
