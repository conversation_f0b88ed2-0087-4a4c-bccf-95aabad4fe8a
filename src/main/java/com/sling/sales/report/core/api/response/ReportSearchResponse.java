package com.sling.sales.report.core.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.api.request.ReportCategory;
import com.sling.sales.report.core.domain.report.ChartType;
import com.sling.sales.report.core.domain.report.ReportType;
import java.util.Date;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReportSearchResponse {

  private long id;
  private String name;
  private final String description;
  private ReportType reportType;
  private Date createdAt;
  private IdName createdBy;
  private Action recordActions;
  private ReportCategory category;
  private ChartType chartType;
  private Date updatedAt;
  private IdName updatedBy;
  private boolean systemDefault;
  private boolean multiMetric;

  @JsonCreator
  public ReportSearchResponse(@JsonProperty("id") long id, @JsonProperty("name") String name, @JsonProperty("description") String description,
      @JsonProperty("reportType") ReportType reportType,
      @JsonProperty("createdAt") Date createdAt, @JsonProperty("createdBy") IdName createdBy,
      @JsonProperty("recordActions") Action recordActions, @JsonProperty("category") ReportCategory category,
      @JsonProperty("chartType") ChartType chartType, @JsonProperty("updatedAt") Date updatedAt, @JsonProperty("updatedBy") IdName updatedBy,
      @JsonProperty("systemDefault") boolean systemDefault, @JsonProperty("multiMetric") boolean multiMetric) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.reportType = reportType;
    this.createdAt = createdAt;
    this.createdBy = createdBy;
    this.recordActions = recordActions;
    this.category = category;
    this.chartType = chartType;
    this.updatedAt = updatedAt;
    this.updatedBy = updatedBy;
    this.systemDefault = systemDefault;
    this.multiMetric = multiMetric;
  }
}
