package com.sling.sales.report.core.api;

import com.sling.sales.report.config.api.response.EntityGoalConfiguration;
import com.sling.sales.report.config.api.response.EntityGoalListConfiguration;
import com.sling.sales.report.config.api.response.FrequencyValue;
import com.sling.sales.report.config.domain.ConfigFacade;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.goal.FrequencyType;
import com.sling.sales.report.core.domain.service.CustomFieldService;
import com.sling.sales.report.dto.DimensionDetail;
import com.sling.sales.report.security.domain.UserService;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class GoalConfigService {

  private final UserService userService;
  private final ConfigFacade configFacade;
  private final CustomFieldService customFieldService;

  @Autowired
  public GoalConfigService(UserService userService, ConfigFacade configFacade,
      CustomFieldService customFieldService) {
    this.userService = userService;
    this.configFacade = configFacade;
    this.customFieldService = customFieldService;
  }

  public <T extends Fact> EntityGoalConfiguration<T> getGoalConfigurationByEntity(Class<T> factClass) {
    List<FrequencyValue> frequencies = buildFrequencies();
    var loggedInUser = userService.getLoggedInUser();
    var authenticationToken = userService.getAuthenticationToken();
    DimensionDetail<T> factCustomFieldDimensionDetail = customFieldService.getFactDimensionsForCustomFieldsByEntity(factClass, loggedInUser);
    EntityGoalConfiguration<T> entityGoalConfig = configFacade.
        getEntityConfigurationForGoal(loggedInUser, authenticationToken, factClass, factCustomFieldDimensionDetail);

    return entityGoalConfig.withFrequencies(frequencies);
  }

  public <T extends Fact> EntityGoalListConfiguration getGoalListingConfigurationByEntity1(Class<T> factClass) {
    var loggedInUser = userService.getLoggedInUser();
    var authenticationToken = userService.getAuthenticationToken();
    DimensionDetail<T> factDimensionsForCustomFields = customFieldService.getFactDimensionsForCustomFieldsByEntity(factClass, loggedInUser);
    return configFacade.getEntityConfigurationForGoalListing1(authenticationToken, factClass, factDimensionsForCustomFields);
  }

  private List<FrequencyValue> buildFrequencies() {
    return Arrays.stream(FrequencyType.values())
        .map(frequencyType -> new FrequencyValue(frequencyType.name(), frequencyType.getDisplayName()))
        .collect(Collectors.toList());
  }
}
