package com.sling.sales.report.core.api.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.api.response.IdName;
import com.sling.sales.report.dto.GoalDimension;
import com.sling.sales.report.dto.GoalFieldValue;
import com.sling.sales.report.dto.GoalFilter;
import com.sling.sales.report.dto.GoalFrequency;
import com.sling.sales.report.dto.GoalMetric;
import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class UpdateGoalRequest extends GoalBaseRequest implements Serializable {

  private long id;

  @JsonCreator
  public UpdateGoalRequest(@JsonProperty("id") long id, @JsonProperty("name") String name, @JsonProperty("description") String description,
      @JsonProperty("entityType") String entityType,
      @JsonProperty("value") double value,
      @JsonProperty("dimensions") List<GoalDimension> dimensions, @JsonProperty("metrics") List<GoalMetric> metrics,
      @JsonProperty("frequency") GoalFrequency frequency,
      @JsonProperty("filters") List<GoalFilter> filters, @JsonProperty("fieldValues") List<GoalFieldValue> fieldValues,
      @JsonProperty("dateRange") GoalFilter dateRange, @JsonProperty("owners") List<IdName> owners, @JsonProperty("showOthersProgress") boolean showOthersProgress) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.entityType = entityType;
    this.value = value;
    this.dimensions = dimensions;
    this.metrics = metrics;
    this.frequency = frequency;
    this.filters = filters;
    this.fieldValues = fieldValues;
    this.dateRange = dateRange;
    this.owners = owners;
    this.showOthersProgress = showOthersProgress;
  }
}
