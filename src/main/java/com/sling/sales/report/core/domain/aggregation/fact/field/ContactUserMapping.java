package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.contact.Contact;
import com.sling.sales.report.security.domain.User;
import javax.persistence.metamodel.SingularAttribute;
import lombok.Getter;

@Getter
public class ContactUserMapping {

  private final String mappedColumnFieldName;
  private final SingularAttribute<Contact, User> mappedColumnFieldPath;

  public ContactUserMapping(String mappedColumnFieldName, SingularAttribute<Contact, User> mappedColumnFieldPath) {
    this.mappedColumnFieldName = mappedColumnFieldName;
    this.mappedColumnFieldPath = mappedColumnFieldPath;
  }
}
