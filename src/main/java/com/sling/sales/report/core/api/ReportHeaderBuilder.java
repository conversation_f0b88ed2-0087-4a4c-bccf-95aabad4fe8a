package com.sling.sales.report.core.api;

import static com.sling.sales.report.core.domain.aggregation.dimension.MetricType.COUNT;

import com.sling.sales.report.config.domain.Field;
import com.sling.sales.report.config.domain.service.EntityService;
import com.sling.sales.report.config.domain.service.EntityServiceCache;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.core.domain.report.ReportType;
import com.sling.sales.report.dto.EntityLabelDto;
import com.sling.sales.report.dto.Header;
import com.sling.sales.report.dto.HeaderDetail;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

@Service
@Slf4j
public class ReportHeaderBuilder {

  private final EntityService entityService;
  private final EntityServiceCache entityServiceCache;

  @Autowired
  public ReportHeaderBuilder(EntityService entityService, EntityServiceCache entityServiceCache) {
    this.entityService = entityService;
    this.entityServiceCache = entityServiceCache;
  }

  public <T extends Fact> HeaderDetail buildHeaders(long tenantId, ReportType reportType, List<GroupByField> groupBy, List<Metric> metrics,
      Class<T> clazz,
      String authenticationToken, Filter filter) {
    List<Field> fields = entityServiceCache.getFiltersByEntity(tenantId,reportType.name().toLowerCase(),clazz, authenticationToken);
    Map<String, Field> fieldsToMap = fieldToMap1(fields);
    HeaderDetail headerDetailMono = buildHeaders(groupBy, authenticationToken, fieldsToMap, metrics, EntityType.valueOf(reportType.name()),filter.getFieldName());
    log.info("end of caching build header call for tenant id {}", tenantId);
    return headerDetailMono;
  }

  private HeaderDetail buildHeaders(List<GroupByField> groupBy, String authenticationToken,
      Map<String, Field> fieldsToMap, List<Metric> metrics, EntityType entityType,String dateRange) {

    Map<EntityType, EntityLabelDto> entityLabels = entityService.getEntitiesLabel(authenticationToken);
    Tuple3<Map<String, Field>, List<GroupByField>, Map<EntityType, EntityLabelDto>> tuple = Tuples.of(fieldsToMap, groupBy, entityLabels);
    List<String> headers = groupBy
        .stream()
        .map(groupByField -> {
          String header = fieldsToMap.get(groupByField.getName()).getHeader();
          if ("teams".equals(groupByField.getProperty())) {
            header = header + "(Teams)";
          }
          return header;
        })
        .collect(Collectors.toList());
    String dateRangeHeader = fieldsToMap.get(dateRange).getHeader();
    return new HeaderDetail(headers, getMetricHeaders(metrics, entityType, tuple), List.of(dateRangeHeader));

  }

  private Mono<Map<String, Field>> fieldToMap(Mono<List<Field>> fields) {
    return fields
        .flatMap(allFields -> {
          Map<String, Field> fieldsMap = allFields.stream()
              .collect(Collectors.toMap(Field::getId, field -> field));
          return Mono.just(fieldsMap);
        });
  }
  private Map<String, Field> fieldToMap1(List<Field> fields) {
    return fields
        .stream().collect(Collectors.toMap(field -> field.getId(), field ->  field));

  }

  private List<Header> getMetricHeaders(List<Metric> metrics, EntityType entityType,
      Tuple3<Map<String, Field>, List<GroupByField>, Map<EntityType, EntityLabelDto>> tuple) {

    return metrics
        .stream()
        .map(metric -> {
          String metricHeader = metric.getType() == COUNT ? getCountHeader(metric, entityType, tuple)
              : getSumOrAverageHeader(metric, tuple);
          return new Header(metricHeader, metric.getType());
        }).collect(Collectors.toList());
  }

  private String getSumOrAverageHeader(Metric metric, Tuple3<Map<String, Field>, List<GroupByField>, Map<EntityType, EntityLabelDto>> tuple) {
    return tuple.getT1().get(metric.getField()).getHeader();
  }

  private String getCountHeader(Metric metric, EntityType entityType,
      Tuple3<Map<String, Field>, List<GroupByField>, Map<EntityType, EntityLabelDto>> tuple) {
    if (metric.getField().equals("id") && tuple.getT3().containsKey(entityType)) {
      return tuple.getT3().get(entityType).getDisplayNamePlural();
    }
    if (!metric.getField().equals("id") && tuple.getT1().containsKey(metric.getField())) {
      return tuple.getT1().get(metric.getField()).getHeader();
    }
    if (metric.getField().equals("numberOfProducts") && entityType == EntityType.DEAL) {
      return "Products";
    }
    return entityType.getEntityPlural();
  }
}
