package com.sling.sales.report.core.api.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class GoalSummaryRequest {

  private final List<GoalSummaryRequestDetail> goals;

  @JsonCreator
  public GoalSummaryRequest(@JsonProperty("goals") List<GoalSummaryRequestDetail> goals) {
    this.goals = goals;
  }
}
