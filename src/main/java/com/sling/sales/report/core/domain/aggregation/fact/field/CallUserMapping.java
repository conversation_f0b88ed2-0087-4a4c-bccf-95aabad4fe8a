package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.call.Call;
import com.sling.sales.report.security.domain.User;
import javax.persistence.metamodel.SingularAttribute;
import lombok.Getter;

@Getter
public class CallUserMapping {

  private final String mappedColumnFieldName;
  private final SingularAttribute<Call, User> mappedColumnFieldPath;

  public CallUserMapping(String mappedColumnFieldName, SingularAttribute<Call, User> mappedColumnFieldPath) {
    this.mappedColumnFieldName = mappedColumnFieldName;
    this.mappedColumnFieldPath = mappedColumnFieldPath;
  }
}
