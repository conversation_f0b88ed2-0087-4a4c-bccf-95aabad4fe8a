package com.sling.sales.report.core.api.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class GoalSummaryRequestDetail {

  private final long id;
  private final String dashletType;

  @JsonCreator
  public GoalSummaryRequestDetail(@JsonProperty("id") long id, @JsonProperty("dashletType") String dashletType) {
    this.id = id;
    this.dashletType = dashletType;
  }
}
