package com.sling.sales.report.core.domain.contact;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
interface ContactCustomPicklistValueRepository extends JpaRepository<ContactCustomPicklistValue, Long>,
    JpaSpecificationExecutor<ContactCustomPicklistValue> {

  @Transactional
  @Modifying
  @Query("UPDATE ContactCustomPicklistValue SET displayName = :displayName WHERE picklistValueId = :picklistValueId")
  void updateDisplayNameByPicklistValueId(@Param("picklistValueId") long picklistValueId, @Param("displayName") String displayName);
}
