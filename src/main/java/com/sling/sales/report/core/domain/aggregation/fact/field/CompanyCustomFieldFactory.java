package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.aggregation.dimension.DateCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyBooleanCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyTextCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberCustomFieldFilterDimension;
import com.sling.sales.report.core.domain.company.Company;
import com.sling.sales.report.core.domain.company.CompanyCustomCheckboxValue;
import com.sling.sales.report.core.domain.company.CompanyCustomCheckboxValue_;
import com.sling.sales.report.core.domain.company.CompanyCustomDatePickerValue;
import com.sling.sales.report.core.domain.company.CompanyCustomDatePickerValue_;
import com.sling.sales.report.core.domain.company.CompanyCustomDatetimePickerValue;
import com.sling.sales.report.core.domain.company.CompanyCustomDatetimePickerValue_;
import com.sling.sales.report.core.domain.company.CompanyCustomMultiPicklistValue;
import com.sling.sales.report.core.domain.company.CompanyCustomMultiPicklistValue_;
import com.sling.sales.report.core.domain.company.CompanyCustomNumberValue;
import com.sling.sales.report.core.domain.company.CompanyCustomNumberValue_;
import com.sling.sales.report.core.domain.company.CompanyCustomTextValue;
import com.sling.sales.report.core.domain.company.CompanyCustomTextValue_;
import com.sling.sales.report.core.domain.company.CompanyPicklistValue;
import com.sling.sales.report.core.domain.company.CompanyPicklistValue_;
import com.sling.sales.report.core.domain.company.Company_;
import com.sling.sales.report.dto.DimensionDetail;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class CompanyCustomFieldFactory {

  public static DimensionDetail<Company> createManyIdCustomFieldDimension(String dimensionName, long fieldId) {
    ManyIdCustomFieldDimension<Company, CompanyPicklistValue> manyIdCustomFieldDimension = new ManyIdCustomFieldDimension<>(
        dimensionName,
        Company_.COMPANY_PICKLIST_VALUES,
        Company_.companyPicklistValues,
        join -> join.get(CompanyPicklistValue_.picklistValueId),
        join -> join.get(CompanyPicklistValue_.displayName),
        join -> join.get(CompanyPicklistValue_.fieldId),
        fieldId, CompanyPicklistValue_.DISPLAY_NAME);

    List<Metric<Company>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Company_.companyPicklistValues, join -> join.get(CompanyPicklistValue_.fieldId),
              CompanyPicklistValue_.PICKLIST_VALUE_ID, fieldId),
          null));
    }};

    return new DimensionDetail<>(manyIdCustomFieldDimension, manyIdCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Company> createManyTextCustomFieldDimension(String dimensionName, long fieldId) {
    ManyTextCustomFieldDimension<Company, CompanyCustomTextValue> manyTextCustomFieldDimension = new ManyTextCustomFieldDimension<>(
        dimensionName,
        Company_.COMPANY_CUSTOM_TEXT_VALUES,
        Company_.companyCustomTextValues,
        join -> join.get(CompanyCustomTextValue_.value),
        join -> join.get(CompanyCustomTextValue_.fieldId),
        fieldId
    );

    List<Metric<Company>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Company_.companyCustomTextValues, join -> join.get(CompanyCustomTextValue_.fieldId),
              CompanyCustomTextValue_.VALUE, fieldId),
          null));
    }};

    return new DimensionDetail<>(manyTextCustomFieldDimension, manyTextCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Company> createNumberCustomFieldFilterDimension(String dimensionName, long fieldId) {
    NumberCustomFieldFilterDimension<Company, CompanyCustomNumberValue> numberCustomFieldFilterDimension = new NumberCustomFieldFilterDimension<>(
        dimensionName,
        Company_.COMPANY_CUSTOM_NUMBER_VALUES,
        Company_.companyCustomNumberValues,
        join -> join.get(CompanyCustomNumberValue_.value),
        join -> join.get(CompanyCustomNumberValue_.fieldId), fieldId);

    List<Metric<Company>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Company_.companyCustomNumberValues, join -> join.get(CompanyCustomNumberValue_.fieldId),
              CompanyCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.SUM,
          getBiFunction(MetricType.SUM, Company_.companyCustomNumberValues, join -> join.get(CompanyCustomNumberValue_.fieldId),
              CompanyCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.AVERAGE,
          getBiFunction(MetricType.AVERAGE, Company_.companyCustomNumberValues, join -> join.get(CompanyCustomNumberValue_.fieldId),
              CompanyCustomNumberValue_.VALUE, fieldId),
          null));
    }};

    return new DimensionDetail<>(null, numberCustomFieldFilterDimension, metrics);
  }

  public static DimensionDetail<Company> createDatePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Company, CompanyCustomDatePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Company_.COMPANY_CUSTOM_DATE_PICKER_VALUES,
        Company_.companyCustomDatePickerValues,
        join -> join.get(CompanyCustomDatePickerValue_.value),
        join -> join.get(CompanyCustomDatePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Company> createDatetimePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Company, CompanyCustomDatetimePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Company_.COMPANY_CUSTOM_DATETIME_PICKER_VALUES,
        Company_.companyCustomDatetimePickerValues,
        join -> join.get(CompanyCustomDatetimePickerValue_.value),
        join -> join.get(CompanyCustomDatetimePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  private static <T, V> BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>> getBiFunction(MetricType metricType,
      SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> getFieldId, String attributeName, long fieldId) {

    return (root, builder) -> CustomFieldMetricBuilder
        .buildExpressionByMetricType(metricType, root, builder, dimensionField, getFieldId,
            attributeName, fieldId);
  }

  public static DimensionDetail<Company> createBooleanCustomFieldDimension(String dimensionName, long fieldId) {
    ManyBooleanCustomFieldDimension<Company, CompanyCustomCheckboxValue> manyBooleanCustomFieldDimension = new ManyBooleanCustomFieldDimension<>(
        dimensionName,
        Company_.COMPANY_CUSTOM_CHECKBOX_VALUES,
        Company_.companyCustomCheckboxValues,
        join -> join.get(CompanyCustomCheckboxValue_.value),
        join -> join.get(CompanyCustomCheckboxValue_.fieldId),
        fieldId
    );
    return new DimensionDetail<>(manyBooleanCustomFieldDimension, manyBooleanCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Company> createCustomMultiFieldDimension(String dimensionName, long fieldId) {
    ManyIdCustomFieldDimension<Company, CompanyCustomMultiPicklistValue> customMultiPicklistDimension = new ManyIdCustomFieldDimension<>(
        dimensionName,
        Company_.COMPANY_CUSTOM_MULTI_PICKLIST_VALUES,
        Company_.companyCustomMultiPicklistValues,
        join -> join.get(CompanyCustomMultiPicklistValue_.picklistValueId),
        join -> join.get(CompanyCustomMultiPicklistValue_.displayName),
        join -> join.get(CompanyCustomMultiPicklistValue_.fieldId),
        fieldId,CompanyCustomMultiPicklistValue_.DISPLAY_NAME);

    List<Metric<Company>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Company_.companyCustomMultiPicklistValues, join -> join.get(CompanyCustomMultiPicklistValue_.fieldId),
              CompanyCustomMultiPicklistValue_.PICKLIST_VALUE_ID, fieldId),
          null));
    }};

    return new DimensionDetail<>(customMultiPicklistDimension, customMultiPicklistDimension, metrics);
  }
}
