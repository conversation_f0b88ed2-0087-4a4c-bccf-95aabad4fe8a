package com.sling.sales.report.core.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.dto.GoalDimension;
import com.sling.sales.report.dto.GoalFieldValue;
import com.sling.sales.report.dto.GoalFilter;
import com.sling.sales.report.dto.GoalFrequency;
import com.sling.sales.report.dto.GoalMetric;
import com.sling.sales.report.security.domain.User;
import java.util.Date;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoalResponse {

  private final long id;
  private final String name;
  private final String description;
  private final String entityType;
  private final double value;
  private final GoalDimension dimension;
  private final GoalMetric metric;
  private final GoalFrequency frequency;
  private final List<GoalFilter> filters;
  private final List<GoalFieldValue> fieldValues;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private final Date createdAt;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private final Date updatedAt;
  private final User createdBy;
  private final User updatedBy;
  private final Action recordActions;
  private final boolean active;
  private final Long defaultReportId;
  private final GoalFilter dateRange;
  private final List<IdName> owners;
  private final boolean showOthersProgress;

  @JsonCreator
  public GoalResponse(@JsonProperty("id") long id, @JsonProperty("name") String name, @JsonProperty("description") String description,
      @JsonProperty("entityType") String entityType,
      @JsonProperty("value") double value,
      @JsonProperty("dimension") GoalDimension dimension, @JsonProperty("metric") GoalMetric metric,
      @JsonProperty("frequency") GoalFrequency frequency,
      @JsonProperty("filters") List<GoalFilter> filters, @JsonProperty("fieldValues") List<GoalFieldValue> fieldValues,
      @JsonProperty("createdAt") Date createdAt, @JsonProperty("updatedAt") Date updatedAt, @JsonProperty("createdBy") User createdBy,
      @JsonProperty("updatedBy") User updatedBy, @JsonProperty("recordActions") Action recordActions, @JsonProperty("active") boolean active,
      @JsonProperty("defaultReportId") Long defaultReportId,
      @JsonProperty("dateRange") GoalFilter dateRange,
      @JsonProperty("owners") List<IdName> owners,
      @JsonProperty("showOthersProgress") boolean showOthersProgress
  ) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.entityType = entityType;
    this.value = value;
    this.dimension = dimension;
    this.metric = metric;
    this.frequency = frequency;
    this.filters = filters;
    this.fieldValues = fieldValues;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.createdBy = createdBy;
    this.updatedBy = updatedBy;
    this.recordActions = recordActions;
    this.active = active;
    this.defaultReportId = defaultReportId;
    this.dateRange = dateRange;
    this.owners = owners;
    this.showOthersProgress = showOthersProgress;
  }
}
