package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.between;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.greater;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.greater_or_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.in;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.less;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.less_or_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_between;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_in;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.query.criteria.internal.expression.LiteralExpression;
import org.hibernate.query.criteria.internal.predicate.ComparisonPredicate;
import org.springframework.data.jpa.domain.Specification;

public class NumberCustomFieldFilterDimension<T extends Fact, V extends EntityDimension> implements FilterDimension<T> {

  private final List<Operator> allowedOperators = asList(equal, not_equal, greater, greater_or_equal, less, less_or_equal,
      between, not_between, in, not_in, is_null, is_not_null);
  private final String name;
  private final String dimensionName;
  private final SetAttribute<T, V> dimensionField;
  private final Function<Join<T, V>, Path<Double>> pathToColumn;
  private final Function<Join<T, V>, Path<Long>> getFieldId;
  private final long fieldId;

  public NumberCustomFieldFilterDimension(String name, String dimensionName, SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Double>> pathToColumn,
      Function<Join<T, V>, Path<Long>> getFieldId, long fieldId) {
    this.name = name;
    this.dimensionName = dimensionName;
    this.dimensionField = dimensionField;
    this.pathToColumn = pathToColumn;
    this.getFieldId = getFieldId;
    this.fieldId = fieldId;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  private Double getValue(Object value) {
    if (value instanceof String) {
      return Double.valueOf((String) value);
    }
    return (Double) value;
  }

  private List<String> getParameters(String value) {
    return Arrays.stream(value.split(","))
        .map(String::trim)
        .collect(toList());
  }

  @Override
  @SuppressWarnings("unchecked")
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    return getSpecification(operator, value);
  }

  private Specification<T> getSpecification(Operator operator, Object value) {
    switch (operator) {
      case equal:
        return hasValue(getValue(value));
      case not_equal:
        return doesNotHaveValue(getValue(value)).or(isNull());
      case greater:
        return greaterThan(getValue(value));
      case greater_or_equal:
        return greaterThanOrEqualTo(getValue(value));
      case less:
        return lessThan(getValue(value));
      case less_or_equal:
        return lessThanOrEqual(getValue(value));
      case between:
        return betweenValues((List<String>) value);
      case not_between:
        return notBetweenValues((List<String>) value);
      case in:
        return inValues((String) value);
      case not_in:
        return notInValues((String) value);
      case is_null:
        return isNull();
      case is_not_null:
        return isNotNull();
      default:
        return null;
    }
  }

  private Specification<T> hasFieldId(Join<T, V> join) {
    return ((root, query, builder) -> builder.equal(getFieldId.apply(join), fieldId));
  }

  private Specification<T> isFieldIdNull(Join<T, V> join) {
    return ((root, query, builder) -> builder.isNull(getFieldId.apply(join)));
  }

  private Optional<Specification<T>> getSpecification(Join<T, V> join) {
    return Optional.ofNullable(hasFieldId(join).or(isFieldIdNull(join)));
  }

  private Specification<T> hasValue(Double value) {
    return ((root, query, builder) -> builder.equal(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), value));
  }

  private Specification<T> doesNotHaveValue(Double value) {
    return ((root, query, builder) -> builder.notEqual(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), value));
  }

  private Specification<T> isNull() {
    return ((root, query, builder) -> builder.isNull(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder))));
  }

  private Specification<T> isNotNull() {
    return ((root, query, builder) -> builder.isNotNull(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder))));
  }

  private Specification<T> greaterThan(Double value) {
    return ((root, query, builder) -> builder.greaterThan(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), value));
  }

  private Specification<T> greaterThanOrEqualTo(Double value) {
    return ((root, query, builder) -> builder.greaterThanOrEqualTo(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), value));
  }

  private Specification<T> lessThan(Double value) {
    return ((root, query, builder) -> builder.lessThan(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), value));
  }

  private Specification<T> lessThanOrEqual(Double value) {
    return ((root, query, builder) -> builder.lessThanOrEqualTo(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), value));
  }

  private Specification<T> betweenValues(List<String> values) {
    return ((root, query, builder) -> builder.between(
        pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), getValue(values.get(0)), getValue(values.get(1)))
    );
  }

  private Specification<T> notBetweenValues(List<String> values) {
    return ((root, query, builder) -> builder.between(
        pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), getValue(values.get(0)), getValue(values.get(1)))
        .not()
    );
  }

  private Specification<T> inValues(String value) {
    List<String> values = getParameters(value);
    return ((root, query, builder) -> {
      Path<Double> jsonbExpression = pathToColumn.apply(getOrCreateLeftJoin(root, query, builder));
      return jsonbExpression.in(values);
    });
  }

  private Specification<T> notInValues(String value) {
    List<String> values = getParameters(value);
    return ((root, query, builder) -> {
      Path<Double> jsonbExpression = pathToColumn.apply(getOrCreateLeftJoin(root, query, builder));
      return jsonbExpression.in(values).not();
    });
  }

  protected Join<T, V> createJoin(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
    Join<T, V> join = root.join(dimensionField, JoinType.LEFT);
    if (getSpecification(join).isPresent()) {
      join.on(getSpecification(join).get().toPredicate(root, query, builder));
    }
    return join;
  }

  private Join<T, V> getOrCreateLeftJoin(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
    return root.getJoins()
        .stream()
        .filter(this::isFieldIdAlreadyPresent)
        .map(j -> ((Join<T, V>) j))
        .findFirst()
        .orElseGet(() -> createJoin(root, query, builder));
  }

  private boolean isFieldIdAlreadyPresent(Join<T, ?> j) {
    if (ObjectUtils.isEmpty(j.getOn()) || ObjectUtils.isEmpty(j.getOn().getExpressions()) || j.getOn().getExpressions().size() < 2) {
      return false;
    }
    ComparisonPredicate comparisonPredicate = (ComparisonPredicate) j.getOn().getExpressions().get(1);
    LiteralExpression<Long> literalExpression = (LiteralExpression<Long>) comparisonPredicate.getRightHandOperand();
    return literalExpression.getLiteral().equals(this.fieldId);
  }

  @Override
  public String getName() {
    return name;
  }
}
