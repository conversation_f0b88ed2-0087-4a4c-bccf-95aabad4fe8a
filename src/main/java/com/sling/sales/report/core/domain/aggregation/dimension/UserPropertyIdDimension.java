package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.in;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_in;
import static java.util.Arrays.asList;
import static java.util.stream.Collectors.toList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.Format;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import com.sling.sales.report.core.domain.goal.GoalFieldValue;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SingularAttribute;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.jpa.domain.Specification;

public class UserPropertyIdDimension<T extends Fact, V extends EntityDimension> implements FilterDimension<T>, GroupByDimension<T> {

  private final List<Operator> allowedOperators = asList(equal, not_equal, is_null, is_not_null, in, not_in);
  private final String name;
  private final String dimensionName;
  private final SingularAttribute<T, V> dimensionField;
  private final Function<Join<T, V>, Path<Long>> getId;
  private final Function<Join<T, V>, Path<String>> getName;
  private final List<FilterDimension<T>> requiredFilters;
  private final String primaryField;
  private final String property;
  private Format format;

  public UserPropertyIdDimension(
      String name,
      String dimensionName,
      SingularAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> pathToDimensionId,
      Function<Join<T, V>, Path<String>> pathToDimensionName,
      List<FilterDimension<T>> requiredFilters,
      String primaryField,
      String property
  ) {
    this.name = name;
    this.dimensionName = dimensionName;
    this.dimensionField = dimensionField;
    this.getId = pathToDimensionId;
    this.getName = pathToDimensionName;
    this.requiredFilters = requiredFilters;
    this.primaryField = primaryField;
    this.property = property;
  }

  @Override
  public UserPropertyIdDimension<T, V> withFormat(Format format) {
    this.format = format;
    return this;
  }

  @Override
  public Format getFormat() {
    return this.format;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return this.allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return this.requiredFilters;
  }

  @Override
  public String getName() {
    return this.name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    switch (operator) {
      case equal:
        return hasId(Long.parseLong(String.valueOf(value)));
      case not_equal:
        return doesNotHaveId(Long.parseLong(String.valueOf(value))).or(isNull());
      case is_null:
        return isNull();
      case is_not_null:
        return isNotNull();
      case in:
        return in(getParameters(value));
      case not_in:
        return notIn(getParameters(value)).or(isNull());
      default:
        return null;
    }
  }

  Specification<T> hasId(Long value) {
    return ((root, query, builder) -> builder.equal(getId.apply(getOrCreateLeftJoin(root)), value));
  }

  Specification<T> doesNotHaveId(Long value) {
    return ((root, query, builder) -> builder.notEqual(getId.apply(getOrCreateLeftJoin(root)), value));
  }

  Specification<T> isNull() {
    return ((root, query, builder) -> builder.isNull(getId.apply(getOrCreateLeftJoin(root))));
  }

  Specification<T> isNotNull() {
    return ((root, query, builder) -> builder.isNotNull(getId.apply(getOrCreateLeftJoin(root))));
  }

  private List<Long> getParameters(Object value) {
    if (value instanceof List) {
      return (List<Long>) value;
    }
    return Arrays.stream(String.valueOf(value).split(","))
        .map(String::trim)
        .map(Long::valueOf)
        .collect(toList());
  }

  private Specification<T> in(List<Long> values) {
    return ((root, query, builder) -> {
      Expression<Long> jsonbExpression = getId.apply(getOrCreateLeftJoin(root));
      return jsonbExpression.in(values);
    });
  }

  private Specification<T> notIn(List<Long> values) {
    return ((root, query, builder) -> {
      Expression<Long> jsonbExpression = getId.apply(getOrCreateLeftJoin(root));
      return jsonbExpression.in(values).not();
    });
  }

  @Override
  public List<Expression<?>> getGroupByPath(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, String timezone) {
    return asList(getId.apply(getOrCreateLeftJoin(root)), getName.apply(getOrCreateLeftJoin(root)));
  }

  @Override
  public GoalGroupByDimensionDetail<T> getGoalGroupByDimensionDetail(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder,
      String timezone,
      GoalDetail goalDetail) {
    Join<T, V> join = getOrCreateLeftJoin(root);
    Expression<Long> longExpression = getId.apply(join);
    return new GoalGroupByDimensionDetail<>(asList(longExpression, getName.apply(join)), inFieldValues(goalDetail.getFieldValues(), longExpression),
        false);
  }

  private Specification<T> inFieldValues(Set<Long> values, Expression<Long> jsonbExpression) {
    return ((root, query, builder) -> jsonbExpression.in(values));
  }

  @Override
  public Aggregate toResultKey(Tuple tuple, int columnNumber) {
    int nextColumnNumber = columnNumber + 2;
    return new Aggregate(tuple.get(columnNumber, Long.class), tuple.get(columnNumber + 1, String.class), nextColumnNumber, null);
  }

  @Override
  public GoalAggregate toGoalResultKey(Tuple tuple, int columnNumber, Map<Long, GoalFieldValue> goalFieldValuesByFieldValueId) {
    int nextColumnNumber = columnNumber + 2;
    Long fieldValueId = tuple.get(columnNumber, Long.class);
    Number goal = getGoalValue(fieldValueId, goalFieldValuesByFieldValueId);
    long goalFieldValueId = getFieldValueId(fieldValueId, goalFieldValuesByFieldValueId);
    return new GoalAggregate(fieldValueId, tuple.get(columnNumber + 1, String.class),
        goal, goalFieldValueId, nextColumnNumber);
  }

  private long getFieldValueId(Long fieldValueId, Map<Long, GoalFieldValue> goalFieldValuesByFieldValueId) {
    GoalFieldValue goalFieldValue = goalFieldValuesByFieldValueId.getOrDefault(fieldValueId, null);
    if (ObjectUtils.isEmpty(goalFieldValue)) {
      return 0L;
    }
    return goalFieldValue.getId();
  }

  private Number getGoalValue(Long fieldValueId, Map<Long, GoalFieldValue> goalFieldValuesByFieldValueId) {
    GoalFieldValue goalFieldValue = goalFieldValuesByFieldValueId.getOrDefault(fieldValueId, null);
    if (ObjectUtils.isEmpty(goalFieldValue)) {
      return 0;
    }
    return goalFieldValue.getValue();
  }

  private Join<T, V> getOrCreateLeftJoin(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<T, V>) j))
        .findFirst()
        .orElseGet(() -> root.join(dimensionField, JoinType.LEFT));
  }
}
