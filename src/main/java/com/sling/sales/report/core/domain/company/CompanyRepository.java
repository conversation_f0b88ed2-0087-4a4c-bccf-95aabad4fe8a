package com.sling.sales.report.core.domain.company;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
interface CompanyRepository extends JpaRepository<Company, Long>, JpaSpecificationExecutor<Company> {

  Optional<Company> findByTenantIdAndId(long tenantId, long id);

  @Modifying(flushAutomatically = true)
  @Transactional
  @Query(value = "DELETE FROM company WHERE deleted = true AND updated_at < now() - INTERVAL '10 days'", nativeQuery = true)
  void deleteRecordsOlderThan10Days();
}
