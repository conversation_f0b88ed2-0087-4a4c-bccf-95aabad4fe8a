package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.in;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_in;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.Format;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.query.criteria.internal.expression.LiteralExpression;
import org.hibernate.query.criteria.internal.predicate.ComparisonPredicate;
import org.springframework.data.jpa.domain.Specification;

public class ManyIdAssociatedEntityFilterDimension<T extends Fact, V extends EntityDimension> implements FilterDimension<T>, GroupByDimension<T>{

  private final List<Operator> allowedOperators = asList(equal, not_equal, is_null, is_not_null, in, not_in);
  private final String name;
  private final String dimensionName;
  private final SetAttribute<T, V> dimensionField;
  private final Function<Join<T, V>, Path<Long>> getId;
  private final Function<Join<T, V>, Path<String>> getEntity;
  private final Function<Join<T, V>, Path<String>> getName;
  private final String entity;
  private Format format;


  public ManyIdAssociatedEntityFilterDimension(
      String name,
      String dimensionName,
      SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> getId,
      Function<Join<T, V>, Path<String>> getEntity,
      Function<Join<T, V>, Path<String>> getName, String entity) {
    this.name = name;
    this.dimensionName = dimensionName;
    this.dimensionField = dimensionField;
    this.getId = getId;
    this.getEntity = getEntity;
    this.getName = getName;
    this.entity = entity;
  }

  @Override
  public ManyIdAssociatedEntityFilterDimension<T, V> withFormat(Format format) {
    this.format = format;
    return this;
  }

  @Override
  public Format getFormat() {
    return this.format;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    switch (operator) {
      case equal:
        return entityIdEqualTo(Long.parseLong(String.valueOf(value)));
      case not_equal:
        return entityIdNotEqualTo(Long.parseLong(String.valueOf(value))).or(isNull());
      case is_null:
        return isNull();
      case is_not_null:
        return isNotNull();
      case in:
        return entityIdsIn(getEntityIds(value));
      case not_in:
        return entityIdsNotIn(getEntityIds(value)).or(isNull());
      default:
        return null;
    }
  }

  private Specification<T> entityIdEqualTo(Long entityId) {
    return (root, query, builder) -> builder.equal(getId.apply(getOrCreateJoin(root, query, builder)), entityId);
  }

  private Specification<T> entityIdNotEqualTo(Long entityId) {
    return (root, query, builder) -> builder.equal(getId.apply(getOrCreateJoin(root, query, builder)), entityId).not();
  }

  private Specification<T> entityIdsIn(List<Long> entityIds) {
    return (root, query, builder) -> {
      Expression<Long> jsonbExpression = getId.apply(getOrCreateJoin(root, query, builder));
      return jsonbExpression.in(entityIds);
    };
  }

  private Specification<T> entityIdsNotIn(List<Long> entityIds) {
    return (root, query, builder) -> {
      Expression<Long> jsonbExpression = getId.apply(getOrCreateJoin(root, query, builder));
      return jsonbExpression.in(entityIds).not();
    };
  }

  Specification<T> isNull() {
    return (root, query, builder) -> builder.isNull(getId.apply(getOrCreateJoin(root, query, builder)));
  }

  Specification<T> isNotNull() {
    return (root, query, builder) -> builder.isNotNull(getId.apply(getOrCreateJoin(root, query, builder)));
  }

  private List<Long> getEntityIds(Object value) {
    if (value instanceof List) {
      return (List<Long>) value;
    }
    return Arrays.stream(String.valueOf(value).split(","))
        .map(String::trim)
        .map(Long::valueOf)
        .collect(toList());
  }

  protected Join<T, V> getOrCreateJoin(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
    return root.getJoins()
        .stream()
        .filter(this::isEntityJoinAlreadyPresent)
        .map(j -> ((Join<T, V>) j))
        .findFirst()
        .orElseGet(() -> createJoin(root, query, builder));
  }

  private boolean isEntityJoinAlreadyPresent(Join<T, ?> join) {
    if (!join.getAttribute().getName().equals(dimensionName)) {
      return false;
    }
    if (ObjectUtils.isEmpty(join.getOn())) {
      return false;
    }
    ComparisonPredicate comparisonPredicate = (ComparisonPredicate) join.getOn();
    LiteralExpression<String> literalExpression = (LiteralExpression<String>) comparisonPredicate.getRightHandOperand();
    return literalExpression.getLiteral().equals(entity);
  }

  private Join<T, V> createJoin(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
    Join<T, V> join = root.join(dimensionField, JoinType.LEFT);
    join.on(builder.equal(getEntity.apply(join), entity));
    return join;
  }

  @Override
  public List<Expression<?>> getGroupByPath(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, String timezone) {
    Join<T, V> join = createJoin(root, query, criteriaBuilder);
    return asList(getId.apply(join), getName.apply(join));
  }

  @Override
  public Aggregate toResultKey(Tuple tuple, int columnNumber) {
    int nextColumnNumber = columnNumber + 2;
    return new Aggregate(tuple.get(columnNumber, Long.class), tuple.get(columnNumber + 1, String.class), nextColumnNumber, null);
  }
}
