package com.sling.sales.report.core.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.api.request.AggregationRequestV2;
import com.sling.sales.report.core.api.request.ReportCategory;
import com.sling.sales.report.core.domain.report.ChartType;
import com.sling.sales.report.core.domain.report.ReportType;
import com.sling.sales.report.dto.HeaderDetail;
import com.sling.sales.report.security.domain.User;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReportSummariesV2 {

    private final List<ReportWiseSummaryV2> content;

    @JsonCreator
    public ReportSummariesV2(@JsonProperty("content") List<ReportWiseSummaryV2> content) {
        this.content = content;
    }

    @Getter
    public static class ReportWiseSummaryV2 {

        private final long id;
        private final String name;
        private final ChartType chartType;
        private final ReportCategory reportCategory;
        private final List<AggregateRecord> data;
        private final HeaderDetail headerDetail;
        private final AggregationRequestV2 config;
        private final String description;
        private final ReportType reportType;
        private final Action recordActions;
        private final User createdBy;


        @JsonCreator
        public ReportWiseSummaryV2(
            @JsonProperty("id") long id,
            @JsonProperty("name") String name,
            @JsonProperty("chartType") ChartType chartType,
            @JsonProperty("reportCategory") ReportCategory reportCategory,
            @JsonProperty("data") List<AggregateRecord> data,
            @JsonProperty("headerDetail") HeaderDetail headerDetail,
            @JsonProperty("config") AggregationRequestV2 config,
            @JsonProperty("description") String description,
            @JsonProperty("reportType") ReportType reportType,
            @JsonProperty("recordActions") Action recordActions,
            @JsonProperty("createdBy") User createdBy) {
            this.id = id;
            this.name = name;
            this.chartType = chartType;
            this.reportCategory = reportCategory;
            this.data = data;
            this.headerDetail = headerDetail;
            this.config = config;
            this.description = description;
            this.reportType = reportType;
            this.recordActions = recordActions;
            this.createdBy = createdBy;
        }
    }
}
