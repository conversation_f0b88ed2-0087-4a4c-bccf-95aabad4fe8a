package com.sling.sales.report.core.domain.aggregation.fact.field;

import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.domain.aggregation.dimension.DateDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.DealMultiFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.IdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.IdNameDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.TextDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.TextJsonBDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.UserPropertyIdDimension;
import com.sling.sales.report.core.domain.contact.ContactUtm_;
import com.sling.sales.report.core.domain.deal.Deal;
import com.sling.sales.report.core.domain.deal.DealCampaign;
import com.sling.sales.report.core.domain.deal.DealCampaign_;
import com.sling.sales.report.core.domain.deal.DealContact;
import com.sling.sales.report.core.domain.deal.DealContact_;
import com.sling.sales.report.core.domain.deal.DealProduct;
import com.sling.sales.report.core.domain.deal.DealProduct_;
import com.sling.sales.report.core.domain.deal.DealSource;
import com.sling.sales.report.core.domain.deal.DealSource_;
import com.sling.sales.report.core.domain.deal.DealUtm;
import com.sling.sales.report.core.domain.deal.DealUtm_;
import com.sling.sales.report.core.domain.deal.Deal_;
import com.sling.sales.report.core.domain.deal.Money_;
import com.sling.sales.report.core.domain.entity.DealCompany;
import com.sling.sales.report.core.domain.entity.DealCompany_;
import com.sling.sales.report.core.domain.entity.Pipeline;
import com.sling.sales.report.core.domain.entity.PipelineStage;
import com.sling.sales.report.core.domain.entity.PipelineStage_;
import com.sling.sales.report.core.domain.entity.Pipeline_;
import com.sling.sales.report.core.domain.lead.Lead;
import com.sling.sales.report.core.domain.lead.Lead_;
import com.sling.sales.report.dto.DimensionDetail;
import com.sling.sales.report.security.domain.Team;
import com.sling.sales.report.security.domain.Team_;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.User_;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
class DealFieldFactory extends FactFieldFactory<Deal> {

  private final static Map<String, DealUserMapping> MAPPED_PRIMARY_PROPERTIES = new HashMap<>() {{
    put("ownedBy", new DealUserMapping(Deal_.OWNED_BY, Deal_.ownedBy));
    put("createdBy", new DealUserMapping(Deal_.CREATED_BY, Deal_.createdBy));
    put("updatedBy", new DealUserMapping(Deal_.UPDATED_BY, Deal_.updatedBy));
    put("importedBy", new DealUserMapping(Deal_.IMPORTED_BY, Deal_.importedBy));
  }};

  private final IdDimension<Deal, Pipeline> pipeline = new IdDimension<>(
      "pipeline",
      Deal_.ATTACHED_PIPELINE,
      Deal_.attachedPipeline,
      join -> join.get(Pipeline_.id),
      join -> join.get(Pipeline_.name),
      emptyList());

  private final IdDimension<Deal, PipelineStage> pipelineStage = new IdDimension<>(
      "pipelineStage",
      Deal_.CURRENT_STAGE,
      Deal_.currentStage,
      join -> join.get(PipelineStage_.id),
      join -> join.get(PipelineStage_.name),
      singletonList(pipeline));

  private final IdDimension<Deal, User> ownedBy = new IdDimension<>(
      "ownedBy",
      Deal_.OWNED_BY,
      Deal_.ownedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final ManyIdDimension<Deal, DealProduct> products = new ManyIdDimension<>(
      "products",
      Deal_.PRODUCTS,
      Deal_.products,
      join -> join.get(DealProduct_.productId),
      join -> join.get(DealProduct_.productName),
      emptyList()
  );

  private final IdDimension<Deal, DealCompany> dealCompany = new IdDimension<>(
      "company",
      Deal_.DEAL_COMPANY,
      Deal_.dealCompany,
      join -> join.get(DealCompany_.id),
      join -> join.get(DealCompany_.name),
      emptyList());

  private final TextDimension<Deal> pipelineStageReason = new TextDimension<>(
      "pipelineStageReason",
      root -> root.get(Deal_.pipelineStageReason),
      singletonList(pipeline));

  private final TextDimension<Deal> forecastingType = new TextDimension<>(
      "forecastingType",
      root -> root.get(Deal_.forecastingType),
      emptyList());

  private final IdDimension<Deal, DealCampaign> campaign = new IdDimension<>(
      "campaign",
      Deal_.CAMPAIGN,
      Deal_.campaign,
      join -> join.get(DealCampaign_.id),
      join -> join.get(DealCampaign_.name),
      emptyList());

  private final IdDimension<Deal, DealSource> source = new IdDimension<>(
      "source",
      Deal_.SOURCE,
      Deal_.source,
      join -> join.get(DealSource_.id),
      join -> join.get(DealSource_.name),
      emptyList());

  private final ManyIdDimension<Deal, DealContact> associatedContacts = new ManyIdDimension<>(
      "associatedContacts",
      Deal_.ASSOCIATED_CONTACTS,
      Deal_.associatedContacts,
      join -> join.get(DealContact_.contactId),
      join -> join.get(DealContact_.name),
      emptyList()
  );

  private final IdDimension<Deal, User> createdBy = new IdDimension<>(
      "createdBy",
      Deal_.CREATED_BY,
      Deal_.createdBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdDimension<Deal, User> updatedBy = new IdDimension<>(
      "updatedBy",
      Deal_.UPDATED_BY,
      Deal_.updatedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdDimension<Deal, User> importedBy = new IdDimension<>(
      "importedBy",
      Deal_.IMPORTED_BY,
      Deal_.importedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdNameDimension<Deal> currency = new IdNameDimension<>(
      "currency",
      root -> root.get(Deal_.currencyId),
      root -> root.get(Deal_.currency),
      emptyList());

  private final DateDimension<Deal> createdAt = new DateDimension<>("createdAt", root -> root.get(Deal_.createdAt));
  private final DateDimension<Deal> updatedAt = new DateDimension<>("updatedAt", root -> root.get(Deal_.updatedAt));
  private final DateDimension<Deal> estimatedClosureOn = new DateDimension<>("estimatedClosureOn", root -> root.get(Deal_.estimatedClosureOn));
  private final DateDimension<Deal> actualClosureDate = new DateDimension<>("actualClosureDate", root -> root.get(Deal_.actualClosureDate));
  private final DateDimension<Deal> latestActivityCreatedAt = new DateDimension<>("latestActivityCreatedAt", root -> root.get(Deal_.latestActivityCreatedAt));
  private final DateDimension<Deal> taskDueOn = new DateDimension<>("taskDueOn", root -> root.get(Deal_.taskDueOn));
  private final DateDimension<Deal> meetingScheduledOn = new DateDimension<>("meetingScheduledOn", root -> root.get(Deal_.meetingScheduledOn));

  private final TextDimension<Deal> subSource = new TextDimension<>(
      "subSource",
      root -> getOrCreateLeftJoin(root, ContactUtm_.SUB_SOURCE).get(DealUtm_.subSource),
      emptyList());

  private final TextDimension<Deal> utmSource = new TextDimension<>(
      "utmSource",
      root -> getOrCreateLeftJoin(root, ContactUtm_.UTM_SOURCE).get(DealUtm_.utmSource),
      emptyList());

  private final TextDimension<Deal> utmMedium = new TextDimension<>(
      "utmMedium",
      root -> getOrCreateLeftJoin(root, ContactUtm_.UTM_MEDIUM).get(DealUtm_.utmMedium),
      emptyList());

  private final TextDimension<Deal> utmCampaign = new TextDimension<>(
      "utmCampaign",
      root -> getOrCreateLeftJoin(root, ContactUtm_.UTM_CAMPAIGN).get(DealUtm_.utmCampaign),
      emptyList());

  private final TextDimension<Deal> utmTerm = new TextDimension<>(
      "utmTerm",
      root -> getOrCreateLeftJoin(root, ContactUtm_.UTM_TERM).get(DealUtm_.utmTerm),
      emptyList());

  private final TextDimension<Deal> utmContent = new TextDimension<>(
      "utmContent",
      root -> getOrCreateLeftJoin(root, ContactUtm_.UTM_CONTENT).get(DealUtm_.utmContent),
      emptyList());

  DealMultiFieldDimension<Deal, DealCompany, DealContact, PipelineStage> dealMultiFieldDimension =
      new DealMultiFieldDimension<>(
          "multi_field",
          Deal_.DEAL_COMPANY,
          Deal_.ASSOCIATED_CONTACTS,
          Deal_.CURRENT_STAGE,
          root -> root.get(Deal_.id),
          root -> root.get(Deal_.EVENT_PAYLOAD),
          Deal_.dealCompany,
          Deal_.associatedContacts,
          Deal_.currentStage,
          join -> join.get(DealCompany_.name),
          join -> join.get(DealContact_.name),
          join -> join.get(PipelineStage_.name)
      );

  NumberFilterDimension<Deal, Long> idFilterDimension = new NumberFilterDimension<>(
      "id",
      root -> root.get(Deal_.id),
      Long::valueOf
  );

  private final NumberFilterDimension<Deal, Double> score = new NumberFilterDimension<>(
      "score",
      root -> root.get(Deal_.score),
      Double::valueOf
  );

  private final Map<String, GroupByDimension<Deal>> GROUP_BY_DIMENSIONS =
      new HashMap<>() {
        {
          put(pipeline.getName(), pipeline);
          put(pipelineStage.getName(), pipelineStage);
          put(ownedBy.getName(), ownedBy);
          put(products.getName(), products);
          put(dealCompany.getName(), dealCompany);
          put(pipelineStageReason.getName(), pipelineStageReason);
          put(forecastingType.getName(), forecastingType);
          put(campaign.getName(), campaign);
          put(source.getName(), source);
          put(createdAt.getName(), createdAt);
          put(updatedAt.getName(), updatedAt);
          put(estimatedClosureOn.getName(), estimatedClosureOn);
          put(actualClosureDate.getName(), actualClosureDate);
          put(latestActivityCreatedAt.getName(), latestActivityCreatedAt);
          put(taskDueOn.getName(), taskDueOn);
          put(meetingScheduledOn.getName(), meetingScheduledOn);
          put(createdBy.getName(), createdBy);
          put(updatedBy.getName(), updatedBy);
          put(importedBy.getName(), importedBy);
          put(associatedContacts.getName(), associatedContacts);
          put(currency.getName(), currency);
          put(subSource.getName(), subSource);
          put(utmSource.getName(), utmSource);
          put(utmCampaign.getName(), utmCampaign);
          put(utmMedium.getName(), utmMedium);
          put(utmTerm.getName(), utmTerm);
          put(utmContent.getName(), utmContent);
        }
      };

  private final HashMap<String, FilterDimension<Deal>> DIMENSIONS =
      new HashMap<>() {
        {
          put(pipeline.getName(), pipeline);
          put(pipelineStage.getName(), pipelineStage);
          put(ownedBy.getName(), ownedBy);
          put(products.getName(), products);
          put(dealCompany.getName(), dealCompany);
          put(pipelineStageReason.getName(), pipelineStageReason);
          put(forecastingType.getName(), forecastingType);
          put(campaign.getName(), campaign);
          put(source.getName(), source);
          put(associatedContacts.getName(), associatedContacts);
          put(createdAt.getName(), createdAt);
          put(updatedAt.getName(), updatedAt);
          put(estimatedClosureOn.getName(), estimatedClosureOn);
          put(actualClosureDate.getName(), actualClosureDate);
          put(latestActivityCreatedAt.getName(), latestActivityCreatedAt);
          put(taskDueOn.getName(), taskDueOn);
          put(meetingScheduledOn.getName(), meetingScheduledOn);
          put("name", new TextJsonBDimension<>("name", root -> root.get(Deal_.EVENT_PAYLOAD)));
          put("estimatedValue", new NumberFilterDimension<>(
              "estimatedValue",
              root -> root.get(Deal_.estimatedValue).get(Money_.amount),
              Double::valueOf
          ));
          put("actualValue", new NumberFilterDimension<>(
              "actualValue",
              root -> root.get(Deal_.actualValue).get(Money_.amount),
              Double::valueOf
          ));
          put("isNew", new TextJsonBDimension<>("isNew", root -> root.get(Deal_.EVENT_PAYLOAD)));
          put("createdViaId", new TextJsonBDimension<>("createdViaId", root -> root.get(Deal_.EVENT_PAYLOAD)));
          put("createdViaName", new TextJsonBDimension<>("createdViaName", root -> root.get(Deal_.EVENT_PAYLOAD)));
          put("createdViaType", new TextJsonBDimension<>("createdViaType", root -> root.get(Deal_.EVENT_PAYLOAD)));
          put("updatedViaId", new TextJsonBDimension<>("updatedViaId", root -> root.get(Deal_.EVENT_PAYLOAD)));
          put("updatedViaName", new TextJsonBDimension<>("updatedViaName", root -> root.get(Deal_.EVENT_PAYLOAD)));
          put("updatedViaType", new TextJsonBDimension<>("updatedViaType", root -> root.get(Deal_.EVENT_PAYLOAD)));
          put(createdBy.getName(), createdBy);
          put(updatedBy.getName(), updatedBy);
          put(importedBy.getName(), importedBy);
          put(currency.getName(), currency);
          put(subSource.getName(), subSource);
          put(utmSource.getName(), utmSource);
          put(utmCampaign.getName(), utmCampaign);
          put(utmMedium.getName(), utmMedium);
          put(utmTerm.getName(), utmTerm);
          put(utmContent.getName(), utmContent);
          put(dealMultiFieldDimension.getName(), dealMultiFieldDimension);
          put(idFilterDimension.getName(), idFilterDimension);
          put(score.getName(), score);
        }
      };

  private final List<Metric<Deal>> SUPPORTED_METRICS =
      new ArrayList<>() {{
        add(new Metric<>(
            "id",
            MetricType.COUNT,
            null,
            null));

        add(new Metric<>(
            "estimatedValue",
            MetricType.SUM,
            null,
            null));

        add(new Metric<>(
            "estimatedValue",
            MetricType.AVERAGE,
            null,
            null));

        add(new Metric<>(
            "actualValue",
            MetricType.SUM, null, null));

        add(new Metric<>(
            "actualValue",
            MetricType.AVERAGE,
            null, null));

        add(new Metric<>(
            "numberOfProducts",
            MetricType.COUNT,
            null,
            null));
        add(new Metric<>(
            score.getName(),
            MetricType.SUM,
            (root, builder) -> builder.sum(root.get(Deal_.score)),
            null));
        add(new Metric<>(
            score.getName(),
            MetricType.AVERAGE,
            (root, builder) -> builder.avg(root.get(Deal_.score)),
            null));
      }};

  @Override
  public DimensionDetail<Deal> getDimensionDetail() {
    return new DimensionDetail<>(GROUP_BY_DIMENSIONS, DIMENSIONS, SUPPORTED_METRICS);
  }

  @Override
  public List<Metric<Deal>> getDealMetrics(Long currencyId) {
    return DealMetricBuilder.buildDealMetrics(currencyId);
  }

  private Join<Deal, DealUtm> getOrCreateLeftJoin(Root<Deal> root, String dimensionName) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<Deal, DealUtm>) j))
        .findFirst()
        .orElseGet(() -> root.join(Deal_.dealUtm, JoinType.LEFT));
  }

  @Override
  public GroupByDimension<Deal> createUserPropertyGroupByIdDimension(GroupByField groupByField) {
    DealUserMapping dealUserMapping = MAPPED_PRIMARY_PROPERTIES.get(groupByField.getPrimaryField());
    if ("teams".equals(groupByField.getProperty())) {
      return new UserPropertyIdDimension<>(
          groupByField.getName(),
          dealUserMapping.getMappedColumnFieldName(),
          dealUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateLeftJoin(join, groupByField.getProperty()).get(Team_.id),
          join -> getOrCreateLeftJoin(join, groupByField.getProperty()).get(Team_.name),
          emptyList(),
          groupByField.getPrimaryField(),
          groupByField.getProperty()
      );
    }
    return null;
  }

  @Override
  public FilterDimension<Deal> createUserPropertyFilterIdDimension(Filter filter) {
    DealUserMapping dealUserMapping = MAPPED_PRIMARY_PROPERTIES.get(filter.getPrimaryField());
    if ("teams".equals(filter.getProperty())) {
      return new UserPropertyIdDimension<>(
          filter.getFieldName(),
          dealUserMapping.getMappedColumnFieldName(),
          dealUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateLeftJoin(join, filter.getProperty()).get(Team_.id),
          join -> getOrCreateLeftJoin(join, filter.getProperty()).get(Team_.name),
          emptyList(),
          filter.getPrimaryField(),
          filter.getProperty()
      );
    }
    return null;
  }

  private Join<User, Team> getOrCreateLeftJoin(Join<Deal, User> dealUserJoin, String dimensionName) {
    return dealUserJoin.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<User, Team>) j))
        .findFirst()
        .orElseGet(() -> dealUserJoin.join(User_.teams, JoinType.LEFT));
  }
}
