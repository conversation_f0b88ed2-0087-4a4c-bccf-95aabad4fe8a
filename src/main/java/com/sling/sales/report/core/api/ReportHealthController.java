package com.sling.sales.report.core.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/vYWJOAWPLZXRTZDEY/reports")
public class ReportHealthController {
  private final ReportHealthService reportHealthService;

  @Autowired
  public ReportHealthController(ReportHealthService reportHealthService) {
    this.reportHealthService = reportHealthService;
  }

  @GetMapping(value = "/health", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<Integer> getCompanyHealth() {
    int reportHealthResponse = reportHealthService.getHealthStatus();

    if (reportHealthResponse == 200) {
      return ResponseEntity.ok().build();
    }
    return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).build();
  }
}
