package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.api.request.AggregationRequestV3;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.Operator;
import com.sling.sales.report.core.domain.aggregation.exception.UnsupportedFilterException;
import com.sling.sales.report.core.domain.aggregation.exception.UnsupportedGroupByException;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.deal.Deal;
import com.sling.sales.report.dto.DimensionDetail;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Root;
import org.apache.commons.lang3.ObjectUtils;

public abstract class FactFieldFactory<T extends Fact> {

  public abstract DimensionDetail<T> getDimensionDetail();

  public abstract GroupByDimension<T> createUserPropertyGroupByIdDimension(GroupByField groupByField);

  public abstract FilterDimension<T> createUserPropertyFilterIdDimension(Filter filter);

  public FilterDimension<T> getFilterField(
      String fieldName, Operator operator, Map<String, FilterDimension<T>> customFilterDimensions) {
    Map<String, FilterDimension<T>> filterDimensions = new HashMap<>();
    filterDimensions.putAll(getDimensionDetail().getFilterDimensions());
    filterDimensions.putAll(customFilterDimensions);
    if (filterDimensions.containsKey(fieldName) && filterDimensions.get(fieldName).supports(operator)) {
      return filterDimensions.get(fieldName);
    }
    throw new UnsupportedFilterException();
  }

  public List<GroupByDimension<T>> getGroupByFields(List<GroupByField> groupByFields, Map<String, GroupByDimension<T>> customGroupByDimensions) {
    return groupByFields
        .stream()
        .map(groupByField -> {
          if (ObjectUtils.isEmpty(groupByField.getPrimaryField()) && ObjectUtils.isEmpty(groupByField.getProperty())) {
            return getGroupByDimensionByFieldName(groupByField, customGroupByDimensions);
          }
          return createUserPropertyGroupByIdDimension(groupByField);
        })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private GroupByDimension<T> getGroupByDimensionByFieldName(GroupByField groupByField, Map<String, GroupByDimension<T>> customGroupByDimensions) {
    Map<String, GroupByDimension<T>> groupByDimensions = new HashMap<>();
    groupByDimensions.putAll(getDimensionDetail().getGroupByDimensions());
    groupByDimensions.putAll(customGroupByDimensions);
    if (groupByDimensions.containsKey(groupByField.getName())) {
      return groupByDimensions.get(groupByField.getName())
          .withFormat(groupByField.getFormat());
    }
    throw new UnsupportedGroupByException();
  }

  public List<BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>>> getMetricSelector(boolean isDealProductsPresent,
      List<AggregationRequestV3.Metric> metrics, List<Metric<T>> customFieldMetrics, Class<T> clazz, Long currencyId) {

    List<Metric<T>> allMetrics = new ArrayList<>();
    allMetrics.addAll(getMetrics(clazz, currencyId));
    allMetrics.addAll(customFieldMetrics);

    return metrics.stream()
        .map(metric -> getBiFunction(isDealProductsPresent, allMetrics, metric))
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private List<Metric<T>> getMetrics(Class<T> clazz, Long currencyId) {
    if (clazz.equals(Deal.class)) {
      return getDealMetrics(currencyId);
    }
    return getDimensionDetail().getMetrics();
  }

  private BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>> getBiFunction(boolean isDealProductsPresent, List<Metric<T>> allMetrics,
      AggregationRequestV3.Metric metric) {
    return allMetrics.stream()
        .filter(m -> metric.getField().equals(m.getField()) && metric.getType() == m.getType())
        .findFirst()
        .map(filteredMetric -> buildBiFunction(isDealProductsPresent, filteredMetric))
        .orElse(null);
  }

  private BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>> buildBiFunction(boolean isDealProductsPresent,
      Metric<T> metric) {
    if (isDealProductsPresent && (metric.getField().equals("estimatedValue") || metric.getField().equals("actualValue"))) {
      return metric.getDealProductMetricSelector();
    }
    return metric.getMetricSelector();
  }

  public List<Metric<T>> getDealMetrics(Long currencyId) {
    return Collections.emptyList();
  }
}
