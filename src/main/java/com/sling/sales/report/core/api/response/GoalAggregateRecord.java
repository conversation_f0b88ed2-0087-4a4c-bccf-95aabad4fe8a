package com.sling.sales.report.core.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoalAggregateRecord {

  private final Long id;
  private final String name;
  @JsonIgnore
  private long goalFieldValueId;
  private final List<GoalCount> value;

  @JsonCreator
  public GoalAggregateRecord(@JsonProperty("id") Long id, @JsonProperty("name") String name, @JsonProperty("value") List<GoalCount> value) {
    this.id = id;
    this.name = name;
    this.value = value;
  }

  public GoalAggregateRecord withGoalFieldValueId(long goalFieldValueId) {
    this.goalFieldValueId = goalFieldValueId;
    return this;
  }
}
