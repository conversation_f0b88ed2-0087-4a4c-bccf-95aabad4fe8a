package com.sling.sales.report.core.domain.contact;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
interface ContactCustomMultiPicklistValueRepository extends JpaRepository<ContactCustomMultiPicklistValue, Long> {

  @Transactional
  @Modifying
  @Query("UPDATE ContactCustomMultiPicklistValue SET displayName = :displayName WHERE picklistValueId = :picklistValueId")
  void updateDisplayNameByPicklistValueId(@Param("picklistValueId") long picklistValueId, @Param("displayName") String displayName);

}