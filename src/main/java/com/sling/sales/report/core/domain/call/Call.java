package com.sling.sales.report.core.domain.call;

import static org.hibernate.annotations.CascadeType.MERGE;
import static org.hibernate.annotations.CascadeType.PERSIST;
import static org.hibernate.annotations.CascadeType.REFRESH;
import static org.hibernate.annotations.CascadeType.REPLICATE;
import static org.hibernate.annotations.CascadeType.SAVE_UPDATE;

import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.security.domain.User;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

@Entity
@Getter
@Setter
@TypeDefs({@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)})
public class Call implements Fact {

  @Id
  private Long id;

  private long tenantId;

  private String callType;

  private String outcome;

  @ElementCollection
  private List<String> entities;

  private Double durationInMinutes;

  private Double durationInHours;

  private Double callbackDurationInMinutes;

  private boolean recordingAvailable;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "owner", insertable = false, updatable = false)
  private User owner;

  @Column(name = "owner")
  private long ownerId;

  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  @NotNull
  private CallEventData eventPayload;

  private Date callConductedAt;

  private Date createdAt;

  private Date updatedAt;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "created_by", insertable = false,updatable = false)
  private User createdBy;

  @Column(name = "created_by")
  private Long createdById;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "updated_by",insertable = false, updatable = false)
  private User updatedBy;

  @Column(name = "updated_by")
  private Long updatedById;

  private String ivrNumber;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "call_disposition_id")
  @Cascade({MERGE, PERSIST, REFRESH, SAVE_UPDATE, REPLICATE})
  private CallDisposition callDisposition;

  private String overallSentiment;

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
  @Setter(AccessLevel.NONE)
  @JoinColumn(name = "call_id")
  private Set<CustomerEmotion> customerEmotion = new HashSet<>();

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "call_id")
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<CallCustomPicklistValue> callPicklistValues = new HashSet<>();

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  @JoinColumn(name = "call_id")
  private Set<CallCustomTextValue> callCustomTextValues = new HashSet<>();

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  @JoinColumn(name = "call_id")
  private Set<CallCustomNumberValue> callCustomNumberValues = new HashSet<>();

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  @JoinColumn(name = "call_id")
  private Set<CallCustomDatePickerValue> callCustomDatePickerValues = new HashSet<>();

  @OneToMany(fetch = FetchType.LAZY,  cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  @JoinColumn(name = "call_id")
  private Set<CallCustomDatetimePickerValue> callCustomDatetimePickerValues = new HashSet<>();

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  @JoinColumn(name = "call_id")
  private Set<CallCustomCheckboxValue> callCustomCheckboxValues = new HashSet<>();

  @NotEmpty
  private String phoneNumber;
  private String originator;
  private String receiver;
  @NotNull
  private boolean manual;
  private String deviceId;
  private boolean deleted;

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL,  orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  @JoinColumn(name = "call_id")
  private Set<CallRelatedTo> callRelatedTos = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  @JoinColumn(name = "call_id")
  private Set<CallAssociatedTo> callAssociatedTos = new HashSet<>();

  public void addCallPicklistValues(Set<CallCustomPicklistValue> callPicklistValues) {
    this.callPicklistValues.addAll(callPicklistValues);
  }

  public Set<CallCustomPicklistValue> getCallPicklistValues() {
    return Collections.unmodifiableSet(this.callPicklistValues);
  }

  public void addCallCustomTextValues(Set<CallCustomTextValue> callCustomTextValues) {
    this.callCustomTextValues.addAll(callCustomTextValues);
  }

  public Set<CallCustomTextValue> getCallCustomTextValues() {
    return Collections.unmodifiableSet(this.callCustomTextValues);
  }

  public void addCallCustomNumberValues(Set<CallCustomNumberValue> callCustomNumberValues) {
    this.callCustomNumberValues.addAll(callCustomNumberValues);
  }

  public Set<CallCustomNumberValue> getCallCustomNumberValues() {
    return Collections.unmodifiableSet(this.callCustomNumberValues);
  }

  public void addCallCustomDatePickerValues(Set<CallCustomDatePickerValue> callCustomDatePickerValues) {
    this.callCustomDatePickerValues.addAll(callCustomDatePickerValues);
  }

  public Set<CallCustomDatePickerValue> getCallCustomDatePickerValues() {
    return Collections.unmodifiableSet(this.callCustomDatePickerValues);
  }

  public void addCallCustomDatetimePickerValues(Set<CallCustomDatetimePickerValue> callCustomDatetimePickerValues) {
    this.callCustomDatetimePickerValues.addAll(callCustomDatetimePickerValues);
  }

  public Set<CallCustomDatetimePickerValue> getCallCustomDatetimePickerValues() {
    return Collections.unmodifiableSet(this.callCustomDatetimePickerValues);
  }

  public void addCallCustomCheckboxValues(Set<CallCustomCheckboxValue> callCustomCheckboxValues) {
    this.callCustomCheckboxValues.addAll(callCustomCheckboxValues);
  }

  public Set<CallCustomCheckboxValue> getCallCustomCheckboxValues() {
    return Collections.unmodifiableSet(this.callCustomCheckboxValues);
  }

  public void addCallRelatedTos(Set<CallRelatedTo> callRelatedTos) {
    this.callRelatedTos.addAll(callRelatedTos);
  }

  public Set<CallRelatedTo> getCallRelatedTos() {
    return Collections.unmodifiableSet(this.callRelatedTos);
  }

  public void addCallAssociatedTos(Set<CallAssociatedTo> callAssociatedTos) {
    this.callAssociatedTos.addAll(callAssociatedTos);
  }

  public Set<CallAssociatedTo> getCallAssociatedTos() {
    return Collections.unmodifiableSet(this.callAssociatedTos);
  }

  public void addCustomerEmotion(Set<CustomerEmotion> customerEmotion) {
    this.customerEmotion = new HashSet<>(customerEmotion);
  }
}
