package com.sling.sales.report.core.api.response;


import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.dto.GoalFilter;
import com.sling.sales.report.dto.GoalFrequency;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReportGoalResponse {

  private long id;
  private String name;
  private GoalFrequency goalFrequency;
  private boolean active;
  private List<GoalFilter> filters;
  private Action recordActions;
  private List<IdName> owners;


  @JsonCreator
  public ReportGoalResponse(@JsonProperty("id") long id, @JsonProperty("name") String name, @JsonProperty("frequency") GoalFrequency goalFrequency,
      @JsonProperty("active") boolean active, @JsonProperty("filters") List<GoalFilter> filters,
      @JsonProperty("recordActions") Action recordActions, @JsonProperty("owners") List<IdName> owners) {
    this.id = id;
    this.name = name;
    this.goalFrequency = goalFrequency;
    this.active = active;
    this.filters = filters;
    this.recordActions = recordActions;
    this.owners = owners;
  }
}
