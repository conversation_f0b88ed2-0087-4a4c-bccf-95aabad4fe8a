package com.sling.sales.report.core.api.response;

import com.sling.sales.report.core.api.request.ReportCategory;
import com.sling.sales.report.core.domain.aggregation.exception.InvalidFilterRuleException;
import com.sling.sales.report.core.domain.report.ChartType;
import com.sling.sales.report.core.domain.report.Report;
import com.sling.sales.report.core.domain.report.ReportSpecification;
import com.sling.sales.report.core.domain.report.ReportType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

@Slf4j
public class ReportFilter {

  private final String operator;
  private final String field;
  private final String type;
  private final Object value;
  private static final List<String> validOperators = Arrays.asList("is_null", "is_not_null", "is_empty", "is_not_empty");

  public ReportFilter(String operator, String field, String type, Object value) {
    verify(operator, field, type, value);
    this.operator = operator;
    this.field = field;
    this.type = type;
    this.value = convertValue(field, value);
  }

  private Object convertValue(String field, Object value) {
    List<String> values = new ArrayList<>();
    boolean isList = value instanceof List;
    if (isList) {
      values.addAll((List<String>) value);
    }
    try {
      if ("reportType".equalsIgnoreCase(field) && value != null) {
        return isList ?
            values.stream()
                .map(reportType -> ReportType.valueOf(reportType.toUpperCase()))
                .collect(Collectors.toList()) :
            ReportType.valueOf(value.toString().toUpperCase());
      }
      if ("chartType".equalsIgnoreCase(field) && value != null) {
        return isList ? values.stream()
            .map(chartType -> ChartType.valueOf(chartType.toUpperCase()))
            .collect(Collectors.toList()) : ChartType.valueOf(value.toString().toUpperCase());
      }
      if ("category".equalsIgnoreCase(field) && value != null) {
        return isList ? values.stream()
            .map(reportCategory -> ReportCategory.valueOf(reportCategory.toUpperCase()))
            .collect(Collectors.toList()) : ReportCategory.valueOf(value.toString().toUpperCase());
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new InvalidFilterRuleException();
    }
    return value;
  }

  private void verify(String operator, String field, String type, Object value) {

    if (StringUtils.isBlank(operator)
        || StringUtils.isBlank(field)
        || StringUtils.isBlank(type)) {
      log.info(
          "Search job operator {}, fieldName {}, type {} can not be blank",
          operator,
          field,
          type);
      throw new InvalidFilterRuleException();
    }
    if (validOperators.contains(operator)) {
      return;
    }
    if (value == null) {
      log.info("Search job operator {} value can not be blank", operator);
      throw new InvalidFilterRuleException();
    }
    if ("between".equalsIgnoreCase(operator) || "not_between".equalsIgnoreCase(operator)) {
      if (!(value instanceof List)) {
        log.info(
            "Search job operator {} and value {}  is mismatch value must be of list",
            operator,
            value);
        throw new InvalidFilterRuleException();
      }
      List values = (List) value;
      if (values.size() != 2) {
        log.info(
            "Search job operator {} and value {}  is mismatch size is 2 expected",
            operator,
            value);
        throw new InvalidFilterRuleException();
      }
      if (StringUtils.isBlank(String.valueOf(values.get(0)))
          || StringUtils.isBlank(String.valueOf(values.get(1)))) {
        log.info(
            "Search job operator {} and value {}  both value must not be blank",
            operator,
            value);
        throw new InvalidFilterRuleException();
      }
    }
    if ("".equals(value)) {
      log.info("Search job operator {} value {} must not be blank", operator, value);
      throw new InvalidFilterRuleException();
    }
  }

  public Specification<Report> toSpecification() {
    switch (this.operator) {
      case "equal":
        return ReportSpecification.isFieldEqualTo(this.field, this.value);
      case "not_equal":
        return ReportSpecification.isFieldNotEqualTo(this.field, this.value);
      case "greater":
        return ReportSpecification.isFieldGraterThan(this.field, this.value);
      case "greater_or_equal":
        return ReportSpecification.isFieldGraterThanOrEqualTo(this.field, this.value);
      case "less":
        return ReportSpecification.isFieldLessThan(this.field, this.value);
      case "less_or_equal":
        return ReportSpecification.isFieldLessThanOrEqualTo(this.field, this.value);
      case "between":
        return ReportSpecification.isFieldBetween(this.field, this.value);
      case "not_between":
        return ReportSpecification.isFieldNotBetween(this.field, this.value);
      case "in":
        return ReportSpecification.isFieldIn(this.field, this.type, this.value);
      case "not_in":
        return ReportSpecification.isFieldNotIn(this.field, this.type, this.value);
      case "is_not_null":
      case "is_not_empty":
        return ReportSpecification.isFieldNotNull(this.field);
      case "is_null":
      case "is_empty":
        return ReportSpecification.isFieldNull(this.field);
      case "begins_with":
        return ReportSpecification.isFieldBeginsWith(this.field, this.value);
      case "contains":
        return ReportSpecification.isFieldContains(this.field, this.value);
      case "not_contains":
        return ReportSpecification.isFieldNotContains(this.field, this.value);
      case "multi_field":
        return ReportSpecification.isMultiFieldsLike(this.value);
      default:
        throw new InvalidFilterRuleException();
    }
  }
}
