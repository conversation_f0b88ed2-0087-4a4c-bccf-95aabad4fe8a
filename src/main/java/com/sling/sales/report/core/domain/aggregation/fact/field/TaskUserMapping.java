package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.task.Task;
import com.sling.sales.report.security.domain.User;
import javax.persistence.metamodel.SingularAttribute;
import lombok.Getter;

@Getter
public class TaskUserMapping {

  private final String mappedColumnFieldName;
  private final SingularAttribute<Task, User> mappedColumnFieldPath;

  public TaskUserMapping(String mappedColumnFieldName, SingularAttribute<Task, User> mappedColumnFieldPath) {
    this.mappedColumnFieldName = mappedColumnFieldName;
    this.mappedColumnFieldPath = mappedColumnFieldPath;
  }
}
