package com.sling.sales.report.core.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.api.request.AggregationRequestV1;
import com.sling.sales.report.core.api.request.ReportCategory;
import com.sling.sales.report.security.domain.User;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReportResponseV1 {

  private final Long id;
  private final String name;
  private final String description;
  private final String reportType;
  private final String chartType;
  private final AggregationRequestV1 config;
  private Action recordActions;
  private final User createdBy;
  private final ReportCategory category;

  @JsonCreator
  public ReportResponseV1(
      @JsonProperty("id") Long id,
      @JsonProperty("name") String name,
      @JsonProperty("description") String description,
      @JsonProperty("reportType") String reportType,
      @JsonProperty("chartType") String chartType,
      @JsonProperty("config") AggregationRequestV1 config,
      @JsonProperty("recordActions") Action recordActions,
      @JsonProperty("createdBy") User createdBy, ReportCategory category) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.reportType = reportType;
    this.chartType = chartType;
    this.config = config;
    this.recordActions = recordActions;
    this.createdBy = createdBy;
    this.category = category;
  }

}
