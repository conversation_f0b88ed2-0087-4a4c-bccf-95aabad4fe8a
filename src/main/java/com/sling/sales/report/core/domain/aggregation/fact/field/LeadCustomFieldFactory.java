package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.aggregation.dimension.DateCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyBooleanCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyTextCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberCustomFieldFilterDimension;
import com.sling.sales.report.core.domain.lead.Lead;
import com.sling.sales.report.core.domain.lead.LeadCustomCheckboxValue;
import com.sling.sales.report.core.domain.lead.LeadCustomCheckboxValue_;
import com.sling.sales.report.core.domain.lead.LeadCustomDatePickerValue;
import com.sling.sales.report.core.domain.lead.LeadCustomDatePickerValue_;
import com.sling.sales.report.core.domain.lead.LeadCustomDatetimePickerValue;
import com.sling.sales.report.core.domain.lead.LeadCustomDatetimePickerValue_;
import com.sling.sales.report.core.domain.lead.LeadCustomMultiPicklistValue;
import com.sling.sales.report.core.domain.lead.LeadCustomMultiPicklistValue_;
import com.sling.sales.report.core.domain.lead.LeadCustomNumberValue;
import com.sling.sales.report.core.domain.lead.LeadCustomNumberValue_;
import com.sling.sales.report.core.domain.lead.LeadCustomTextValue;
import com.sling.sales.report.core.domain.lead.LeadCustomTextValue_;
import com.sling.sales.report.core.domain.lead.LeadPicklistValue;
import com.sling.sales.report.core.domain.lead.LeadPicklistValue_;
import com.sling.sales.report.core.domain.lead.Lead_;
import com.sling.sales.report.dto.DimensionDetail;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;

public class LeadCustomFieldFactory {

  private LeadCustomFieldFactory() {

  }

  public static DimensionDetail<Lead> createManyIdCustomFieldDimension(String dimensionName, long fieldId) {
    ManyIdCustomFieldDimension<Lead, LeadPicklistValue> manyIdCustomFieldDimension = new ManyIdCustomFieldDimension<>(
        dimensionName,
        Lead_.LEAD_PICKLIST_VALUES,
        Lead_.leadPicklistValues,
        join -> join.get(LeadPicklistValue_.picklistValueId),
        join -> join.get(LeadPicklistValue_.displayName),
        join -> join.get(LeadPicklistValue_.fieldId),
        fieldId, LeadPicklistValue_.DISPLAY_NAME);

    List<Metric<Lead>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Lead_.leadPicklistValues, join -> join.get(LeadPicklistValue_.fieldId),
              LeadPicklistValue_.PICKLIST_VALUE_ID, fieldId),
          null));
    }};

    return new DimensionDetail<>(manyIdCustomFieldDimension, manyIdCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Lead> createManyTextCustomFieldDimension(String dimensionName, long fieldId) {
    ManyTextCustomFieldDimension<Lead, LeadCustomTextValue> manyTextCustomFieldDimension = new ManyTextCustomFieldDimension<>(
        dimensionName,
        Lead_.LEAD_CUSTOM_TEXT_VALUES,
        Lead_.leadCustomTextValues,
        join -> join.get(LeadCustomTextValue_.value),
        join -> join.get(LeadCustomTextValue_.fieldId),
        fieldId
    );

    List<Metric<Lead>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Lead_.leadCustomTextValues, join -> join.get(LeadCustomTextValue_.fieldId),
              LeadCustomTextValue_.VALUE, fieldId),
          null));
    }};
    return new DimensionDetail<>(manyTextCustomFieldDimension, manyTextCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Lead> createNumberCustomFieldFilterDimension(String dimensionName, long fieldId) {
    NumberCustomFieldFilterDimension<Lead, LeadCustomNumberValue> numberCustomFieldFilterDimension = new NumberCustomFieldFilterDimension<>(
        dimensionName,
        Lead_.LEAD_CUSTOM_NUMBER_VALUES,
        Lead_.leadCustomNumberValues,
        join -> join.get(LeadCustomNumberValue_.value),
        join -> join.get(LeadCustomNumberValue_.fieldId), fieldId);

    List<Metric<Lead>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Lead_.leadCustomNumberValues, join -> join.get(LeadCustomNumberValue_.fieldId),
              LeadCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.SUM,
          getBiFunction(MetricType.SUM, Lead_.leadCustomNumberValues, join -> join.get(LeadCustomNumberValue_.fieldId),
              LeadCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.AVERAGE,
          getBiFunction(MetricType.AVERAGE, Lead_.leadCustomNumberValues, join -> join.get(LeadCustomNumberValue_.fieldId),
              LeadCustomNumberValue_.VALUE, fieldId),
          null));
    }};

    return new DimensionDetail<>(null, numberCustomFieldFilterDimension, metrics);
  }

  public static DimensionDetail<Lead> createBooleanCustomFieldDimension(String dimensionName, long fieldId) {
    ManyBooleanCustomFieldDimension<Lead, LeadCustomCheckboxValue> manyBooleanCustomFieldDimension = new ManyBooleanCustomFieldDimension<>(
        dimensionName,
        Lead_.LEAD_CUSTOM_CHECKBOX_VALUES,
        Lead_.leadCustomCheckboxValues,
        join -> join.get(LeadCustomCheckboxValue_.value),
        join -> join.get(LeadCustomCheckboxValue_.fieldId),
        fieldId
    );
    return new DimensionDetail<>(manyBooleanCustomFieldDimension, manyBooleanCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Lead> createDatePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Lead, LeadCustomDatePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Lead_.LEAD_CUSTOM_DATE_PICKER_VALUES,
        Lead_.leadCustomDatePickerValues,
        join -> join.get(LeadCustomDatePickerValue_.value),
        join -> join.get(LeadCustomDatePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Lead> createDatetimePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Lead, LeadCustomDatetimePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Lead_.LEAD_CUSTOM_DATETIME_PICKER_VALUES,
        Lead_.leadCustomDatetimePickerValues,
        join -> join.get(LeadCustomDatetimePickerValue_.value),
        join -> join.get(LeadCustomDatetimePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Lead> createCustomMultiFieldDimension(String dimensionName, long fieldId) {
    ManyIdCustomFieldDimension<Lead, LeadCustomMultiPicklistValue> customMultiPicklistDimension = new ManyIdCustomFieldDimension<>(
        dimensionName,
        Lead_.LEAD_CUSTOM_MULTI_PICKLIST_VALUES,
        Lead_.leadCustomMultiPicklistValues,
        join -> join.get(LeadCustomMultiPicklistValue_.picklistValueId),
        join -> join.get(LeadCustomMultiPicklistValue_.displayName),
        join -> join.get(LeadCustomMultiPicklistValue_.fieldId),
        fieldId, LeadCustomMultiPicklistValue_.DISPLAY_NAME);

    List<Metric<Lead>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Lead_.leadCustomMultiPicklistValues, join -> join.get(LeadCustomMultiPicklistValue_.fieldId),
              LeadCustomMultiPicklistValue_.PICKLIST_VALUE_ID, fieldId),
          null));
    }};

    return new DimensionDetail<>(customMultiPicklistDimension, customMultiPicklistDimension, metrics);
  }

  private static <T, V> BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>> getBiFunction(MetricType metricType,
      SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> getFieldId, String attributeName, long fieldId) {

    return (root, builder) -> CustomFieldMetricBuilder
        .buildExpressionByMetricType(metricType, root, builder, dimensionField, getFieldId,
            attributeName, fieldId);
  }

}
