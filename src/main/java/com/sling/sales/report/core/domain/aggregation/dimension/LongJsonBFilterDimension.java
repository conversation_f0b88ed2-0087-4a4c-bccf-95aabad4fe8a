package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.in;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_in;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;

public class LongJsonBFilterDimension<T extends Fact> implements FilterDimension<T> {

  private final String name;
  private final List<Operator> allowedOperators = asList(equal, not_equal, is_null, is_not_null, in, not_in);
  private final Function<Root<T>, Path<String>> getPathToJsonbColumn;

  public LongJsonBFilterDimension(String name,
      Function<Root<T>, Path<String>> getPathToJsonbColumn) {
    this.name = name;
    this.getPathToJsonbColumn = getPathToJsonbColumn;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    switch (operator) {
      case equal:
        return hasId(String.valueOf(value));
      case not_equal:
        return doesNotHaveId(String.valueOf(value)).or(isNull());
      case is_null:
        return isNull();
      case is_not_null:
        return isNotNull();
      case in:
        return in(getParameters(value));
      case not_in:
        return notIn(getParameters(value)).or(isNull());
      default:
        return null;
    }
  }

  private Specification<T> hasId(String value) {
    return ((root, query, builder) ->
        builder.equal(
            builder.function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name)),
            value
        )
    );
  }

  private Specification<T> doesNotHaveId(String value) {
    return ((root, query, builder) ->
        builder.notEqual(
            builder.function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name)),
            value
        )
    );
  }

  Specification<T> isNull() {
    return ((root, query, builder) -> builder.isNull(
        builder.function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name))
    ));
  }

  Specification<T> isNotNull() {
    return ((root, query, builder) -> builder.isNotNull(
        builder.function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name))
    ));
  }

  private List<String> getParameters(Object value) {
    if (value instanceof List) {
      List<Object> objectsList = (List<Object>) value;
      return objectsList.get(0) instanceof Number ? toString((List<Number>) value) : (List<String>) value;
    }
    return Arrays.stream(String.valueOf(value).split(","))
        .map(String::trim)
        .collect(toList());
  }

  private List<String> toString(List<Number> values) {
    return values
        .stream()
        .map(String::valueOf)
        .collect(Collectors.toList());
  }

  private Specification<T> in(List<String> values) {
    return ((root, query, builder) -> {
      Expression<String> jsonbExpression = builder
          .function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name));
      return jsonbExpression.in(values);
    });
  }

  private Specification<T> notIn(List<String> values) {
    return ((root, query, builder) -> {
      Expression<String> jsonbExpression = builder
          .function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name));
      return jsonbExpression.in(values).not();
    });
  }
}
