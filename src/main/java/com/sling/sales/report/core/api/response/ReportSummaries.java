package com.sling.sales.report.core.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.api.request.AggregationRequestV3;
import com.sling.sales.report.core.api.request.ReportCategory;
import com.sling.sales.report.core.domain.report.ChartType;
import com.sling.sales.report.core.domain.report.ReportType;
import com.sling.sales.report.dto.HeaderDetail;
import com.sling.sales.report.mq.event.IdName;
import com.sling.sales.report.security.domain.User;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReportSummaries {
    private final List<ReportWiseSummary> content;

    @JsonCreator
    public ReportSummaries(@JsonProperty("content") List<ReportWiseSummary> content) {
        this.content = content;
    }

    @Getter
    public static class ReportWiseSummary {

        private final long id;
        private final String name;
        private final ChartType chartType;
        private final ReportCategory reportCategory;
        private final List<AggregateRecord> data;
        private final GoalAggregateResponse goalReportData;
        private final HeaderDetail headerDetail;
        private final AggregationRequestV3 config;
        private final String description;
        private final ReportType reportType;
        private final Action recordActions;
        private final User createdBy;
        private final ReportGoalResponse goal;
        private final boolean systemDefault;
        private final boolean prorated;
        private final IdName currency;

        @JsonCreator
        public ReportWiseSummary(
            @JsonProperty("id") long id,
            @JsonProperty("name") String name,
            @JsonProperty("chartType") ChartType chartType,
            @JsonProperty("reportCategory") ReportCategory reportCategory,
            @JsonProperty("data") List<AggregateRecord> data,
            @JsonProperty("goalReportData") GoalAggregateResponse goalReportData, @JsonProperty("headerDetail") HeaderDetail headerDetail,
            @JsonProperty("config") AggregationRequestV3 config,
            @JsonProperty("description") String description,
            @JsonProperty("reportType") ReportType reportType,
            @JsonProperty("recordActions") Action recordActions,
            @JsonProperty("createdBy") User createdBy, @JsonProperty("goal") ReportGoalResponse goal,
            @JsonProperty("systemDefault") boolean systemDefault,
            @JsonProperty("prorated") boolean prorated,
            @JsonProperty("currency") IdName currency) {
            this.id = id;
            this.name = name;
            this.chartType = chartType;
            this.reportCategory = reportCategory;
            this.data = data;
            this.goalReportData = goalReportData;
            this.headerDetail = headerDetail;
            this.config = config;
            this.description = description;
            this.reportType = reportType;
            this.recordActions = recordActions;
            this.createdBy = createdBy;
            this.goal = goal;
            this.systemDefault = systemDefault;
            this.prorated = prorated;
            this.currency = currency;
        }
    }
}
