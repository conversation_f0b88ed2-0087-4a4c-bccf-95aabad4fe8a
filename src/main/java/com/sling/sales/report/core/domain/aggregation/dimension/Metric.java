package com.sling.sales.report.core.domain.aggregation.dimension;

import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import java.util.function.BiFunction;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Root;
import lombok.Getter;

@Getter
public class Metric<T extends Fact> {
  private final String field;
  private final MetricType type;
  private final BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>> metricSelector;
  private final BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>> dealProductMetricSelector;

  public Metric(String field, MetricType type,
      BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>> metricSelector,
      BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>> dealProductMetricSelector) {
    this.field = field;
    this.type = type;
    this.metricSelector = metricSelector;
    this.dealProductMetricSelector = dealProductMetricSelector;
  }
}
