package com.sling.sales.report.core.domain.call;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.api.response.AggregateRecord;
import com.sling.sales.report.core.api.response.GoalAggregateResponse;
import com.sling.sales.report.core.domain.aggregation.dimension.GoalDetail;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.fact.FactAccessSpecifications;
import com.sling.sales.report.core.domain.aggregation.fact.FactAggregationFacade;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import com.sling.sales.report.core.domain.goal.GoalFieldValue;
import com.sling.sales.report.security.domain.User;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CallAggregationFacade {

  private final FactAggregationFacade<Call> factAggregationFacade;

  @Autowired
  public CallAggregationFacade(FactAggregationFacade<Call> factAggregationFacade) {
    this.factAggregationFacade = factAggregationFacade;
  }

  private static final FactAccessSpecifications<Call> accessSpecifications = new FactAccessSpecifications<>(
      root -> root.get(Call_.tenantId),
      root -> root.get(Call_.id),
      root -> root.get(Call_.owner),
      root -> root.get(Call_.deleted),
      null);

  public List<AggregateRecord> getAggregatedResults(Map<String, GroupByDimension<Call>> customGroupByDimensions, List<FactFilter<Call>> factFilters,
      List<GroupByField> groupBy, List<Metric> metrics,
      List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<Call>> customFieldMetrics,
      String timezone, Long currencyId) {
    return factAggregationFacade
        .getAggregatedResults(customGroupByDimensions, factFilters, groupBy, metrics, customFieldMetrics, Call.class, accessSpecifications,
            timezone, currencyId);
  }

  public List<AggregateRecord> getAggregationResult(List<FactFilter<Call>> callFactFilters, List<GroupByDimension<Call>> groupByDimensions,
      List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<Call>> customFieldMetrics, String timezone,
      Long currencyId) {
    return factAggregationFacade
        .getAggregationResult(callFactFilters, groupByDimensions, metrics, customFieldMetrics, accessSpecifications, Call.class, timezone,
            currencyId);
  }

  public GoalAggregateResponse getGoalAggregateRecords(Map<String, GroupByDimension<Call>> groupByDimensions, List<FactFilter<Call>> factFilters,
      List<GroupByField> groupBy,
      List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<Call>> customFieldMetrics, String timezone,
      GoalDetail goalDetail, User loggedInUser, Set<GoalFieldValue> goalFieldValues, Long currencyId) {
    return factAggregationFacade
        .getGoalAggregateRecords(groupByDimensions, factFilters, groupBy, metrics, customFieldMetrics, Call.class, accessSpecifications, timezone,
            goalDetail, loggedInUser, goalFieldValues, currencyId);
  }
}
