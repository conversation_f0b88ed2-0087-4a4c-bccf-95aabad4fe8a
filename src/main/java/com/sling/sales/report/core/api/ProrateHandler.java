package com.sling.sales.report.core.api;

import static com.sling.sales.report.core.domain.aggregation.dimension.DateTimeParser.parseDate;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.response.GoalAggregateRecord;
import com.sling.sales.report.core.api.response.GoalAggregateResponse;
import com.sling.sales.report.core.api.response.GoalCount;
import com.sling.sales.report.core.api.response.GoalCountSummary;
import com.sling.sales.report.core.domain.aggregation.dimension.DateOperator;
import com.sling.sales.report.core.domain.aggregation.dimension.GoalDetail;
import com.sling.sales.report.core.domain.aggregation.dimension.Operator;
import com.sling.sales.report.core.domain.aggregation.dimension.RollingDateFilter;
import com.sling.sales.report.core.domain.aggregation.dimension.RollingDateFilter.RollingDate;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

@Service
public class ProrateHandler {

  public GoalAggregateResponse prorate(GoalAggregateResponse goalAggregateResponse, GoalDetail goalDetail,
      RollingDateFilter rollingDateFilter) {
    Operator frequencyOperator = goalDetail.getFrequencyType().getOperator();
    String dateRangeOperator = goalDetail.getDateRange().getOperator();
    DateOperator frequencyDateOperator = DateOperator.valueOf(frequencyOperator.name());
    DateOperator reportDateOperator = DateOperator.valueOf(dateRangeOperator);
    if (reportDateOperator == DateOperator.between) {
      return prorateGoalReportData(goalAggregateResponse, goalDetail, frequencyDateOperator,rollingDateFilter);
    }
    if (reportDateOperator.isAllowedToProrate()) {
      long frequencyOperatorNumberOfDays = relativeFilterNumberOfDays(goalDetail, frequencyDateOperator, rollingDateFilter);
      long reportOperatorNumberOfDays = relativeFilterNumberOfDays(goalDetail, reportDateOperator, rollingDateFilter);
      return prorateGoalReportData(goalAggregateResponse, frequencyOperatorNumberOfDays, reportOperatorNumberOfDays);
    }
    return goalAggregateResponse;
  }

  private GoalAggregateResponse prorateGoalReportData(GoalAggregateResponse goalAggregateResponse, GoalDetail goalDetail,
      DateOperator frequencyDateOperator, RollingDateFilter rollingDateFilter) {
    var dateRange = goalDetail.getDateRange();
    long frequencyOperatorNumberOfDays = relativeFilterNumberOfDays(goalDetail, frequencyDateOperator, rollingDateFilter);
    var stringArray = (ArrayList) dateRange.getValue();
    var start = parseDate(stringArray.get(0));
    var end = parseDate(stringArray.get(1));
    long reportOperatorNumberOfDays = TimeUnit.DAYS.convert(end.getTime() - start.getTime(), TimeUnit.MILLISECONDS) + 1;
    return prorateGoalReportData(goalAggregateResponse, frequencyOperatorNumberOfDays, reportOperatorNumberOfDays);
  }

  private GoalCountSummary prorateTotalGoal(GoalCountSummary goalSummary, long frequencyInDays,
      long dateRangeInDays, long size) {
    double proratedTotalGoalValue = (goalSummary.getGoal().doubleValue() / frequencyInDays) * dateRangeInDays;
    double percentage = proratedTotalGoalValue > 0.0 ? (goalSummary.getAchieved().doubleValue() / proratedTotalGoalValue) * 100 : 0.0;
    double averageGoal = size > 0 ? proratedTotalGoalValue / size : 0.0;
    return new GoalCountSummary(goalSummary.getAchieved(), proratedTotalGoalValue, percentage, goalSummary.getAverageOfAchieved(), averageGoal);
  }

  private long relativeFilterNumberOfDays(GoalDetail goalDetail, DateOperator dateOperator,
      RollingDateFilter rollingDateFilter) {
    Filter dateRange = goalDetail.getDateRange();
    RollingDate rollingDate = rollingDateFilter.from(Operator.valueOf(dateOperator.name()), Instant.now(),
        ZoneId.of(goalDetail.getTimeZone()), dateRange.getFrom(), dateRange.getTo());

    long numberOfDays = TimeUnit.DAYS.convert(rollingDate.getEndDate().getTime() - rollingDate.getStartDate().getTime(),
        TimeUnit.MILLISECONDS);
    return numberOfDays + 1;
  }

  private GoalAggregateResponse prorateGoalReportData(GoalAggregateResponse goalAggregateResponse, long frequencyInDays,
      long dateRangeInDays) {
    if (frequencyInDays < dateRangeInDays) {
      return goalAggregateResponse;
    }
    List<GoalAggregateRecord> proratedGoalAggregateRecords = goalAggregateResponse
        .getResult()
        .stream()
        .map(goalAggregateRecord -> prorateGoalAggregateRecord(frequencyInDays, dateRangeInDays, goalAggregateRecord))
        .collect(Collectors.toList());
    GoalCountSummary proratedGoalCountSummary = prorateTotalGoal(goalAggregateResponse.getGoalSummary(), frequencyInDays, dateRangeInDays,
        proratedGoalAggregateRecords.size());
    return new GoalAggregateResponse(proratedGoalAggregateRecords, proratedGoalCountSummary);
  }

  private GoalAggregateRecord prorateGoalAggregateRecord(long frequencyInDays, long dateRangeInDays,
      GoalAggregateRecord goalAggregateRecord) {
    GoalCount goalCount = prorateGoalCount(goalAggregateRecord.getValue().get(0), frequencyInDays,
        dateRangeInDays);
    return new GoalAggregateRecord(goalAggregateRecord.getId(), goalAggregateRecord.getName(), List.of(goalCount));
  }

  private GoalCount prorateGoalCount(GoalCount goalCount, long frequencyInDays, long dateRangeInDays) {
    double proratedGoalValue = (goalCount.getGoal().doubleValue() / frequencyInDays) * dateRangeInDays;
    double percentage = proratedGoalValue > 0.0 ? (goalCount.getAchieved().doubleValue() / proratedGoalValue) * 100 : 0.0;
    return new GoalCount(goalCount.getAchieved(), proratedGoalValue, percentage);
  }
}
