package com.sling.sales.report.core.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.domain.aggregation.dimension.Aggregate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class AggregateRecord {

  private final Long id;
  private final String name;
  private List<Number> values;
  private RecordDetails recordDetails;
  @JsonIgnore
  private Map<Aggregate, AggregateRecord> aggregateRecordMap = new LinkedHashMap<>();
  @JsonIgnore
  private Date orderByDate;
  private List<AggregateRecord> dimensions = new LinkedList<>();

  @JsonCreator
  public AggregateRecord(@JsonProperty("id") Long id, @JsonProperty("name") String name, @JsonProperty("values") List<Number> values) {
    this.id = id;
    this.name = name;
    this.values = getValues(values);
  }

  public AggregateRecord(Long id, String name) {
    this.id = id;
    this.name = name;
  }

  private List<Number> getValues(List<Number> values) {
    return values.stream()
        .map(value -> {
          if (value instanceof Double) {
            return BigDecimal.valueOf((Double) value)
                .setScale(4, RoundingMode.HALF_EVEN)
                .doubleValue();
          }
          return value;
        }).collect(Collectors.toList());
  }

  public void updateValues(List<Number> prevRecordValues, List<Number> nextRecordValues) {
    if (prevRecordValues.isEmpty()) {
      this.values = getValues(nextRecordValues);
      return;
    }
    List<Number> updatedValues = IntStream.range(0, prevRecordValues.size())
        .boxed()
        .map(index -> prevRecordValues.get(index).doubleValue() + nextRecordValues.get(index).doubleValue())
        .collect(Collectors.toList());
    this.values = getValues(updatedValues);
  }

  public void withValues(List<Number> values) {
    this.values = values;
  }

  public void updateValues(List<Number> values) {
    this.values = getValues(values);
  }

  public AggregateRecord withRecordDetails(RecordDetails recordDetails) {
    this.recordDetails = recordDetails;
    return this;
  }

  public void addDimension(AggregateRecord aggregateRecord, boolean isTimeBasedDimensionPresent) {
    this.dimensions.add(aggregateRecord);
    if (!this.dimensions.isEmpty() && isTimeBasedDimensionPresent) {
      this.dimensions.sort(this::orderByDate);
    }
  }

  public void addAndSortDimensions(AggregateRecord aggregateRecord, boolean isTimeBasedDimensionPresent) {
    this.dimensions.add(aggregateRecord);
    if (!this.dimensions.isEmpty() && isTimeBasedDimensionPresent) {
      this.dimensions.sort(this::orderByDate);
      return;
    }
    if (!this.dimensions.isEmpty()) {
      this.dimensions.sort(this::compare);
    }
  }

  public void addDimensionToMap(Aggregate aggregate, AggregateRecord aggregateRecord) {
    this.aggregateRecordMap.put(aggregate, aggregateRecord);
  }

  public List<AggregateRecord> getDimensions() {
    return Collections.unmodifiableList(this.dimensions);
  }

  private int compare(AggregateRecord aggregateRecordOne, AggregateRecord aggregateRecordTwo) {
    double aggregateRecordOneTotal = getMaxValue(aggregateRecordOne).doubleValue();
    double aggregateRecordTwoTotal = getMaxValue(aggregateRecordTwo).doubleValue();

    return Double.compare(aggregateRecordOneTotal, aggregateRecordTwoTotal);
  }

  private int orderByDate(AggregateRecord aggregateRecordOne, AggregateRecord aggregateRecordTwo) {
    if (ObjectUtils.isEmpty(aggregateRecordOne.getOrderByDate()) || ObjectUtils.isEmpty(aggregateRecordTwo.getOrderByDate())) {
      return 0;
    }
    return aggregateRecordOne.getOrderByDate().compareTo(aggregateRecordTwo.getOrderByDate());
  }

  private Number getMaxValue(AggregateRecord aggregateRecordOne) {
    return aggregateRecordOne.getValues()
        .stream()
        .max(Comparator.comparing(Number::doubleValue))
        .orElse(0.0);
  }

  public AggregateRecord withOrderByDate(Date orderByDate) {
    this.orderByDate = orderByDate;
    return this;
  }

  public AggregateRecord getAggregateRecordById(Aggregate aggregate) {
    return this.aggregateRecordMap.get(aggregate);
  }

  public boolean containsKey(Aggregate aggregate) {
    return this.aggregateRecordMap.containsKey(aggregate);
  }
}
