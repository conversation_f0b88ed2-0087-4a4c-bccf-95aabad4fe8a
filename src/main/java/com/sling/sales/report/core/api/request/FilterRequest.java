package com.sling.sales.report.core.api.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class FilterRequest {

  private final ArrayList<Filter> jsonRules = new ArrayList<>();

  @JsonCreator
  public FilterRequest(@JsonProperty("jsonRules") List<Filter> jsonRules) {
    if (jsonRules != null) {
      this.jsonRules.addAll(jsonRules);
    }
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Filter {

    private final String operator;
    private final String field;
    private final String type;
    private final Object value;

    @JsonCreator
    public Filter(
        @JsonProperty("operator") String operator,
        @JsonProperty("field") String field,
        @JsonProperty("type") String type,
        @JsonProperty("value") Object value) {
      this.operator = operator;
      this.field = field;
      this.type = type;
      this.value = value;
    }
  }
}
