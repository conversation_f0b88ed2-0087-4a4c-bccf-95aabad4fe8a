package com.sling.sales.report.core.api;

import static com.sling.sales.report.core.domain.aggregation.dimension.MetricType.AVERAGE;
import static com.sling.sales.report.core.domain.aggregation.dimension.MetricType.COUNT;

import com.sling.sales.report.config.domain.Field;
import com.sling.sales.report.config.domain.ForecastingType;
import com.sling.sales.report.config.domain.service.EntityService;
import com.sling.sales.report.core.api.request.AggregationRequestV3;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.api.response.AggregateRecord;
import com.sling.sales.report.core.api.response.GoalAggregateRecord;
import com.sling.sales.report.core.api.response.GoalAggregateResponse;
import com.sling.sales.report.core.api.response.GoalCountSummary;
import com.sling.sales.report.core.api.response.RecordDetails;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.Operator;
import com.sling.sales.report.core.domain.aggregation.dimension.RollingDateFilter;
import com.sling.sales.report.core.domain.aggregation.dimension.RollingDateFilter.RollingDate;
import com.sling.sales.report.core.domain.aggregation.exception.DownloadReportException;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.core.domain.report.Report;
import com.sling.sales.report.dto.DownloadDetail;
import com.sling.sales.report.dto.EntityLabelDto;
import com.sling.sales.report.dto.Row;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple4;
import reactor.util.function.Tuple7;
import reactor.util.function.Tuples;

@Service
@Slf4j
public class DownloadReportService {

  private final EntityService entityService;
  private static final List<String> FORECASTING_TYPES = ForecastingType.toStringValues();

  @Autowired
  public DownloadReportService(EntityService entityService) {
    this.entityService = entityService;
  }


  public <T extends Fact> Resource generateCsvFile(List<AggregateRecord> aggregateRecords, Report report,
      AggregationRequestV3 aggregationRequest, Class<T> clazz, String authenticationToken, EntityType entityType, String currency,
      String timezone) {
    Map<EntityType, EntityLabelDto> entityLabels = entityService.getEntitiesLabel(authenticationToken);
    List<Field> fields = entityService.getFiltersByEntity1(clazz, authenticationToken);
    Map<String, Field> fieldsToMap = fieldToMap(fields);

    DownloadDetail downloadDetail = getDownloadDetail(aggregateRecords, aggregationRequest, entityType, currency, entityLabels,
        fieldsToMap);
    return getByteArrayResource(downloadDetail, report, aggregationRequest, entityLabels,
        fieldsToMap, entityType, timezone, currency);
  }

  private ByteArrayResource getByteArrayResource(DownloadDetail tuple, Report report, AggregationRequestV3 aggregationRequest,
      Map<EntityType, EntityLabelDto> entityLabels,
      Map<String, Field> fieldsToMap, EntityType entityType, String timezone, String currency) {
    try {
      List<String> headers = tuple.getHeaders();
      List<String> emptyStrings = Collections.nCopies(headers.size() - 1, "");
      ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
      PrintWriter printWriter = new PrintWriter(byteArrayOutputStream);
      CSVPrinter csvPrinter = new CSVPrinter(printWriter, CSVFormat.DEFAULT);
      String description = report.getDescription() == null ? "-" : report.getDescription();
      csvPrinter.println();
      csvPrinter.print("Report Name:- " + report.getName());
      csvPrinter.printRecord(emptyStrings);
      csvPrinter.print("Description:- " + description);
      csvPrinter.printRecord(emptyStrings);
      csvPrinter.print("Date Range:- " + buildDateRangeHeaders(aggregationRequest.getDateRange(), fieldsToMap, timezone, true));
      csvPrinter.printRecord(emptyStrings);
      csvPrinter.print("Dimensions:- " + buildDimensionsHeaders(aggregationRequest.getGroupBy(), fieldsToMap));
      csvPrinter.printRecord(emptyStrings);
      csvPrinter.print("Metrics:- " + buildMetricsHeaders(aggregationRequest.getMetrics(), entityType, entityLabels, fieldsToMap, currency));
      csvPrinter.printRecord(emptyStrings);
      csvPrinter.print(
          "Filters:- " + buildFiltersHeaders(aggregationRequest.getFilters(), fieldsToMap, timezone));
      csvPrinter.printRecord(emptyStrings);
      csvPrinter.println();
      csvPrinter.printRecord(headers);
      for (int i = 0; i < tuple.getRows().size(); i++) {
        csvPrinter.printRecord(tuple.getRows().get(i).getValues());
      }
      csvPrinter.close();
      printWriter.close();
      byteArrayOutputStream.close();
      return new ByteArrayResource(byteArrayOutputStream.toByteArray());
    } catch (IOException e) {
      log.error("error in generating a csv file", e);
      throw new DownloadReportException();
    }
  }

  private String buildFiltersHeaders(ArrayList<Filter> filters, Map<String, Field> fieldsToMap, String timezone) {
    if (ObjectUtils.isEmpty(filters)) {
      return "No filters Applied";
    }
    return filters
        .stream()
        .map(filter -> buildFilterHeader(filter, fieldsToMap, timezone))
        .collect(Collectors.joining(","));
  }

  private String buildFilterHeader(Filter filter, Map<String, Field> fieldsToMap, String timezone) {
    Field field = fieldsToMap.getOrDefault(filter.getFieldName(), null);
    String fieldDisplayName = field == null ? filter.getFieldName() : field.getHeader();
    if ("teams".equals(filter.getProperty())) {
      fieldDisplayName = fieldDisplayName.concat("(Teams)");
    }
    if (filter.getFieldType().equals("date")) {
      return String.join(" ", fieldDisplayName, "( " + buildDateRangeHeaders(filter, fieldsToMap, timezone, false) + " )");
    }
    String operatorDisplayName = Operator.valueOf(filter.getOperator()).getDisplayName();
    if (ObjectUtils.isEmpty(filter.getValue())) {
      String result = "( " + operatorDisplayName + " )";
      return String.join(" ", fieldDisplayName, result);
    }
    if (filter.getValue() instanceof List) {
      List<Object> values = (List<Object>) filter.getValue();
      return values.isEmpty() ? "" : buildFilterHeaderByTypeForList(values, fieldDisplayName, operatorDisplayName);
    }
    if (filter.getValue() instanceof Boolean) {
      return getBooleanAsString(filter, fieldDisplayName, operatorDisplayName);
    }
    String result = "( " + operatorDisplayName + " - " + filter.getValue() + ")";
    return String.join(" ", fieldDisplayName, result);
  }

  private String getBooleanAsString(Filter filter, String fieldDisplayName, String operatorDisplayName) {
    String value = String.valueOf(filter.getValue());
    String filterValue = value.equals("true") ? "Yes" : "No";
    String result = "( " + operatorDisplayName + " - " + filterValue + ")";
    return String.join(" ", fieldDisplayName, result);
  }

  private String buildFilterHeaderByTypeForList(List<Object> values, String fieldDisplayName, String operatorDisplayName) {
    String filterHeader = values
        .stream()
        .map(String::valueOf)
        .collect(Collectors.joining(","));
    String result = "( " + operatorDisplayName + " - " + filterHeader + ")";
    return String.join(" ", fieldDisplayName, result);
  }

  private String buildMetricsHeaders(List<Metric> metrics, EntityType entityType, Map<EntityType, EntityLabelDto> entityLabels,
      Map<String, Field> fieldsToMap, String currency) {
    return metrics.stream()
        .map(metric -> metric.getType() == COUNT ? getCountHeader(metric, entityType, fieldsToMap, entityLabels)
            : getSumOrAverageHeader(metric, fieldsToMap, entityType, currency))
        .collect(Collectors.joining(","));
  }

  private String buildDimensionsHeaders(List<GroupByField> groupBy, Map<String, Field> fieldMap) {
    List<String> groupByHeaders = groupBy
        .stream()
        .map(groupByField -> getGroupByFieldHeader(groupByField, fieldMap))
        .collect(Collectors.toList());
    return String.join(",", groupByHeaders);
  }

  private String getGroupByFieldHeader(GroupByField groupByField, Map<String, Field> fieldMap) {
    Field field = fieldMap.getOrDefault(groupByField.getName(), null);
    if (field == null) {
      return groupByField.getName();
    }
    String header = field.getHeader();
    if ("teams".equals(groupByField.getProperty())) {
      header = header + "(Teams)";
    }
    return header;
  }

  private String buildDateRangeHeaders(Filter dateRange, Map<String, Field> fieldMap, String timezone, boolean withFieldDisplayName) {
    try {
      Field field = fieldMap.getOrDefault(dateRange.getId(), null);
      String fieldDisplayName = getFieldDisplayName(dateRange, field);
      Operator operator = Operator.valueOf(dateRange.getOperator());
      String fromDate = "";
      String toDate = "";
      boolean isRollingDateFilter = operator.isRollingDateOperator();
      DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm a");
      DateFormat utcDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
      if (isRollingDateFilter) {
        RollingDate rollingDate = new RollingDateFilter().from(operator, Instant.now(), ZoneId.of(timezone), dateRange.getFrom(),
            dateRange.getTo());
        fromDate = getStringDate(dateFormat, rollingDate.getStartDate(), timezone);
        toDate = getStringDate(dateFormat, rollingDate.getEndDate(), timezone);
      }
      if (!isRollingDateFilter && !(dateRange.getValue() instanceof List)) {
        return withOrWithoutFieldDisplayName(dateRange, withFieldDisplayName, fieldDisplayName, operator, dateFormat);
      }
      if (!isRollingDateFilter) {
        List<String> range = (List<String>) dateRange.getValue();
        fromDate = dateFormat.format(getDate(utcDateFormat, range.get(0), timezone));
        toDate = dateFormat.format(getDate(utcDateFormat, range.get(1), timezone));
      }
      return withOrWithoutFieldName(withFieldDisplayName, fieldDisplayName, operator, fromDate, toDate);
    } catch (ParseException e) {
      log.error("error in parsing date while downloading report {} with error details {}", e.getMessage(), e);
    }
    return "Not Available";
  }

  private String getStringDate(DateFormat dateFormat, Date date, String timezone) {
    if (date == null) {
      return "";
    }
    Date dateWithTimezone = Date.from(
        date.toInstant().atZone(ZoneId.of(timezone)).toLocalDateTime().toInstant(ZoneOffset.UTC));
    return dateFormat.format(dateWithTimezone);
  }

  private Date getDate(DateFormat dateFormat, String date, String timezone) throws ParseException {
    Date parsedDate = dateFormat.parse(date);
    return Date.from(
        parsedDate.toInstant().atZone(ZoneId.of(timezone)).toLocalDateTime().toInstant(ZoneOffset.UTC));
  }

  private String withOrWithoutFieldName(boolean withFieldDisplayName, String fieldDisplayName, Operator operator, String fromDate, String toDate) {
    if (withFieldDisplayName) {
      return getByFromDateAndToDate(fieldDisplayName, operator, fromDate, toDate);
    }
    return String.join(" - ", operator.getDisplayName(), fromDate, toDate);
  }

  private String getByFromDateAndToDate(String fieldDisplayName, Operator operator, String fromDate, String toDate) {
    if ((ObjectUtils.isEmpty(fromDate) || StringUtils.isBlank(fromDate)) && ObjectUtils.isNotEmpty(toDate)) {
      return String.join(" - ", fieldDisplayName, operator.getDisplayName(), toDate);
    }
    if ((ObjectUtils.isEmpty(toDate) || StringUtils.isBlank(toDate)) && ObjectUtils.isNotEmpty(fromDate)) {
      return String.join(" - ", fieldDisplayName, operator.getDisplayName(), fromDate);
    }
    return String.join(" - ", fieldDisplayName, operator.getDisplayName(), fromDate, toDate);
  }

  private String getFieldDisplayName(Filter dateRange, Field field) {
    if (field == null) {
      return dateRange.getFieldName();
    }
    return field.getHeader();
  }

  private String withOrWithoutFieldDisplayName(Filter dateRange, boolean withFieldDisplayName, String fieldDisplayName, Operator operator,
      DateFormat dateFormat)
      throws ParseException {
    if (withFieldDisplayName) {
      return String.join(" - ", fieldDisplayName, operator.getDisplayName(),
          dateFormat.format(dateFormat.parse(dateRange.getValue().toString())));
    }
    return String.join(" - ", operator.getDisplayName(), dateFormat.format(dateFormat.parse(dateRange.getValue().toString())));
  }


  private List<String> getMetricHeaders(
      Tuple7<List<GroupByField>, Map<String, Field>, List<Metric>, EntityType, String, List<Double>, Map<EntityType, EntityLabelDto>> tuple7) {

    return tuple7.getT3().stream()
        .map(metric -> metric.getType() == COUNT ? getCountHeader(metric, tuple7.getT4(), tuple7.getT2(), tuple7.getT7())
            : getSumOrAverageHeader(metric, tuple7.getT2(), tuple7.getT4(), tuple7.getT5()))
        .collect(Collectors.toList());
  }

  private String getSumOrAverageHeader(Metric metric, Map<String, Field> fieldMap, EntityType entityType, String currency) {
    String metricCurrency = entityType == EntityType.CALL || entityType == EntityType.TASK ? "" : "(In " + currency + ")";
    return metric.getType().getDisplayName()
        + fieldMap.get(metric.getField()).getHeader()
        + metricCurrency;
  }

  private String getCountHeader(Metric metric, EntityType entityType, Map<String, Field> fieldMap, Map<EntityType, EntityLabelDto> entityLabels) {
    if (metric.getField().equals("id") && entityLabels.containsKey(entityType)) {
      return COUNT.getDisplayName() + entityLabels.get(entityType).getDisplayNamePlural();
    }
    if (!metric.getField().equals("id") && fieldMap.containsKey(metric.getField())) {
      return COUNT.getDisplayName() + fieldMap.get(metric.getField()).getHeader();
    }
    if (metric.getField().equals("numberOfProducts") && entityType == EntityType.DEAL) {
      return "Number of Products";
    }
    return COUNT.getDisplayName() + entityType.getEntityPlural();
  }

  private DownloadDetail getDownloadDetail(List<AggregateRecord> aggregateRecords,
      AggregationRequestV3 aggregationRequest, EntityType entityType, String currency, Map<EntityType, EntityLabelDto> entityLabels,
      Map<String, Field> fieldsToMap) {
    List<GroupByField> groupBy = aggregationRequest.getGroupBy();
    List<Metric> metrics = aggregationRequest.getMetrics();
    List<Double> totals = calculateTotal(aggregateRecords, metrics.size());
    var tuple7 = Tuples.of(groupBy, fieldsToMap, metrics, entityType, currency, totals, entityLabels);
    List<String> headers = buildHeaders(tuple7);
    List<Row> rows = buildRows(aggregateRecords, totals, metrics, groupBy);

    return new DownloadDetail(headers, rows);
  }

  private Mono<Map<String, Field>> fieldToMap(Mono<List<Field>> fields) {
    return fields
        .flatMap(allFields -> {
          Map<String, Field> fieldsMap = allFields.stream()
              .collect(Collectors.toMap(Field::getId, field -> field));
          return Mono.just(fieldsMap);
        });
  }

  private Map<String, Field> fieldToMap(List<Field> fields) {
    return fields
        .stream()
        .collect(Collectors.toMap(Field::getId, field -> field));
  }

  private List<String> buildHeaders(
      Tuple7<List<GroupByField>, Map<String, Field>, List<Metric>, EntityType, String, List<Double>, Map<EntityType, EntityLabelDto>> tuple7) {
    List<String> headers = tuple7.getT1()
        .stream()
        .map(groupByField -> {
          String fieldDisplayName = tuple7.getT2().get(groupByField.getName()).getHeader();
          if ("teams".equals(groupByField.getProperty())) {
            fieldDisplayName = fieldDisplayName + "(Teams)";
          }
          return fieldDisplayName;
        })
        .collect(Collectors.toList());
    headers.addAll(getMetricHeaders(tuple7));
    return headers;
  }

  private List<Row> buildRows(List<AggregateRecord> aggregateRecords, List<Double> totals, List<Metric> metrics, List<GroupByField> groupByFields) {
    if (aggregateRecords == null || aggregateRecords.isEmpty()) {
      return Collections.emptyList();
    }
    List<Row> rows = new ArrayList<>(
        aggregateRecordsToRows(aggregateRecords, new ArrayList<>(), new ArrayList<>(), totals, metrics, groupByFields, 0));

    if (!rows.isEmpty()) {
      List<String> values = buildLastRow(totals, rows, metrics);
      rows.add(new Row(values));
    }
    return rows;
  }

  public List<Row> aggregateRecordsToRows(List<AggregateRecord> aggregateRecords, List<String> values, List<Row> rows, List<Double> totals,
      List<Metric> metrics, List<GroupByField> groupByFields, int groupByLevel) {
    if (aggregateRecords.get(0).getDimensions().isEmpty()) {
      return extractStringValues(values, aggregateRecords, totals, metrics, groupByFields, groupByLevel);
    }
    aggregateRecords.forEach(aggregateRecord -> {
      List<Row> rowList = new ArrayList<>();
      String name = StringUtils.isEmpty(aggregateRecord.getName()) ? "Not Available"
          : getAggregateRecordFormattedName(aggregateRecord, groupByFields.get(groupByLevel));
      values.add(name);
      rows.addAll(aggregateRecordsToRows(aggregateRecord.getDimensions(), values, rowList, totals, metrics, groupByFields, groupByLevel + 1));
      values.remove(values.size() - 1);
    });
    return rows;
  }

  private List<String> buildLastRow(List<Double> totals, List<Row> rows, List<Metric> metrics) {

    long rowsSize = rows.size();
    metrics
        .stream()
        .filter(metric -> metric.getType() == AVERAGE)
        .map(metrics::indexOf)
        .forEach(index -> totals.set(index, totals.get(index) / rowsSize));

    List<String> values = new ArrayList<>();
    values.add("Total");
    int size = rows.get(0).getValues().size() - (metrics.size() + 1);
    IntStream.range(0, size)
        .forEach(index -> values.add(" "));
    IntStream.range(0, metrics.size())
        .forEach(index -> {
          String formattedValue =
              getFormattedValueAsString(totals.get(index), metrics.get(index).getType()) + "(" + calculatePercentageAndReturn(totals.get(index),
                  totals.get(index)) + ")";
          values.add(formattedValue);
        });
    return values;
  }

  private String getFormattedValueAsString(Number value, MetricType metricType) {
    NumberFormat numberFormat = NumberFormat.getInstance();
    DecimalFormat decimalFormat = new DecimalFormat("#,##0.0000");
    decimalFormat.setRoundingMode(RoundingMode.HALF_UP);
    if (metricType == COUNT) {
      return numberFormat.format(value);
    }
    return decimalFormat.format(value);
  }

  private List<Row> extractStringValues(List<String> valuesList, List<AggregateRecord> aggregateRecords, List<Double> totals, List<Metric> metrics,
      List<GroupByField> groupByFields, int groupByLevel) {
    return aggregateRecords
        .stream()
        .map(aggregateRecord -> {
          List<Number> metricValues = aggregateRecord.getValues();
          List<String> values = new ArrayList<>(valuesList);
          String name = StringUtils.isEmpty(aggregateRecord.getName()) ? "Not Available" :
              getAggregateRecordFormattedName(aggregateRecord, groupByFields.get(groupByLevel));
          values.add(name);
          IntStream.range(0, metrics.size())
              .forEach(index -> {
                String formattedValue =
                    getFormattedValueAsString(metricValues.get(index), metrics.get(index).getType()) + "(" + calculatePercentageAndReturn(
                        metricValues.get(index), totals.get(index)) + ")";
                values.add(formattedValue);
              });
          return new Row(values);
        }).collect(Collectors.toList());
  }

  private List<Double> calculateTotal(List<AggregateRecord> aggregateRecords, int metricsSize) {
    List<Double> totals = new ArrayList<>(Collections.nCopies(metricsSize, 0.0));
    aggregateRecords
        .forEach(aggregateRecord -> IntStream.range(0, metricsSize)
            .forEach(index -> {
              double value = aggregateRecord.getValues().get(index).doubleValue();
              if (!totals.isEmpty()) {
                value = value + totals.get(index);
              }
              totals.set(index, value);
            }));
    return totals;
  }

  private String calculatePercentageAndReturn(Number value, Double total) {
    DecimalFormat decimalFormat = new DecimalFormat("0.00");
    double percentage = (value.doubleValue() / total) * 100;
    return Double.isNaN(percentage) ? "0.00%" : decimalFormat.format(percentage) + "%";
  }

  private String getAggregateRecordFormattedName(AggregateRecord aggregateRecord, GroupByField groupByField) {
    List<String> fieldNames = List.of("sentBy", "receivedBy", "participants");
    String name = aggregateRecord.getName();
    RecordDetails recordDetails = aggregateRecord.getRecordDetails();

    if (FORECASTING_TYPES.contains(name)) {
      return ForecastingType.valueOf(name).getDisplayName();
    }
    if (fieldNames.contains(groupByField.getName()) && ObjectUtils.isNotEmpty(recordDetails)) {
      StringBuilder sb = new StringBuilder();
      if (ObjectUtils.isNotEmpty(recordDetails.getEntity())) {
        sb.append(StringUtils.capitalize(recordDetails.getEntity())).append(" - ");
      }
      sb.append(name);
      if (ObjectUtils.isNotEmpty(recordDetails.getEmail())) {
        sb.append(" (").append(recordDetails.getEmail()).append(")");
      }
      return sb.toString();
    }
    return name;
  }
  public <T extends Fact> Resource generateCsvFileForGoalReport(Report report, GoalAggregateResponse goalAggregateResponse,
      AggregationRequestV3 aggregationRequest,
      Class<T> clazz, String authenticationToken, EntityType entityType, String currency, String timezone) {
    Map<EntityType, EntityLabelDto> entityLabels = entityService.getEntitiesLabel(authenticationToken);
    List<Field> fields = entityService.getFiltersByEntity1(clazz, authenticationToken);
    Map<String, Field> fieldsToMap = fieldToMap(fields);

    DownloadDetail downloadDetail = getDownloadDetail(goalAggregateResponse, aggregationRequest, entityType, currency, fieldsToMap,
        entityLabels);
    return getByteArrayResource(downloadDetail, report, aggregationRequest, entityLabels, fieldsToMap, entityType, timezone, currency);
  }

  private DownloadDetail getDownloadDetail(GoalAggregateResponse goalAggregateResponse, AggregationRequestV3 aggregationRequest,
      EntityType entityType, String currency, Map<String, Field> fieldsToMap, Map<EntityType, EntityLabelDto> entityLabels) {
    List<GroupByField> groupBy = aggregationRequest.getGroupBy();
    List<Metric> metrics = aggregationRequest.getMetrics();
    List<String> headers = buildGoalReportHeaders(groupBy, fieldsToMap, entityLabels, metrics, entityType, currency);
    List<Row> rows = buildGoalReportRows(goalAggregateResponse);

    return new DownloadDetail(headers, rows);
  }

  private List<String> buildGoalReportHeaders(List<GroupByField> groupBy, Map<String, Field> fieldsToMap,
      Map<EntityType, EntityLabelDto> entityLabels,
      List<Metric> metrics, EntityType entityType, String currency) {

    List<Double> totals = new ArrayList<>();
    Tuple4 tuple4 = Tuples.of(fieldsToMap, entityLabels, currency, totals);
    List<String> headers = groupBy
        .stream()
        .map(groupByField -> fieldsToMap.get(groupByField.getName()).getHeader())
        .collect(Collectors.toList());
    headers.addAll(getMetricHeadersForGoalReport(metrics, entityType, tuple4));
    return headers;
  }

  private List<String> getMetricHeadersForGoalReport(List<Metric> metrics, EntityType entityType,
      Tuple4<Map<String, Field>, Map<EntityType, EntityLabelDto>, String, List<Double>> tuple) {
    List<String> headers = new LinkedList<>();
    metrics.stream()
        .map(metric -> metric.getType() == COUNT ? getCountHeadersForGoalReport(metric, entityType, tuple)
            : getSumOrAverageHeadersForGoalReport(metric, tuple, entityType))
        .forEach(headers::addAll);
    return headers;
  }

  private List<String> getSumOrAverageHeadersForGoalReport(Metric metric,
      Tuple4<Map<String, Field>, Map<EntityType, EntityLabelDto>, String, List<Double>> tuple,
      EntityType entityType) {
    String currency = entityType == EntityType.CALL || entityType == EntityType.TASK ? "" : "(In " + tuple.getT3() + ")";
    String displayName = metric.getType().getDisplayName()
        + tuple.getT1().get(metric.getField()).getHeader()
        + currency;
    return getGoalHeaders(displayName);
  }

  private List<String> getCountHeadersForGoalReport(Metric metric, EntityType entityType,
      Tuple4<Map<String, Field>, Map<EntityType, EntityLabelDto>, String, List<Double>> tuple) {
    if (metric.getField().equals("id") && tuple.getT2().containsKey(entityType)) {
      String displayName = COUNT.getDisplayName() + tuple.getT2().get(entityType).getDisplayNamePlural();
      return getGoalHeaders(displayName);
    }
    if (!metric.getField().equals("id") && tuple.getT1().containsKey(metric.getField())) {
      String displayName = COUNT.getDisplayName() + tuple.getT1().get(metric.getField()).getHeader();
      return getGoalHeaders(displayName);
    }
    if (metric.getField().equals("numberOfProducts") && entityType == EntityType.DEAL) {
      return getGoalHeaders("Number of Products");
    }
    String displayName = COUNT.getDisplayName() + entityType.getEntityPlural();
    return getGoalHeaders(displayName);
  }

  private List<String> getGoalHeaders(String displayName) {
    String achievementHeader = "Achievement (" + displayName + ")";
    String goalHeader = "Goal (" + displayName + ")";
    List<String> headers = new LinkedList<>() {{
      add(achievementHeader);
      add(goalHeader);
    }};
    return headers;
  }

  private List<Row> buildGoalReportRows(GoalAggregateResponse goalAggregateResponse) {
    if (goalAggregateResponse.getResult() == null || goalAggregateResponse.getResult().isEmpty()) {
      return Collections.emptyList();
    }
    List<Row> rows = new ArrayList<>(goalAggregateRecordsToRows(goalAggregateResponse.getResult()));

    if (!rows.isEmpty() || ObjectUtils.isEmpty(goalAggregateResponse.getGoalSummary())) {
      List<String> values = buildGoalReportLastRow(goalAggregateResponse.getGoalSummary());
      rows.add(new Row(values));
    }
    return rows;
  }

  private List<Row> goalAggregateRecordsToRows(List<GoalAggregateRecord> goalAggregateRecords) {
    return goalAggregateRecords
        .stream()
        .map(this::toGoalRow)
        .collect(Collectors.toList());
  }

  private Row toGoalRow(GoalAggregateRecord goalAggregateRecord) {
    List<String> values = new LinkedList<>();
    values.add(goalAggregateRecord.getName());
    goalAggregateRecord
        .getValue()
        .forEach(value -> {
          values.add(value.getAchieved() + "(" + value.getPercentage() + "%)");
          values.add(String.valueOf(value.getGoal()));
        });
    return new Row(values);
  }

  private List<String> buildGoalReportLastRow(GoalCountSummary goalCountSummary) {
    List<String> values = new LinkedList<>();
    values.add("Total");
    values.add("Total Achievement = " + goalCountSummary.getAchieved() + "(" + goalCountSummary.getPercentage() + "%)");
    values.add("Total Goal = " + goalCountSummary.getGoal());
    return values;
  }
}
