package com.sling.sales.report.core.domain.call;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
interface CallAssociatedToRepository extends JpaRepository<CallAssociatedTo, Long> {

  @Query(value = "UPDATE CallAssociatedTo SET ownerId = :ownerId WHERE entityId = :entityId AND entity = :entity")
  @Modifying(flushAutomatically = true)
  @Transactional
  void updateOwnerIdByEntityIdAndEntity(@Param("ownerId") long ownerId, @Param("entityId") long entityId, @Param("entity") String entity);

  @Transactional
  @Modifying
  @Query("UPDATE CallAssociatedTo SET entityName = :entityName WHERE entityId = :entityId AND entity = :entity")
  void updateEntityNameByEntityIdAndEntity(@Param("entityName") String name, @Param("entityId") long entityId, @Param("entity") String entity);
}
