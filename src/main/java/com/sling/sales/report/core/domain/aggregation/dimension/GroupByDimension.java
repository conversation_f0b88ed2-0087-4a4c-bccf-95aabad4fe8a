package com.sling.sales.report.core.domain.aggregation.dimension;

import com.sling.sales.report.core.api.request.Format;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.goal.GoalFieldValue;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Root;

public interface GroupByDimension<T extends Fact> extends Dimension<T> {

  List<Expression<?>> getGroupByPath(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, String timezone);

  Aggregate toResultKey(Tuple tuple, int columnNumber);

  GroupByDimension<T> withFormat(Format format);

  Format getFormat();

  default List<Number> toResultValues(Tuple tuple, int metricsSize) {
    int size = tuple.getElements().size();
    int startIndex = size - metricsSize;
    return IntStream.range(startIndex, size)
        .boxed()
        .map(index -> {
          Number value = tuple.get(index, Number.class);
          return value == null ? 0 : value;
        })
        .collect(Collectors.toList());
  }

  default GoalAggregate toGoalResultKey(Tuple tuple, int columnNumber, Map<Long, GoalFieldValue> goalFieldValuesByFieldValueId) {
    return null;
  }

  default GoalGroupByDimensionDetail getGoalGroupByDimensionDetail(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder,
      String timezone,
      GoalDetail goalDetail) {
    return null;
  }
}
