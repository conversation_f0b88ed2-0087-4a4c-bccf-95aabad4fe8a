package com.sling.sales.report.core.domain.aggregation.fact;

import com.sling.sales.report.core.domain.call.CallRelatedTo;
import com.sling.sales.report.core.domain.call.CallRelatedTo_;
import com.sling.sales.report.core.domain.call.Call_;
import com.sling.sales.report.core.domain.email.EmailRecipient;
import com.sling.sales.report.core.domain.email.EmailRecipient_;
import com.sling.sales.report.core.domain.email.EmailRelatedTo;
import com.sling.sales.report.core.domain.email.EmailRelatedTo_;
import com.sling.sales.report.core.domain.email.EmailSender;
import com.sling.sales.report.core.domain.email.EmailSender_;
import com.sling.sales.report.core.domain.email.Email_;
import com.sling.sales.report.core.domain.meeting.MeetingInvitee;
import com.sling.sales.report.core.domain.meeting.MeetingInvitee_;
import com.sling.sales.report.core.domain.meeting.MeetingRelatedTo;
import com.sling.sales.report.core.domain.meeting.MeetingRelatedTo_;
import com.sling.sales.report.core.domain.meeting.Meeting_;
import com.sling.sales.report.core.domain.meeting.Organizer;
import com.sling.sales.report.core.domain.meeting.Organizer_;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.User_;
import java.util.List;
import java.util.function.Function;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import javax.persistence.metamodel.SingularAttribute;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.jpa.domain.Specification;

public class FactAccessSpecifications<T extends Fact> {

  private final Function<Root<T>, Path<Long>> tenantIdPath;
  private final Function<Root<T>, Path<Long>> idPath;
  private final Function<Root<T>, Path<User>> ownedByPath;
  private final Function<Root<T>, Path<Boolean>> deletedPath;
  private final Function<Root<T>, Path<User>> taskAssignedToPath;

  public FactAccessSpecifications(
      Function<Root<T>, Path<Long>> tenantIdPath,
      Function<Root<T>, Path<Long>> idPath,
      Function<Root<T>, Path<User>> ownedByPath,
      Function<Root<T>, Path<Boolean>> deletedPath,
      Function<Root<T>, Path<User>> taskAssignedToPath) {
    this.tenantIdPath = tenantIdPath;
    this.ownedByPath = ownedByPath;
    this.idPath = idPath;
    this.deletedPath = deletedPath;
    this.taskAssignedToPath = taskAssignedToPath;
  }

  public Specification<T> belongsToTenant(Long tenantId) {
    return ((root, query, builder) -> builder.equal(tenantIdPath.apply(root), tenantId));
  }

  public Specification<T> ownedBy(long userId) {
    return (root, query, builder) -> builder.equal(ownedByPath.apply(root).get(User_.id), userId);
  }

  public Specification<T> ownedByWithTaskAssignedTo(long userId) {
    return (root, query, builder) -> builder.or(builder.equal(ownedByPath.apply(root).get(User_.id), userId),
        builder.equal(taskAssignedToPath.apply(root).get(User_.id), userId));
  }

  public Specification<T> haveIdsIn(List<Long> factIds) {
    return (root, query, builder) -> idPath.apply(root).in(factIds);
  }

  public Specification<T> haveFactOwnersIn(List<Long> factOwnerIds) {
    return (root, query, builder) -> ownedByPath.apply(root).in(factOwnerIds);
  }

  public Specification<T> isDeletedFalse() {
    if (ObjectUtils.isEmpty(this.deletedPath)) {
      return null;
    }
    return (root, query, builder) -> builder.equal(deletedPath.apply(root), false);
  }

  public Specification<T> haveEntityEqualToAndOwnerIdsInForMeeting(String entity, List<Long> ownerIds) {
    return (root, criteriaQuery, criteriaBuilder) -> {
      Join<T, MeetingRelatedTo> joinForMeetingRelatedTo = getOrCreateLeftJoinForMeetingRelatedTo(root);
      Join<T, MeetingInvitee> joinForMeetingInvitee = getOrCreateLeftJoinForMeetingInvitees(root);
      Join<T, Organizer> joinForMeetingOrganizer = getOrCreateLeftJoinForMeetingOrganizer(root);

      Predicate relatedToPredicate = criteriaBuilder.and(joinForMeetingRelatedTo.get(MeetingRelatedTo_.ownerId).in(ownerIds),
          criteriaBuilder.equal(joinForMeetingRelatedTo.get(MeetingRelatedTo_.entity), entity));
      Predicate inviteesPredicate = criteriaBuilder.and(joinForMeetingInvitee.get(MeetingInvitee_.ownerId).in(ownerIds),
          criteriaBuilder.equal(joinForMeetingInvitee.get(MeetingInvitee_.entity), entity));
      Predicate organizerPredicate = criteriaBuilder.and(joinForMeetingOrganizer.get(Organizer_.ownerId).in(ownerIds),
          criteriaBuilder.equal(joinForMeetingOrganizer.get(Organizer_.entity), entity));

      return criteriaBuilder.or(relatedToPredicate, inviteesPredicate, organizerPredicate);

    };
  }

  public Specification<T> haveEntityEqualToAndOwnerIdsInForEmail(String entity, List<Long> ownerIds) {
    return (root, criteriaQuery, criteriaBuilder) -> {
      Join<T, EmailRelatedTo> joinForEmailRelatedTo = getOrCreateLeftJoinForEmailRelatedTo(root);
      Join<T, EmailRecipient> joinForEmailRecipients = getOrCreateLeftJoinForEmailRecipients(root);
      Join<T, EmailSender> joinForEmailSender = getOrCreateLeftJoinForEmailSender(root);

      Predicate relatedToPredicate = criteriaBuilder.and(joinForEmailRelatedTo.get(EmailRelatedTo_.ownerId).in(ownerIds),
          criteriaBuilder.equal(joinForEmailRelatedTo.get(EmailRelatedTo_.entity), entity));
      Predicate inviteesPredicate = criteriaBuilder.and(joinForEmailRecipients.get(EmailRecipient_.ownerId).in(ownerIds),
          criteriaBuilder.equal(joinForEmailRecipients.get(EmailRecipient_.entity), entity));
      Predicate organizerPredicate = criteriaBuilder.and(joinForEmailSender.get(EmailSender_.ownerId).in(ownerIds),
          criteriaBuilder.equal(joinForEmailSender.get(EmailSender_.entity), entity));

      return criteriaBuilder.or(relatedToPredicate, inviteesPredicate, organizerPredicate);

    };
  }

  public Specification<T> haveEntityEqualToAndEntityIdsInForEmail(String entity, List<Long> entityIds) {
    return (root, criteriaQuery, criteriaBuilder) -> {
      Join<T, EmailRelatedTo> joinForEmailRelatedTo = getOrCreateLeftJoinForEmailRelatedTo(root);
      Join<T, EmailRecipient> joinForEmailRecipients = getOrCreateLeftJoinForEmailRecipients(root);
      Join<T, EmailSender> joinForEmailSender = getOrCreateLeftJoinForEmailSender(root);

      Predicate relatedToPredicate = criteriaBuilder.and(joinForEmailRelatedTo.get(EmailRelatedTo_.entityId).in(entityIds),
          criteriaBuilder.equal(joinForEmailRelatedTo.get(EmailRelatedTo_.entity), entity));
      Predicate inviteesPredicate = criteriaBuilder.and(joinForEmailRecipients.get(EmailRecipient_.entityId).in(entityIds),
          criteriaBuilder.equal(joinForEmailRecipients.get(EmailRecipient_.entity), entity));
      Predicate organizerPredicate = criteriaBuilder.and(joinForEmailSender.get(EmailSender_.entityId).in(entityIds),
          criteriaBuilder.equal(joinForEmailSender.get(EmailSender_.entity), entity));

      return criteriaBuilder.or(relatedToPredicate, inviteesPredicate, organizerPredicate);

    };
  }


  public Specification<T> haveEntityEqualToAndEntityIdsInForMeeting(String entity, List<Long> entityIds) {
    return (root, criteriaQuery, criteriaBuilder) -> {
      Join<T, MeetingRelatedTo> joinForMeetingRelatedTo = getOrCreateLeftJoinForMeetingRelatedTo(root);
      Join<T, MeetingInvitee> joinForMeetingInvitee = getOrCreateLeftJoinForMeetingInvitees(root);
      Join<T, Organizer> joinForMeetingOrganizer = getOrCreateLeftJoinForMeetingOrganizer(root);

      Predicate relatedToPredicate = criteriaBuilder.and(joinForMeetingRelatedTo.get(MeetingRelatedTo_.entityId).in(entityIds),
          criteriaBuilder.equal(joinForMeetingRelatedTo.get(MeetingRelatedTo_.entity), entity));
      Predicate inviteesPredicate = criteriaBuilder.and(joinForMeetingInvitee.get(MeetingInvitee_.entityId).in(entityIds),
          criteriaBuilder.equal(joinForMeetingInvitee.get(MeetingInvitee_.entity), entity));
      Predicate organizerPredicate = criteriaBuilder.and(joinForMeetingOrganizer.get(Organizer_.entityId).in(entityIds),
          criteriaBuilder.equal(joinForMeetingOrganizer.get(Organizer_.entity), entity));

      return criteriaBuilder.or(relatedToPredicate, inviteesPredicate, organizerPredicate);

    };
  }

  public Specification<T> haveEntityEqualToAndIdsInForMeeting(String entity, List<Long> ids) {
    return (root, criteriaQuery, criteriaBuilder) -> {
      Join<T, MeetingRelatedTo> joinForMeetingRelatedTo = getOrCreateLeftJoinForMeetingRelatedTo(root);
      Join<T, MeetingInvitee> joinForMeetingInvitee = getOrCreateLeftJoinForMeetingInvitees(root);
      Join<T, Organizer> joinForMeetingOrganizer = getOrCreateLeftJoinForMeetingOrganizer(root);

      Predicate relatedToPredicate = criteriaBuilder.and(joinForMeetingRelatedTo.get(MeetingRelatedTo_.entityId).in(ids),
          criteriaBuilder.equal(joinForMeetingRelatedTo.get(MeetingRelatedTo_.entity), entity));
      Predicate inviteesPredicate = criteriaBuilder.and(joinForMeetingInvitee.get(MeetingInvitee_.entityId).in(ids),
          criteriaBuilder.equal(joinForMeetingInvitee.get(MeetingInvitee_.entity), entity));
      Predicate organizerPredicate = criteriaBuilder.and(joinForMeetingOrganizer.get(Organizer_.entityId).in(ids),
          criteriaBuilder.equal(joinForMeetingOrganizer.get(Organizer_.entity), entity));

      return criteriaBuilder.or(relatedToPredicate, inviteesPredicate, organizerPredicate);

    };
  }

  public Specification<T> haveEntityEqualToAndEntityIdsIn(String entity, List<Long> entityIds) {
    return createSubquerySpecification(CallRelatedTo_.entity, entity, CallRelatedTo_.entityId, entityIds);
  }

  public Specification<T> haveEntityEqualToAndOwnerIdsIn(String entity, List<Long> ownerIds) {
    return createSubquerySpecification(CallRelatedTo_.entity, entity, CallRelatedTo_.ownerId, ownerIds);
  }

  public Specification<T> haveLoggedInUserEqualTo(Long userId) {
    return (root, query, builder) -> {
      Predicate ownerIdPredicate = builder.equal(root.get(Call_.OWNER_ID), userId);

      Subquery<Long> subquery = query.subquery(Long.class);
      Root<CallRelatedTo> subRoot = subquery.from(CallRelatedTo.class);

      subquery.select(subRoot.get(CallRelatedTo_.CALL_ID))
          .where(builder.and(
              builder.equal(subRoot.get(CallRelatedTo_.OWNER_ID), userId),
              builder.equal(subRoot.get(CallRelatedTo_.CALL_ID), root.get(Call_.ID))
          ));

      Predicate userIdInCallRelatedToPredicate = builder.exists(subquery);

      return builder.or(ownerIdPredicate, userIdInCallRelatedToPredicate);
    };
  }

  private Specification<T> createSubquerySpecification(
      SingularAttribute<CallRelatedTo, ?> entityAttribute,
      Object entityValue,
      SingularAttribute<CallRelatedTo, ?> idAttribute,
      List<Long> ids
  ) {
    return (root, criteriaQuery, criteriaBuilder) -> {
      Subquery<Long> subquery = criteriaQuery.subquery(Long.class);
      Root<CallRelatedTo> subRoot = subquery.from(CallRelatedTo.class);

      Predicate predicate = criteriaBuilder.conjunction();

      if (entityAttribute != null && entityValue != null) {
        predicate = criteriaBuilder.and(
            predicate,
            criteriaBuilder.equal(subRoot.get(entityAttribute), entityValue)
        );
      }

      if (ObjectUtils.isNotEmpty(ids)) {
        predicate = criteriaBuilder.and(
            predicate,
            subRoot.get(idAttribute).in(ids)
        );
      }

      subquery.select(subRoot.get(CallRelatedTo_.callId))
          .where(predicate)
          .groupBy(subRoot.get(CallRelatedTo_.callId));

      return root.get(Call_.ID).in(subquery);
    };
  }

  private Join<T, CallRelatedTo> getOrCreateLeftJoin(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(Call_.CALL_RELATED_TOS))
        .map(j -> ((Join<T, CallRelatedTo>) j))
        .findFirst()
        .orElseGet(() -> root.join(Call_.CALL_RELATED_TOS, JoinType.INNER));
  }

  public Specification<T> haveLoggedInUserEqualToForMeeting(Long userId) {
    return (root, criteriaQuery, criteriaBuilder) -> {
      Join<T, MeetingRelatedTo> joinForMeetingRelatedTo = getOrCreateLeftJoinForMeetingRelatedTo(root);
      Join<T, MeetingInvitee> joinForMeetingInvitees = getOrCreateLeftJoinForMeetingInvitees(root);
      Join<T, Organizer> joinForMeetingOrganizer = getOrCreateLeftJoinForMeetingOrganizer(root);

      Predicate relatedToPredicate = criteriaBuilder.equal(joinForMeetingRelatedTo.get(MeetingRelatedTo_.ownerId), userId);
      Predicate inviteesPredicate = criteriaBuilder.equal(joinForMeetingInvitees.get(MeetingInvitee_.ownerId), userId);
      Predicate organizerPredicate = criteriaBuilder.equal(joinForMeetingOrganizer.get(Organizer_.ownerId), userId);
      Predicate finalPredicate = criteriaBuilder.or(relatedToPredicate, inviteesPredicate, organizerPredicate);
      return finalPredicate;
    };
  }


  private Join<T, MeetingInvitee> getOrCreateLeftJoinForMeetingInvitees(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(Meeting_.MEETING_INVITEES))
        .map(j -> ((Join<T, MeetingInvitee>) j))
        .findFirst()
        .orElseGet(() -> root.join(Meeting_.MEETING_INVITEES, JoinType.LEFT));
  }

  private Join<T, MeetingRelatedTo> getOrCreateLeftJoinForMeetingRelatedTo(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(Meeting_.MEETING_RELATED_TOS))
        .map(j -> ((Join<T, MeetingRelatedTo>) j))
        .findFirst()
        .orElseGet(() -> root.join(Meeting_.MEETING_RELATED_TOS, JoinType.LEFT));
  }

  private Join<T, Organizer> getOrCreateLeftJoinForMeetingOrganizer(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(Meeting_.ORGANIZER))
        .map(j -> ((Join<T, Organizer>) j))
        .findFirst()
        .orElseGet(() -> root.join(Meeting_.ORGANIZER, JoinType.LEFT));
  }


  public Specification<T> haveLoggedInUserEqualToForEmail(Long userId) {
    return (root, criteriaQuery, criteriaBuilder) -> {
      Join<T, EmailRelatedTo> joinForEmailRelatedTo = getOrCreateLeftJoinForEmailRelatedTo(root);
      Join<T, EmailRecipient> joinForEmailRecipients = getOrCreateLeftJoinForEmailRecipients(root);
      Join<T, EmailSender> emailSenderJoin = getOrCreateLeftJoinForEmailSender(root);

      Predicate relatedToPredicate = criteriaBuilder.equal(joinForEmailRelatedTo.get(EmailRelatedTo_.ownerId), userId);
      Predicate recipientPredicate = criteriaBuilder.equal(joinForEmailRecipients.get(EmailRecipient_.ownerId), userId);
      Predicate senderPredicate = criteriaBuilder.equal(emailSenderJoin.get(EmailSender_.ownerId), userId);
      Predicate finalPredicate = criteriaBuilder.or(relatedToPredicate, recipientPredicate, senderPredicate);
      return finalPredicate;
    };
  }


  private Join<T, EmailRelatedTo> getOrCreateLeftJoinForEmailRelatedTo(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(Email_.EMAIL_RELATED_TO))
        .map(j -> ((Join<T, EmailRelatedTo>) j))
        .findFirst()
        .orElseGet(() -> root.join(Email_.EMAIL_RELATED_TO, JoinType.LEFT));
  }

  private Join<T, EmailRecipient> getOrCreateLeftJoinForEmailRecipients(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(Email_.EMAIL_RECIPIENTS))
        .map(j -> ((Join<T, EmailRecipient>) j))
        .findFirst()
        .orElseGet(() -> root.join(Email_.EMAIL_RECIPIENTS, JoinType.LEFT));
  }

  private Join<T, EmailSender> getOrCreateLeftJoinForEmailSender(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(Email_.SENDER))
        .map(j -> ((Join<T, EmailSender>) j))
        .findFirst()
        .orElseGet(() -> root.join(Email_.SENDER, JoinType.LEFT));
  }
}
