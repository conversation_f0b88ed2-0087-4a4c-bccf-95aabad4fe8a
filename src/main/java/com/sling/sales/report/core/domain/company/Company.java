package com.sling.sales.report.core.domain.company;

import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.mq.event.Revenue;
import com.sling.sales.report.security.domain.User;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

@Entity
@Getter
@NoArgsConstructor
@TypeDefs({@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)})
public class Company implements Fact {

  @Id
  private long id;
  @NotNull
  private String name;
  private long tenantId;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "owned_by")
  private User ownedBy;

  @Embedded
  @AttributeOverrides(
      value = {
          @AttributeOverride(name = "currencyId", column = @Column(name = "revenue_currency_id")),
          @AttributeOverride(name = "value", column = @Column(name = "revenue_value"))
      })
  private Revenue annualRevenue;

  @Embedded
  @AttributeOverrides(
      value = {
          @AttributeOverride(name = "id", column = @Column(name = "industry_id")),
          @AttributeOverride(name = "name", column = @Column(name = "industry_value"))
      })
  private PickListValue industry;

  @Embedded
  @AttributeOverrides(
      value = {
          @AttributeOverride(name = "id", column = @Column(name = "number_of_employees_id")),
          @AttributeOverride(name = "name", column = @Column(name = "number_of_employees_value"))
      })
  private PickListValue numberOfEmployees;

  @Embedded
  @AttributeOverrides(
      value = {
          @AttributeOverride(name = "id", column = @Column(name = "business_type_id")),
          @AttributeOverride(name = "name", column = @Column(name = "business_type_value"))
      })
  private PickListValue businessType;

  @Embedded
  @AttributeOverrides(
      value = {
          @AttributeOverride(name = "id", column = @Column(name = "country_id")),
          @AttributeOverride(name = "name", column = @Column(name = "country_value"))
      })
  private PickListValue country;

  private String city;

  private String state;

  private Date createdAt;
  private Date updatedAt;

  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  @NotNull
  private CompanyEventData eventPayload;

  @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, mappedBy = "company", orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<CompanyPicklistValue> companyPicklistValues = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, mappedBy = "company", orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<CompanyCustomTextValue> companyCustomTextValues = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, mappedBy = "company", cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<CompanyCustomNumberValue> companyCustomNumberValues = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, mappedBy = "company", cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<CompanyCustomDatePickerValue> companyCustomDatePickerValues = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, mappedBy = "company", cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<CompanyCustomDatetimePickerValue> companyCustomDatetimePickerValues = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, mappedBy = "company", cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<CompanyCustomCheckboxValue> companyCustomCheckboxValues = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, mappedBy = "company", cascade = CascadeType.ALL, orphanRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private Set<CompanyCustomMultiPicklistValue> companyCustomMultiPicklistValues = new HashSet<>();

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "created_by")
  private User createdBy;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "updated_by")
  private User updatedBy;

  @ManyToOne
  @JoinColumn(name = "imported_by")
  private User importedBy;

  private boolean deleted;

  @OneToMany(fetch = FetchType.EAGER, mappedBy = "company", cascade = CascadeType.ALL, orphanRemoval = true)
  private Set<CompanyPhoneNumber> companyPhoneNumbers=new HashSet<>();


  @OneToMany(fetch = FetchType.EAGER, mappedBy = "company", cascade = CascadeType.ALL, orphanRemoval = true)
  private Set<CompanyEmail> companyEmails =new HashSet<>();

  private String uniqueText1;

  private String uniqueText2;

  public Company(long id, String name, long tenantId, User ownedBy, Revenue annualRevenue, PickListValue industry,
      PickListValue numberOfEmployees, PickListValue businessType, PickListValue country, String city, String state, Date createdAt,
      Date updatedAt, @NotNull CompanyEventData eventPayload, User createdBy, User updatedBy, User importedBy, boolean deleted, String uniqueText1,String uniqueText2) {
    this.id = id;
    this.name = name;
    this.tenantId = tenantId;
    this.ownedBy = ownedBy;
    this.annualRevenue = annualRevenue;
    this.industry = industry;
    this.numberOfEmployees = numberOfEmployees;
    this.businessType = businessType;
    this.country = country;
    this.city = city;
    this.state = state;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.eventPayload = eventPayload;
    this.createdBy = createdBy;
    this.updatedBy = updatedBy;
    this.importedBy = importedBy;
    this.deleted = deleted;
    this.uniqueText1 = uniqueText1;
    this.uniqueText2 = uniqueText2;
  }

  public static Company create(long id, String name, long tenantId, User ownedBy, Revenue annualRevenue, PickListValue industry,
      PickListValue numberOfEmployees,
      PickListValue businessType, PickListValue country, String city,
      String state, Date createdAt, Date updatedAt, CompanyEventData companyEventData, User createdBy, User updatedBy, User importedBy, boolean deleted, String uniqueText1, String uniqueText2) {
    return new Company(id, name, tenantId, ownedBy, annualRevenue, industry, numberOfEmployees, businessType, country,
        city, state, createdAt, updatedAt, companyEventData, createdBy, updatedBy, importedBy, deleted, uniqueText1, uniqueText2);
  }

  public Company update(String name, Revenue annualRevenue, PickListValue industry, PickListValue numberOfEmployees, PickListValue businessType,
      PickListValue country,
      String city,
      String state, Date updatedAt, CompanyEventData eventPayload, User ownedBy, User updatedBy, boolean deleted, String uniqueText1, String uniqueText2) {
    this.name = name;
    this.annualRevenue = annualRevenue;
    this.industry = industry;
    this.numberOfEmployees = numberOfEmployees;
    this.businessType = businessType;
    this.country = country;
    this.city = city;
    this.state = state;
    this.updatedAt = updatedAt;
    this.eventPayload = eventPayload;
    this.ownedBy = ownedBy;
    this.updatedBy = updatedBy;
    this.deleted = deleted;
    this.uniqueText1=uniqueText1;
    this.uniqueText2=uniqueText2;
    return this;
  }

  public void addCompanyPicklistValues(Set<CompanyPicklistValue> companyPicklistValues) {
    this.companyPicklistValues.clear();
    this.companyPicklistValues.addAll(companyPicklistValues);
  }

  public Set<CompanyPicklistValue> getCompanyPicklistValues() {
    return Collections.unmodifiableSet(this.companyPicklistValues);
  }

  public void addCompanyCustomTextValues(Set<CompanyCustomTextValue> companyCustomTextValues) {
    this.companyCustomTextValues.clear();
    this.companyCustomTextValues.addAll(companyCustomTextValues);
  }

  public Set<CompanyCustomTextValue> getCompanyCustomTextValues() {
    return Collections.unmodifiableSet(this.companyCustomTextValues);
  }

  public void addCompanyCustomNumberValues(Set<CompanyCustomNumberValue> companyCustomNumberValues) {
    this.companyCustomNumberValues.clear();
    this.companyCustomNumberValues.addAll(companyCustomNumberValues);
  }

  public Set<CompanyCustomNumberValue> getCompanyCustomNumberValues() {
    return Collections.unmodifiableSet(this.companyCustomNumberValues);
  }

  public void addCompanyCustomDatePickerValues(Set<CompanyCustomDatePickerValue> companyCustomDatePickerValues) {
    this.companyCustomDatePickerValues.clear();
    this.companyCustomDatePickerValues.addAll(companyCustomDatePickerValues);
  }

  public Set<CompanyCustomDatePickerValue> getCompanyCustomDatePickerValues() {
    return Collections.unmodifiableSet(this.companyCustomDatePickerValues);
  }
  
  public void addCompanyCustomDatetimePickerValues(Set<CompanyCustomDatetimePickerValue> companyCustomDatetimePickerValues) {
    this.companyCustomDatetimePickerValues.clear();
    this.companyCustomDatetimePickerValues.addAll(companyCustomDatetimePickerValues);
  }

  public Set<CompanyCustomDatetimePickerValue> getCompanyCustomDatetimePickerValues() {
    return Collections.unmodifiableSet(this.companyCustomDatetimePickerValues);
  }

  public void addCompanyCustomCheckboxValues(Set<CompanyCustomCheckboxValue> companyCustomCheckboxValues) {
    this.companyCustomCheckboxValues.clear();
    this.companyCustomCheckboxValues.addAll(companyCustomCheckboxValues);
  }

  public Set<CompanyCustomCheckboxValue> getCompanyCustomCheckboxValues() {
    return Collections.unmodifiableSet(this.companyCustomCheckboxValues);
  }

  public void addCompanyCustomMultiPicklistValues(Set<CompanyCustomMultiPicklistValue> companyCustomMultiPicklistValues) {
    this.companyCustomMultiPicklistValues =new HashSet<>(companyCustomMultiPicklistValues);
  }

  public Set<CompanyCustomMultiPicklistValue> getCompanyCustomMultiPicklistValues() {
    return Collections.unmodifiableSet(this.companyCustomMultiPicklistValues);
  }

  public void addCompanyPhoneNumbers(Set<CompanyPhoneNumber> companyPhoneNumbers) {
    this.companyPhoneNumbers.clear();
    this.companyPhoneNumbers.addAll(companyPhoneNumbers);
  }

  public void addCompanyEmails(Set<CompanyEmail> companyEmails) {
    this.companyEmails.clear();
    this.companyEmails.addAll(companyEmails);
  }
}
