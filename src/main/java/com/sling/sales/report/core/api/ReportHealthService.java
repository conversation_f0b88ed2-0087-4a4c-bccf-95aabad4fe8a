package com.sling.sales.report.core.api;

import com.sling.sales.report.core.domain.report.ReportHealthFacade;
import org.hibernate.ObjectNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class ReportHealthService {
  @Value("${health.tenantId}")
  private long tenantId;

  private final ReportHealthFacade reportHealthFacade;

  @Autowired
  public ReportHealthService(ReportHealthFacade reportHealthFacade) {
    this.reportHealthFacade = reportHealthFacade;
  }

  public Integer getHealthStatus() {
    try {
      reportHealthFacade.getReportIdByTenantId(tenantId);
      return 200;
    } catch (ObjectNotFoundException objectNotFoundException) {
      return 200;
    } catch (Exception e) {
      return 503;
    }
  }
}
