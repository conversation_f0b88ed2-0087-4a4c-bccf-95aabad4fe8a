package com.sling.sales.report.core.domain.aggregation.fact;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import com.sling.sales.report.core.api.response.AggregateRecord;
import com.sling.sales.report.core.api.response.GoalAggregateResponse;
import com.sling.sales.report.core.domain.aggregation.dimension.GoalDetail;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import com.sling.sales.report.core.domain.goal.GoalFieldValue;
import java.util.List;
import java.util.Set;
import java.util.function.BiFunction;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;

public interface FactAggregationRepository {

  <T extends Fact> List<AggregateRecord> groupByMetric(
      Specification<T> withSpecification,
      List<GroupByDimension<T>> groupByDimension,
      List<BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>>> metricFn,
      Class<T> clazz, String timezone
  );

  <T extends Fact> List<AggregateRecord> getAggregateResult(
      Specification<T> withSpecification,
      List<GroupByDimension<T>> groupByDimension,
      List<BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>>> metricFn,
      Class<T> clazz, String timezone
  );

  <T extends Fact> GoalAggregateResponse groupByGoalMetric(
      Specification<T> withSpecification,
      List<GroupByDimension<T>> groupByDimension,
      List<BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>>> metricFn,
      Class<T> clazz, String timezone,
      GoalDetail goalDetail,
      Set<GoalFieldValue> goalFieldValues,
      FactFilter<T> dimensionAsFilter,
      List<Metric> metrics);
}
