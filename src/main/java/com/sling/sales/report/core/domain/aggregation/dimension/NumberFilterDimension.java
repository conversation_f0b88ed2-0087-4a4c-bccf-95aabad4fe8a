package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.between;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.greater;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.greater_or_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.in;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.less;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.less_or_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_between;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_in;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;

public class NumberFilterDimension<T extends Fact, V extends Number & Comparable<V>> implements FilterDimension<T> {

  private final String name;
  private final List<Operator> allowedOperators = asList(equal, not_equal, greater, greater_or_equal, less, less_or_equal,
      between, not_between, in, not_in, is_null, is_not_null);
  private final Function<Root<T>, Path<V>> pathToColumn;
  private final Function<String, V> fromString;

  public NumberFilterDimension(String name,
      Function<Root<T>, Path<V>> pathToColumn,
      Function<String, V> fromString) {
    this.name = name;
    this.pathToColumn = pathToColumn;
    this.fromString = fromString;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  @SuppressWarnings("unchecked")
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    switch (operator) {
      case equal:
        return hasId(getValue(value));
      case not_equal:
        return doesNotHaveId(getValue(value)).or(isNull());
      case greater:
        return greaterThan(getValue(value));
      case greater_or_equal:
        return greaterThanOrEqualTo(getValue(value));
      case less:
        return lessThan(getValue(value));
      case less_or_equal:
        return lessThanOrEqual(getValue(value));
      case between:
        return betweenValues(getListOfString((List<?>) value));
      case not_between:
        return notBetweenValues(getListOfString((List<?>) value));
      case in:
        return inValues((String) value);
      case not_in:
        return notInValues((String) value);
      case is_null:
        return isNull();
      case is_not_null:
        return isNotNull();
      default:
        return null;
    }
  }

  private List<String> getListOfString(List<?> value) {
    return value.stream()
        .filter(element -> element instanceof String || element instanceof Number)
        .map(Object::toString)
        .collect(Collectors.toList());
  }

  private V getValue(Object value) {
    if(value instanceof String) {
      return fromString.apply((String) value);
    }
    return (V) value;
  }

  private Specification<T> notInValues(String value) {
    List<String> values = getParameters(value);
    return ((root, query, builder) -> {
      Path<V> jsonbExpression = pathToColumn.apply(root);
      return jsonbExpression.in(values).not();
    });
  }

  private Specification<T> inValues(String value) {
    List<String> values = getParameters(value);
    return ((root, query, builder) -> {
      Path<V> jsonbExpression = pathToColumn.apply(root);
      return jsonbExpression.in(values);
    });
  }

  private Specification<T> notBetweenValues(List<String> parameters) {
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) -> builder.between(
        pathToColumn.apply(root),
        fromString.apply(parameters.get(0)),
        fromString.apply(parameters.get(1))
    ).not());
  }

  private Specification<T> betweenValues(List<String> parameters) {
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) -> builder.between(
        pathToColumn.apply(root),
        fromString.apply(parameters.get(0)),
        fromString.apply(parameters.get(1))
    ));
  }

  private List<String> getParameters(String value) {
    return Arrays.stream(value.split(","))
        .map(String::trim)
        .collect(toList());
  }

  private Specification<T> lessThanOrEqual(V value) {
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) -> builder.lessThanOrEqualTo(
        pathToColumn.apply(root),
        value
    ));
  }

  private Specification<T> lessThan(V value) {
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) -> builder.lessThan(
        pathToColumn.apply(root),
        value
    ));
  }

  private Specification<T> greaterThanOrEqualTo(V value) {
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) -> builder.greaterThanOrEqualTo(
        pathToColumn.apply(root),
        value
    ));
  }

  private Specification<T> greaterThan(V value) {
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) -> builder.greaterThan(
        pathToColumn.apply(root),
        value
    ));
  }

  private Specification<T> hasId(V value) {
    return ((root, query, builder) ->
        builder.equal(
            pathToColumn.apply(root),
            value
        )
    );
  }

  private Specification<T> doesNotHaveId(V value) {
    return ((root, query, builder) ->
        builder.notEqual(
            pathToColumn.apply(root),
            value
        )
    );
  }

  Specification<T> isNull() {
    return ((root, query, builder) -> builder.isNull(
        pathToColumn.apply(root)
    ));
  }

  Specification<T> isNotNull() {
    return ((root, query, builder) -> builder.isNotNull(
        pathToColumn.apply(root)
    ));
  }

}
