package com.sling.sales.report.core.api.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class Metric {

  private final String field;
  private final MetricType type;

  @JsonCreator
  public Metric(@JsonProperty("field") String field, @JsonProperty("type") MetricType type) {
    this.field = field;
    this.type = type;
  }
}
