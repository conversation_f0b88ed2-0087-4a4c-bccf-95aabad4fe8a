package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import java.util.Optional;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.query.criteria.internal.expression.LiteralExpression;
import org.hibernate.query.criteria.internal.predicate.ComparisonPredicate;
import org.springframework.data.jpa.domain.Specification;

public class CustomFieldMetricBuilder {

  public static <T, V> Expression<? extends Number> buildExpressionByMetricType(MetricType metricType, Root<T> root, CriteriaBuilder builder,
      SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> getFieldId, String attributeName, long fieldId) {
    Join<T, V> join = getOrCreateLeftJoin(root, builder, dimensionField, getFieldId, fieldId);
    if (metricType == MetricType.COUNT) {
      return builder.count(join.get(attributeName));
    }
    if (metricType == MetricType.SUM) {
      return builder.sum(join.get(attributeName));
    }
    return builder.avg(join.get(attributeName));
  }

  private static <T, V> Join<T, V> getOrCreateLeftJoin(Root<T> root, CriteriaBuilder builder,
      SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> getFieldId, long fieldId) {
    return root.getJoins()
        .stream()
        .filter(j -> isFieldIdAlreadyPresent(j, fieldId))
        .map(j -> ((Join<T, V>) j))
        .findFirst()
        .orElseGet(() -> createJoin(root, builder, dimensionField, getFieldId, fieldId));
  }

  private static <T, V> Optional<Specification<T>> getSpecification(Join<T, V> join, Function<Join<T, V>, Path<Long>> getFieldId, long fieldId) {
    return Optional.ofNullable(hasFieldId(join, getFieldId, fieldId).or(isFieldIdNull(join, getFieldId)));
  }

  private static <T, V> Specification<T> hasFieldId(Join<T, V> join, Function<Join<T, V>, Path<Long>> getFieldId, long fieldId) {
    return ((root, query, builder) -> builder.equal(getFieldId.apply(join), fieldId));
  }

  private static <T, V> Specification<T> isFieldIdNull(Join<T, V> join, Function<Join<T, V>, Path<Long>> getFieldId) {
    return ((root, query, builder) -> builder.isNull(getFieldId.apply(join)));
  }

  private static <T, V> Join<T, V> createJoin(Root<T> root, CriteriaBuilder builder, SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> getFieldId, long fieldId) {
    Join<T, V> join = root.join(dimensionField, JoinType.LEFT);
    if (getSpecification(join, getFieldId, fieldId).isPresent()) {
      join.on(getSpecification(join, getFieldId, fieldId).get().toPredicate(root, builder.createQuery(), builder));
    }
    return join;
  }

  private static <T> boolean isFieldIdAlreadyPresent(Join<T, ?> j, long fieldId) {
    if (ObjectUtils.isEmpty(j.getOn()) || ObjectUtils.isEmpty(j.getOn().getExpressions()) || j.getOn().getExpressions().size() < 2) {
      return false;
    }
    ComparisonPredicate comparisonPredicate = (ComparisonPredicate) j.getOn().getExpressions().get(1);
    LiteralExpression<Long> literalExpression = (LiteralExpression<Long>) comparisonPredicate.getRightHandOperand();
    return literalExpression.getLiteral().equals(fieldId);
  }
}
