package com.sling.sales.report.core.api;

import com.sling.sales.report.cache.CacheFacade;
import com.sling.sales.report.config.api.response.EntityGoalConfiguration;
import com.sling.sales.report.config.api.response.EntityGoalListConfiguration;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.call.Call;
import com.sling.sales.report.core.domain.report.ReportType;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.UserService;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class GoalConfigCacheService {

  private final GoalConfigService goalConfigService;
  private final CacheFacade cacheFacade;
  private final UserService userService;

  @Autowired
  public GoalConfigCacheService(GoalConfigService goalConfigService, CacheFacade cacheFacade,
      UserService userService) {
    this.goalConfigService = goalConfigService;
    this.cacheFacade = cacheFacade;
    this.userService = userService;
  }

  public List<EntityGoalConfiguration<? extends Fact>> getAllGoalConfigurations() {
    Optional<EntityGoalConfiguration<? extends Fact>> leadGoalConfig = getGoalConfigurationByEntity(ReportType.LEAD);
    Optional<EntityGoalConfiguration<? extends Fact>> dealGoalConfig = getGoalConfigurationByEntity(ReportType.DEAL);
    Optional<EntityGoalConfiguration<? extends Fact>> meetingGoalConfig = getGoalConfigurationByEntity(ReportType.MEETING);
    Optional<EntityGoalConfiguration<? extends Fact>> callGoalConfig = getGoalConfigurationByEntity(ReportType.CALL);
    return List.of(leadGoalConfig, dealGoalConfig, meetingGoalConfig, callGoalConfig)
        .stream()
        .filter(entityGoalConfiguration -> entityGoalConfiguration.isPresent())
        .map(entityGoalConfiguration -> entityGoalConfiguration.get())
        .collect(Collectors.toList());
  }


  public Optional<EntityGoalConfiguration<? extends Fact>> getGoalConfigurationByEntity(ReportType entity) {
    User loggedInUser = userService.getLoggedInUser();
    long tenantId = loggedInUser.getTenantId();
    if (!hasAccessToFacts(entity.getEntityClass(), loggedInUser)) {
      return Optional.empty();
    }

    EntityGoalConfiguration<? extends Fact> goalConfiguration = cacheFacade.getGoalConfigurationByEntity(tenantId, entity.name().toLowerCase());
    if (goalConfiguration != null) {
      return Optional.ofNullable(goalConfiguration);
    }
    EntityGoalConfiguration<? extends Fact> goalConfigurationByEntity1 = goalConfigService.getGoalConfigurationByEntity(entity.getEntityClass());
    cacheFacade.putGoalConfigurationByEntity(tenantId, entity.name().toLowerCase(), goalConfigurationByEntity1);
    return Optional.ofNullable(goalConfigurationByEntity1);
  }

  public void refreshGoalConfiguration(long tenantId, String entity) {
    cacheFacade.refreshGoalConfigurationByEntity(tenantId, entity.toLowerCase());
  }

  public Optional<EntityGoalListConfiguration> getGoalListingConfigurationByEntity1(String entityTypePlural) {
    User loggedInUser = userService.getLoggedInUser();
    long tenantId = loggedInUser.getTenantId();
    var entity = Arrays.stream(ReportType.values())
        .filter(entityType -> entityType.getEntityPlural().toLowerCase().equals(entityTypePlural))
        .findFirst();
    if (entity.isEmpty() || !hasAccessToFacts(entity.get().getEntityClass(), loggedInUser)) {
      return Optional.empty();
    }

    EntityGoalListConfiguration listConfiguration = cacheFacade.getGoalListingConfigurationByEntity(tenantId, entityTypePlural);
    if (listConfiguration != null) {
      return Optional.of(listConfiguration);
    }
    EntityGoalListConfiguration goalListingConfigurationByEntity1 = goalConfigService.getGoalListingConfigurationByEntity1(
        entity.get().getEntityClass());
    cacheFacade.putGoalListingConfigurationByEntity(tenantId, entityTypePlural, goalListingConfigurationByEntity1);
    return Optional.ofNullable(goalListingConfigurationByEntity1);
  }

  public void refreshGoalListConfigurationByEntity(long tenantId, String entity) {
    cacheFacade.refreshGoalListingConfigurationByEntity(tenantId, entity);
  }

  private <T extends Fact> boolean hasAccessToFacts(Class<T> factClass, User loggedInUser) {
    if (Call.class.equals(factClass)) {
      return loggedInUser.canReadCall() && !loggedInUser.returnEntitiesHavingCallPermission().isEmpty();
    }
    return loggedInUser.hasAccessToFacts(factClass);
  }
}
