package com.sling.sales.report.core.api;

import com.sling.sales.report.core.api.request.AggregationRequestV1;
import com.sling.sales.report.core.api.request.AggregationRequestV2;
import com.sling.sales.report.core.api.request.AggregationRequestV3;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.api.response.AggregateRecord;
import com.sling.sales.report.core.api.response.AggregateRecordV1;
import com.sling.sales.report.core.api.response.ReportResponse;
import com.sling.sales.report.core.api.response.ReportResponseV1;
import com.sling.sales.report.core.api.response.ReportResponseV2;
import com.sling.sales.report.core.api.response.ReportSummaries;
import com.sling.sales.report.core.api.response.ReportSummariesV1;
import com.sling.sales.report.core.api.response.ReportSummariesV1.ReportWiseSummaryV1;
import com.sling.sales.report.core.api.response.ReportSummariesV2;
import com.sling.sales.report.core.api.response.ReportSummariesV2.ReportWiseSummaryV2;
import com.sling.sales.report.dto.Header;
import com.sling.sales.report.dto.HeaderDetail;
import com.sling.sales.report.dto.HeaderDetailV1;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import reactor.core.publisher.Mono;

@NoArgsConstructor
public class RequestResponseMapper {

  public static ReportResponseV1 toReportResponseV1(ReportResponse reportResponse) {
    AggregationRequestV3 aggregationRequest = reportResponse.getConfig();
    AggregationRequestV1 aggregationRequestV1 = new AggregationRequestV1(aggregationRequest.getFilters(),
        toArrayOfStrings(aggregationRequest.getGroupBy()),
        aggregationRequest.getDateRange(), aggregationRequest.getMetrics()
        .get(0));
    return new ReportResponseV1(reportResponse.getId(), reportResponse.getName(), reportResponse.getDescription(), reportResponse.getReportType(),
        reportResponse.getChartType(),
        aggregationRequestV1, reportResponse.getRecordActions(), reportResponse.getCreatedBy(), reportResponse.getCategory());
  }

  public static ReportResponseV2 toReportResponseV2(ReportResponse reportResponse) {
    AggregationRequestV3 aggregationRequest = reportResponse.getConfig();
    AggregationRequestV2 aggregationRequestV2 = new AggregationRequestV2(aggregationRequest.getFilters(),
        toArrayOfStrings(aggregationRequest.getGroupBy()),
        aggregationRequest.getDateRange(), aggregationRequest.getMetrics());
    return new ReportResponseV2(reportResponse.getId(), reportResponse.getName(), reportResponse.getDescription(), reportResponse.getReportType(),
        reportResponse.getChartType(),
        aggregationRequestV2, reportResponse.getRecordActions(), reportResponse.getCreatedBy(), reportResponse.getCategory());
  }

  public static List<AggregateRecordV1> toAggregateRecordsV1(List<AggregateRecord> aggregateRecords) {
    List<AggregateRecordV1> aggregateRecordV1List = new ArrayList<>();
    aggregateRecords.forEach(aggregateRecord ->
    {
      Number value = ObjectUtils.isEmpty(aggregateRecord.getValues()) ? null : aggregateRecord.getValues().get(0);
      AggregateRecordV1 aggregateRecordV1 = new AggregateRecordV1(aggregateRecord.getId(), aggregateRecord.getName(), value);
      toAggregateRecordsV1(aggregateRecord, aggregateRecordV1);
      aggregateRecordV1List.add(aggregateRecordV1);
    });
    return aggregateRecordV1List;
  }

  private static void toAggregateRecordsV1(AggregateRecord aggregateRecord, AggregateRecordV1 aggregateRecordV1) {
    if (aggregateRecord.getDimensions().isEmpty()) {
      addToDimensions(aggregateRecord, aggregateRecordV1);
    }
    aggregateRecord.getDimensions().forEach(aggregateRecordObject -> {
      Number value = ObjectUtils.isEmpty(aggregateRecordObject.getValues()) ? null : aggregateRecordObject.getValues().get(0);
      AggregateRecordV1 aggregateRecordV1Object = new AggregateRecordV1(aggregateRecordObject.getId(), aggregateRecordObject.getName(), value);
      aggregateRecordV1.addDimension(aggregateRecordV1Object);
      toAggregateRecordsV1(aggregateRecordObject, aggregateRecordV1Object);
    });
  }

  private static void addToDimensions(AggregateRecord aggregateRecord, AggregateRecordV1 aggregateRecordV1) {
    aggregateRecord.getDimensions()
        .forEach(aggregateRecordObject -> {
          Number value = ObjectUtils.isEmpty(aggregateRecordObject.getValues()) ? null : aggregateRecordObject.getValues().get(0);
          AggregateRecordV1 aggregateRecordV1Object = new AggregateRecordV1(aggregateRecordObject.getId(), aggregateRecordObject.getName(), value);
          aggregateRecordV1.addDimension(aggregateRecordV1Object);
        });
  }

  public static ReportSummariesV1 toReportSummariesV1(ReportSummaries reportSummariesMono) {
      List<ReportWiseSummaryV1> reportWiseSummaryV1List = toReportWiseSummariesV1(reportSummariesMono);
      return new ReportSummariesV1(reportWiseSummaryV1List);
  }

  public static ReportSummariesV2 toReportSummariesV2(ReportSummaries reportSummariesMono) {
      List<ReportWiseSummaryV2> reportWiseSummaryV2List = toReportWiseSummariesV2(reportSummariesMono);
      return new ReportSummariesV2(reportWiseSummaryV2List);
}

  private static List<ReportWiseSummaryV1> toReportWiseSummariesV1(ReportSummaries reportSummaries) {
    return reportSummaries.getContent()
        .stream()
        .map(reportWiseSummary -> {
          List<AggregateRecordV1> aggregateRecordV1List = toAggregateRecordsV1(reportWiseSummary.getData());
          HeaderDetailV1 headerDetailV1 = toHeaderDetailV1(reportWiseSummary.getHeaderDetail());
          AggregationRequestV1 aggregationRequestV1 = toAggregateRequestV1(reportWiseSummary.getConfig());
          return new ReportWiseSummaryV1(reportWiseSummary.getId(), reportWiseSummary.getName(),
              reportWiseSummary.getChartType(), reportWiseSummary.getReportCategory(), aggregateRecordV1List, headerDetailV1,
              aggregationRequestV1, reportWiseSummary.getDescription(), reportWiseSummary.getReportType(), reportWiseSummary.getRecordActions(),
              reportWiseSummary.getCreatedBy());
        }).collect(Collectors.toList());
  }

  private static List<ReportWiseSummaryV2> toReportWiseSummariesV2(ReportSummaries reportSummaries) {
    return reportSummaries.getContent()
        .stream()
        .map(reportWiseSummary -> {
          List<AggregateRecord> aggregateRecords = reportWiseSummary.getData();
          AggregationRequestV2 aggregationRequestV2 = toAggregateRequestV2(reportWiseSummary.getConfig());
          return new ReportWiseSummaryV2(reportWiseSummary.getId(), reportWiseSummary.getName(),
              reportWiseSummary.getChartType(), reportWiseSummary.getReportCategory(), aggregateRecords, reportWiseSummary.getHeaderDetail(),
              aggregationRequestV2, reportWiseSummary.getDescription(), reportWiseSummary.getReportType(), reportWiseSummary.getRecordActions(),
              reportWiseSummary.getCreatedBy());
        }).collect(Collectors.toList());
  }

  private static AggregationRequestV1 toAggregateRequestV1(AggregationRequestV3 config) {
    Metric metric = config.getMetrics().get(0);
    return new AggregationRequestV1(config.getFilters(), toArrayOfStrings(config.getGroupBy()), config.getDateRange(), metric);
  }

  private static AggregationRequestV2 toAggregateRequestV2(AggregationRequestV3 config) {
    return new AggregationRequestV2(config.getFilters(), toArrayOfStrings(config.getGroupBy()), config.getDateRange(), config.getMetrics());
  }

  private static HeaderDetailV1 toHeaderDetailV1(HeaderDetail headerDetail) {
    Header header = headerDetail.getMetricHeaders().get(0);
    return new HeaderDetailV1(headerDetail.getGroupByHeaders(), header.getMetricHeader(), header.getMetricType());
  }

  private static List<String> toArrayOfStrings(List<GroupByField> groupByFields) {
    return groupByFields
        .stream()
        .map(GroupByField::getName)
        .collect(Collectors.toList());
  }
}
