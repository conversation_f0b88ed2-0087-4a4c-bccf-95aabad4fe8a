package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.aggregation.dimension.DateCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyBooleanCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyTextCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberCustomFieldFilterDimension;
import com.sling.sales.report.core.domain.call.Call;
import com.sling.sales.report.core.domain.call.CallCustomCheckboxValue;
import com.sling.sales.report.core.domain.call.CallCustomCheckboxValue_;
import com.sling.sales.report.core.domain.call.CallCustomDatePickerValue;
import com.sling.sales.report.core.domain.call.CallCustomDatePickerValue_;
import com.sling.sales.report.core.domain.call.CallCustomDatetimePickerValue;
import com.sling.sales.report.core.domain.call.CallCustomDatetimePickerValue_;
import com.sling.sales.report.core.domain.call.CallCustomNumberValue;
import com.sling.sales.report.core.domain.call.CallCustomNumberValue_;
import com.sling.sales.report.core.domain.call.CallCustomPicklistValue;
import com.sling.sales.report.core.domain.call.CallCustomPicklistValue_;
import com.sling.sales.report.core.domain.call.CallCustomTextValue;
import com.sling.sales.report.core.domain.call.CallCustomTextValue_;
import com.sling.sales.report.core.domain.call.Call_;
import com.sling.sales.report.dto.DimensionDetail;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class CallCustomFieldFactory {

  public static DimensionDetail<Call> createManyIdCustomFieldDimension(String dimensionName, long fieldId) {
    ManyIdCustomFieldDimension<Call, CallCustomPicklistValue> manyIdCustomFieldDimension = new ManyIdCustomFieldDimension<>(
        dimensionName,
        Call_.CALL_PICKLIST_VALUES,
        Call_.callPicklistValues,
        join -> join.get(CallCustomPicklistValue_.picklistValueId),
        join -> join.get(CallCustomPicklistValue_.displayName),
        join -> join.get(CallCustomPicklistValue_.fieldId),
        fieldId, CallCustomPicklistValue_.DISPLAY_NAME);

    List<Metric<Call>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Call_.callPicklistValues, join -> join.get(CallCustomPicklistValue_.fieldId),
              CallCustomPicklistValue_.PICKLIST_VALUE_ID, fieldId),
          null));
    }};

    return new DimensionDetail<>(manyIdCustomFieldDimension, manyIdCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Call> createManyTextCustomFieldDimension(String dimensionName, long fieldId) {
    ManyTextCustomFieldDimension<Call, CallCustomTextValue> manyTextCustomFieldDimension = new ManyTextCustomFieldDimension<>(
        dimensionName,
        Call_.CALL_CUSTOM_TEXT_VALUES,
        Call_.callCustomTextValues,
        join -> join.get(CallCustomTextValue_.value),
        join -> join.get(CallCustomTextValue_.fieldId),
        fieldId
    );

    List<Metric<Call>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Call_.callCustomTextValues, join -> join.get(CallCustomTextValue_.fieldId),
              CallCustomTextValue_.VALUE, fieldId),
          null));
    }};

    return new DimensionDetail<>(manyTextCustomFieldDimension, manyTextCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Call> createNumberCustomFieldFilterDimension(String dimensionName, long fieldId) {
    NumberCustomFieldFilterDimension<Call, CallCustomNumberValue> numberCustomFieldFilterDimension = new NumberCustomFieldFilterDimension<>(
        dimensionName,
        Call_.CALL_CUSTOM_NUMBER_VALUES,
        Call_.callCustomNumberValues,
        join -> join.get(CallCustomNumberValue_.value),
        join -> join.get(CallCustomNumberValue_.fieldId), fieldId);

    List<Metric<Call>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Call_.callCustomNumberValues, join -> join.get(CallCustomNumberValue_.fieldId),
              CallCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.SUM,
          getBiFunction(MetricType.SUM, Call_.callCustomNumberValues, join -> join.get(CallCustomNumberValue_.fieldId),
              CallCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.AVERAGE,
          getBiFunction(MetricType.AVERAGE, Call_.callCustomNumberValues, join -> join.get(CallCustomNumberValue_.fieldId),
              CallCustomNumberValue_.VALUE, fieldId),
          null));
    }};

    return new DimensionDetail<>(null, numberCustomFieldFilterDimension, metrics);
  }

  public static DimensionDetail<Call> createDatePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Call, CallCustomDatePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Call_.CALL_CUSTOM_DATE_PICKER_VALUES,
        Call_.callCustomDatePickerValues,
        join -> join.get(CallCustomDatePickerValue_.value),
        join -> join.get(CallCustomDatePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Call> createDatetimePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Call, CallCustomDatetimePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Call_.CALL_CUSTOM_DATETIME_PICKER_VALUES,
        Call_.callCustomDatetimePickerValues,
        join -> join.get(CallCustomDatetimePickerValue_.value),
        join -> join.get(CallCustomDatetimePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  private static <T, V> BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>> getBiFunction(MetricType metricType,
      SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> getFieldId, String attributeName, long fieldId) {

    return (root, builder) -> CustomFieldMetricBuilder
        .buildExpressionByMetricType(metricType, root, builder, dimensionField, getFieldId,
            attributeName, fieldId);
  }

  public static DimensionDetail<Call> createBooleanCustomFieldDimension(String dimensionName, long fieldId) {
    ManyBooleanCustomFieldDimension<Call, CallCustomCheckboxValue> manyBooleanCustomFieldDimension = new ManyBooleanCustomFieldDimension<>(
        dimensionName,
        Call_.CALL_CUSTOM_CHECKBOX_VALUES,
        Call_.callCustomCheckboxValues,
        join -> join.get(CallCustomCheckboxValue_.value),
        join -> join.get(CallCustomCheckboxValue_.fieldId),
        fieldId
    );
    return new DimensionDetail<>(manyBooleanCustomFieldDimension, manyBooleanCustomFieldDimension, Collections.emptyList());
  }
}
