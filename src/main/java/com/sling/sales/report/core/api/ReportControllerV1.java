package com.sling.sales.report.core.api;

import static com.sling.sales.report.core.api.RequestResponseMapper.toAggregateRecordsV1;
import static com.sling.sales.report.core.api.RequestResponseMapper.toReportResponseV1;
import static com.sling.sales.report.core.api.RequestResponseMapper.toReportSummariesV1;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.http.ResponseEntity.ok;

import com.sling.sales.report.core.api.request.AggregationRequestV3;
import com.sling.sales.report.core.api.request.FilterRequest;
import com.sling.sales.report.core.api.request.FilterRequest.Filter;
import com.sling.sales.report.core.api.response.AggregateRecord;
import com.sling.sales.report.core.api.response.AggregateRecordV1;
import com.sling.sales.report.core.api.response.ReportResponse;
import com.sling.sales.report.core.api.response.ReportSearchResponse;
import com.sling.sales.report.core.api.response.ReportSummaries;
import com.sling.sales.report.core.api.response.ReportSummariesV1;
import com.sling.sales.report.core.domain.report.ReportType;
import com.sling.sales.report.dto.DownloadDetail;
import com.sling.sales.report.security.domain.UserService;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/v1/reports")
@Slf4j
public class ReportControllerV1 {

  private final ReportService reportService;
  private final UserService userService;

  @Autowired
  public ReportControllerV1(ReportService reportService, UserService userService) {
    this.reportService = reportService;
    this.userService = userService;
  }

  @ApiOperation(value = "Get existing report", response = ReportResponse.class)
  @GetMapping(
      value = "/{id}",
      produces = APPLICATION_JSON_VALUE)
  public ResponseEntity getReport(@PathVariable("id") long reportId) {
    return ok(toReportResponseV1(reportService.getReport(reportId)));
  }

  @ApiOperation(value = "Search reports", response = ReportResponse.class)
  @GetMapping(
      value = "/search",
      produces = APPLICATION_JSON_VALUE)
  public Page<ReportSearchResponse> search(@RequestParam(value = "reportType", required = false) Optional<ReportType> reportType,
      Pageable pageable) {
    if (reportType.isPresent()) {
      FilterRequest.Filter reportTypeFilter = new Filter("equal", "reportType", "string", reportType.get());
      FilterRequest filterRequest = new FilterRequest(Collections.singletonList(reportTypeFilter));
      return reportService.search(Optional.of(filterRequest), pageable
      );
    }
    return reportService.search(Optional.empty(), pageable
    );
  }

  @ApiOperation(value = "Search reports", response = ReportResponse.class)
  @PostMapping(
      value = "/search",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public Page<ReportSearchResponse> search(@RequestParam(value = "reportType", required = false) Optional<ReportType> reportType,
      @RequestBody(required = false) FilterRequest filterRequest,
      Pageable pageable) {
    return reportService.search(Optional.ofNullable(filterRequest), pageable
    );
  }

  @DeleteMapping(value = "/{id}")
  public void deleteReport(@PathVariable("id") long reportId) {
    reportService.deleteReport(reportId);
  }

  @ApiOperation(value = "Get lead by", response = AggregateRecord.class)
  @PostMapping(
      value = "leads",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public ResponseEntity getLeadReport(@RequestBody AggregationRequestV3 aggregationRequestV3,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start = System.currentTimeMillis();
    List<AggregateRecordV1> aggregateRecordV1List = toAggregateRecordsV1(
        reportService.aggregateResultsForLead(aggregationRequestV3, timezone, currencyId));
    log.info("v1 leads report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequestV3.toString());
    return ok(aggregateRecordV1List);
  }

  @ApiOperation(value = "Get deal by", response = AggregateRecord.class)
  @PostMapping(
      value = "deals",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public ResponseEntity getDealReport(@RequestBody AggregationRequestV3 aggregationRequestV3,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start= System.currentTimeMillis();
    List<AggregateRecordV1> aggregateRecordV1List = toAggregateRecordsV1(
        reportService.aggregateResultsForDeal(aggregationRequestV3, timezone, currencyId));
    log.info("v1 deals report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequestV3.toString());
    return ok(aggregateRecordV1List);
  }

  @ApiOperation(value = "Get calls by", response = AggregateRecord.class)
  @PostMapping(value = "calls", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public ResponseEntity getCallReport(
      @RequestBody AggregationRequestV3 aggregationRequestV3,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start= System.currentTimeMillis();
    List<AggregateRecordV1> aggregateRecordV1List = toAggregateRecordsV1(
        reportService.aggregateResultsForCall(aggregationRequestV3, timezone, currencyId));
    log.info("v1 calls report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequestV3.toString());
    return ok(aggregateRecordV1List);
  }

  @ApiOperation(value = "Get tasks by", response = List.class)
  @PostMapping(value = "/tasks", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<List<AggregateRecordV1>> getTaskReport(@RequestBody AggregationRequestV3 aggregationRequestV3,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start= System.currentTimeMillis();
    List<AggregateRecordV1> aggregateRecordV1List = toAggregateRecordsV1(
        reportService.aggregateResultsForTask(aggregationRequestV3, timezone, currencyId));
    log.info("v1 tasks report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequestV3.toString());
    return ok(aggregateRecordV1List);
  }

  @ApiOperation(value = "Get company aggregated records", response = List.class)
  @PostMapping(value = "/companies", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<List<AggregateRecordV1>> getCompanyReport(@RequestBody AggregationRequestV3 aggregationRequestV3,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId") Long baseCurrencyId, @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start= System.currentTimeMillis();
    var aggregateRecordV1List = toAggregateRecordsV1(
        reportService.aggregateResultsForCompany(aggregationRequestV3, timezone, baseCurrencyId, currencyId));
    log.info("v1 companies report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequestV3.toString());
    return ok(aggregateRecordV1List);
  }

  @ApiOperation(value = "Get meeting aggregated records", response = List.class)
  @PostMapping(value = "/meetings", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<List<AggregateRecordV1>> getMeetingReport(@RequestBody AggregationRequestV3 aggregationRequestV3,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    long start= System.currentTimeMillis();
    var aggregateRecordV1List = toAggregateRecordsV1(
        reportService.aggregateResultsForMeeting(aggregationRequestV3, timezone, baseCurrencyId, currencyId));
    log.info("v1 meetings report get took {} for tenantId {} with payload {}", (System.currentTimeMillis() - start)/1000d,userService.getTenantId(),aggregationRequestV3.toString());
    return ok(aggregateRecordV1List);
  }

  @ApiOperation(value = "Get leads for given report ids")
  @GetMapping(
      value = "leads/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummariesV1 getSummaryForLeads(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    ReportSummaries reportSummariesMono = reportService.aggregateResultsForReportType(ReportType.LEAD, ids, timezone, null, currencyId);
    return toReportSummariesV1(reportSummariesMono);
  }

  @ApiOperation(value = "Get deals for given report ids")
  @GetMapping(
      value = "deals/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummariesV1 getSummaryForDeals(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId") Long baseCurrencyId,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    ReportSummaries reportSummariesMono = reportService.aggregateResultsForReportType(ReportType.DEAL, ids, timezone, baseCurrencyId,
        currencyId);
    return toReportSummariesV1(reportSummariesMono);
  }

  @ApiOperation(value = "Get tasks for given report ids")
  @GetMapping(
      value = "tasks/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummariesV1 getSummaryForTasks(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    ReportSummaries reportSummariesMono = reportService.aggregateResultsForReportType(ReportType.TASK, ids, timezone, null, currencyId);
    return toReportSummariesV1(reportSummariesMono);
  }

  @ApiOperation(value = "Get calls for given report ids", response = AggregateRecord.class)
  @GetMapping(
      value = "calls/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummariesV1 getSummaryForCalls(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    ReportSummaries reportSummariesMono = reportService.aggregateResultsForReportType(ReportType.CALL, ids, timezone, null, currencyId);
    return toReportSummariesV1(reportSummariesMono);
  }

  @ApiOperation(value = "Get companies for given report ids", response = AggregateRecord.class)
  @GetMapping(
      value = "companies/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummariesV1 getSummaryForCompanies(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId") Long baseCurrencyId, @RequestParam(value = "currencyId", required = false) Long currencyId) {
    ReportSummaries reportSummariesMono = reportService.aggregateResultsForReportType(ReportType.COMPANY, ids, timezone, baseCurrencyId,
        currencyId);
    return toReportSummariesV1(reportSummariesMono);
  }

  @ApiOperation(value = "Get meetings for given report ids", response = AggregateRecord.class)
  @GetMapping(
      value = "meetings/summary",
      produces = APPLICATION_JSON_VALUE)
  public ReportSummariesV1 getSummaryForMeetings(
      @RequestParam(value = "id") List<Long> ids,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Kolkata") String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    ReportSummaries reportSummariesMono = reportService.aggregateResultsForReportType(ReportType.MEETING, ids, timezone, baseCurrencyId,
        currencyId);
    return toReportSummariesV1(reportSummariesMono);
  }

  @ApiOperation("download report by id")
  @GetMapping(value = "/{id}/download")
  public ResponseEntity<Resource> downloadReport(@PathVariable("id") long id,
      @RequestParam(value = "timezone", required = false, defaultValue = "Asia/Calcutta") String timezone,
      @RequestParam(value = "baseCurrencyId", required = false) Long baseCurrencyId, @RequestParam("baseCurrency") String baseCurrency,
      @RequestParam(value = "currencyId", required = false) Long currencyId) {
    DownloadDetail downloadDetail = reportService.downloadReport(id, timezone, baseCurrency, currencyId);
    HttpHeaders headers = new HttpHeaders();
    headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; " + downloadDetail.getFileName() + ".csv");
    headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-Disposition");
    return ResponseEntity.ok().headers(headers).body(downloadDetail.getResource());
  }
}
