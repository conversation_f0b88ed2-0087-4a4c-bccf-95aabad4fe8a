package com.sling.sales.report.core.domain.contact;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.api.response.AggregateRecord;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.fact.FactAccessSpecifications;
import com.sling.sales.report.core.domain.aggregation.fact.FactAggregationFacade;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ContactAggregationFacade {

  private final FactAggregationFacade<Contact> factAggregationFacade;

  @Autowired
  public ContactAggregationFacade(
      FactAggregationFacade<Contact> factAggregationFacade) {
    this.factAggregationFacade = factAggregationFacade;
  }

  private static final FactAccessSpecifications<Contact> accessSpecifications = new FactAccessSpecifications<>(
      root -> root.get(Contact_.tenantId),
      root -> root.get(Contact_.id),
      root -> root.get(Contact_.ownedBy),
      root -> root.get(Contact_.deleted),
      null);

  public List<AggregateRecord> getAggregatedResults(Map<String, GroupByDimension<Contact>> groupByDimensions, List<FactFilter<Contact>> factFilters,
      List<GroupByField> groupBy,
      List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<Contact>> customFieldMetrics, Long baseCurrencyId,
      String timezone, Long currencyId) {
    return factAggregationFacade
        .getAggregatedResults(groupByDimensions, factFilters, groupBy, metrics, customFieldMetrics, Contact.class, accessSpecifications,
            timezone, currencyId);
  }

  public List<AggregateRecord> getAggregationResult(List<FactFilter<Contact>> leadFactFilters, List<GroupByDimension<Contact>> groupByDimensions,
      List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<Contact>> customFieldMetrics, String timezone,
      Long currencyId) {
    return factAggregationFacade
        .getAggregationResult(leadFactFilters, groupByDimensions, metrics, customFieldMetrics, accessSpecifications, Contact.class, timezone,
            currencyId);
  }
}
