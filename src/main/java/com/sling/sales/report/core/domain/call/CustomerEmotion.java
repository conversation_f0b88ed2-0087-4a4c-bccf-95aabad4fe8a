package com.sling.sales.report.core.domain.call;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Entity
@Getter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class CustomerEmotion implements EntityDimension {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;
  private Long customerEmotionId;
  private String name;
  private long tenantId;

  @Column(name = "call_id")
  private Long callId;

  public CustomerEmotion(Long customerEmotionId, String name, long tenantId, Long callId) {
    this.customerEmotionId = customerEmotionId;
    this.name = name;
    this.tenantId = tenantId;
    this.callId = callId;
  }

}
