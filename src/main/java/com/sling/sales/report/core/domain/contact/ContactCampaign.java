package com.sling.sales.report.core.domain.contact;

import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import javax.persistence.Entity;
import javax.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class ContactCampaign implements EntityDimension {

  @Id
  private long id;
  private String name;
  private long tenantId;

}