package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.aggregation.exception.InvalidFactForReporting;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.call.Call;
import com.sling.sales.report.core.domain.company.Company;
import com.sling.sales.report.core.domain.contact.Contact;
import com.sling.sales.report.core.domain.deal.Deal;
import com.sling.sales.report.core.domain.email.Email;
import com.sling.sales.report.core.domain.lead.Lead;
import com.sling.sales.report.core.domain.meeting.Meeting;
import com.sling.sales.report.core.domain.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AbstractFactFieldFactory {

  private final LeadFieldFactory leadFieldFactory;
  private final DealFieldFactory dealFieldFactory;
  private final CallFieldFactory callFieldFactory;
  private final TaskFieldFactory taskFieldFactory;
  private final CompanyFieldFactory companyFieldFactory;
  private final MeetingFieldFactory meetingFieldFactory;
  private final ContactFieldFactory contactFieldFactory;
  private final EmailFieldFactory emailFieldFactory;

  @Autowired
  public AbstractFactFieldFactory(LeadFieldFactory leadFieldFactory,
      DealFieldFactory dealFieldFactory, CallFieldFactory callFieldFactory, TaskFieldFactory taskFieldFactory,
      CompanyFieldFactory companyFieldFactory, MeetingFieldFactory meetingFieldFactory,
      ContactFieldFactory contactFieldFactory, EmailFieldFactory emailFieldFactory) {
    this.leadFieldFactory = leadFieldFactory;
    this.dealFieldFactory = dealFieldFactory;
    this.callFieldFactory = callFieldFactory;
    this.taskFieldFactory = taskFieldFactory;
    this.companyFieldFactory = companyFieldFactory;
    this.meetingFieldFactory = meetingFieldFactory;
    this.contactFieldFactory = contactFieldFactory;
    this.emailFieldFactory = emailFieldFactory;
  }

  public <T extends Fact> FactFieldFactory<T> getFieldFactory(Class<T> factClass) {
    if (factClass.equals(Lead.class)) {
      return (FactFieldFactory<T>) leadFieldFactory;
    }
    if (factClass.equals(Deal.class)) {
      return (FactFieldFactory<T>) dealFieldFactory;
    }
    if (factClass.equals(Call.class)) {
      return (FactFieldFactory<T>) callFieldFactory;
    }
    if (factClass.equals(Task.class)) {
      return (FactFieldFactory<T>) taskFieldFactory;
    }
    if (factClass.equals(Company.class)) {
      return (FactFieldFactory<T>) companyFieldFactory;
    }
    if (factClass.equals(Meeting.class)) {
      return (FactFieldFactory<T>) meetingFieldFactory;
    }
    if (factClass.equals(Contact.class)) {
      return (FactFieldFactory<T>) contactFieldFactory;
    }
    if (factClass.equals(Email.class)) {
      return (FactFieldFactory<T>) emailFieldFactory;
    }
    throw new InvalidFactForReporting();
  }
}
