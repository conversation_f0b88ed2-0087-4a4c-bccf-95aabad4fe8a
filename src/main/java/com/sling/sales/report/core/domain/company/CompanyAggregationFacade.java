package com.sling.sales.report.core.domain.company;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.api.response.AggregateRecord;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Operator;
import com.sling.sales.report.core.domain.aggregation.fact.FactAccessSpecifications;
import com.sling.sales.report.core.domain.aggregation.fact.FactAggregationFacade;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import com.sling.sales.report.mq.event.Revenue_;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CompanyAggregationFacade {

  private final FactAggregationFacade<Company> factAggregationFacade;

  @Autowired
  public CompanyAggregationFacade(
      FactAggregationFacade<Company> factAggregationFacade) {
    this.factAggregationFacade = factAggregationFacade;
  }

  private static final FactAccessSpecifications<Company> accessSpecifications = new FactAccessSpecifications<>(
      root -> root.get(Company_.tenantId),
      root -> root.get(Company_.id),
      root -> root.get(Company_.ownedBy),
      root -> root.get(Company_.deleted),
      null);

  public List<AggregateRecord> getAggregatedResults(Map<String, GroupByDimension<Company>> groupByDimensions, List<FactFilter<Company>> factFilters,
      List<GroupByField> groupBy,
      List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<Company>> customFieldMetrics, Long baseCurrencyId,
      String timezone, Long currencyId) {
    List<FactFilter<Company>> appliedFactFilters = new ArrayList<>(factFilters);
    FactFilter<Company> baseCurrencyIdFilter = createBaseCurrencyFilter(baseCurrencyId, metrics, factFilters);
    if (Objects.nonNull(baseCurrencyIdFilter)) {
      appliedFactFilters.add(baseCurrencyIdFilter);
    }
    return factAggregationFacade
        .getAggregatedResults(groupByDimensions, appliedFactFilters, groupBy, metrics, customFieldMetrics, Company.class, accessSpecifications,
            timezone, currencyId);
  }

  private FactFilter<Company> createBaseCurrencyFilter(Long baseCurrencyId, List<Metric> metrics, List<FactFilter<Company>> factFilters) {

    Optional<FactFilter<Company>> moneyFactFilter = factFilters.stream()
        .filter(factFilter -> {
          FilterDimension<Company> filterDimension = factFilter.getDimension();
          return filterDimension.getName().equals(Company_.ANNUAL_REVENUE);
        })
        .findFirst();

    if (moneyFactFilter.isPresent()) {
      return moneyFactFilter.map(factFilter -> toBaseCurrencyFilter(baseCurrencyId))
          .orElse(null);
    }

    return metrics
        .stream()
        .filter(this::isAnnualRevenue)
        .findFirst()
        .map(metric -> toBaseCurrencyFilter(baseCurrencyId))
        .orElse(null);
  }

  private boolean isAnnualRevenue(Metric metric) {
    return (metric.getType() == MetricType.SUM || metric.getType() == MetricType.AVERAGE)
        && metric.getField().equals(Company_.ANNUAL_REVENUE);
  }

  private FactFilter<Company> toBaseCurrencyFilter(Long baseCurrencyId) {
    var moneyAttribute = Company_.annualRevenue;
    return new FactFilter<>(
        Operator.equal,
        new NumberFilterDimension<>(
            "baseCurrencyId",
            root -> root.get(moneyAttribute).get(Revenue_.currencyId),
            Long::valueOf),
        new Filter("equal", "baseCurrencyId", "baseCurrencyId", "long", baseCurrencyId, null, null, "NUMBER", null, null),
        "long"
    );
  }

  public List<AggregateRecord> getAggregationResult(List<FactFilter<Company>> leadFactFilters, List<GroupByDimension<Company>> groupByDimensions,
      List<Metric> metrics, List<com.sling.sales.report.core.domain.aggregation.dimension.Metric<Company>> customFieldMetrics,
      String timezone, Long currencyId) {
    return factAggregationFacade
        .getAggregationResult(leadFactFilters, groupByDimensions, metrics, customFieldMetrics, accessSpecifications, Company.class, timezone,
            currencyId);
  }
}
