package com.sling.sales.report.core.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoalCount {

  private final Number achieved;
  private final Number goal;
  private final Double percentage;

  @JsonCreator
  public GoalCount(@JsonProperty("achieved") Number achieved, @JsonProperty("goal") Number goal, @JsonProperty("percentage") Double percentage) {
    this.achieved = achieved instanceof Double ? BigDecimal.valueOf(achieved.doubleValue()).setScale(4, RoundingMode.HALF_EVEN) : achieved;
    this.goal = goal instanceof Double ? BigDecimal.valueOf(goal.doubleValue()).setScale(4, RoundingMode.HALF_EVEN) : goal;
    this.percentage = BigDecimal.valueOf(percentage).setScale(4, RoundingMode.HALF_EVEN).doubleValue();
  }
}
