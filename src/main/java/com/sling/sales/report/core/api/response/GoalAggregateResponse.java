package com.sling.sales.report.core.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoalAggregateResponse {

  private final List<GoalAggregateRecord> result;
  private final GoalCountSummary goalSummary;

  @JsonCreator
  public GoalAggregateResponse(@JsonProperty("result") List<GoalAggregateRecord> result, @JsonProperty("goalSummary") GoalCountSummary goalSummary) {
    this.result = result;
    this.goalSummary = goalSummary;
  }

  public GoalAggregateResponse withSortedAggregateRecords() {
    this.getResult()
        .sort(Collections.reverseOrder(this::compare));
    return this;
  }

  private int compare(GoalAggregateRecord goalAggregateRecordOne, GoalAggregateRecord goalAggregateRecordTwo) {
    double aggregateRecordOneTotal = getMaxValue(goalAggregateRecordOne).doubleValue();
    double aggregateRecordTwoTotal = getMaxValue(goalAggregateRecordTwo).doubleValue();

    return Double.compare(aggregateRecordOneTotal, aggregateRecordTwoTotal);
  }

  private Number getMaxValue(GoalAggregateRecord goalAggregateRecord) {
    return goalAggregateRecord.getValue()
        .stream()
        .map(GoalCount::getAchieved)
        .max(Comparator.comparing(Number::doubleValue))
        .orElse(0.0);
  }

}
