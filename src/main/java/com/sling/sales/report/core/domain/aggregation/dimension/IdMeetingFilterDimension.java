package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.in;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_in;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import com.sling.sales.report.mq.event.MeetingLookup;
import java.lang.reflect.Type;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SingularAttribute;
import org.springframework.data.jpa.domain.Specification;

public class IdMeetingFilterDimension<T extends Fact, V extends EntityDimension> implements FilterDimension<T> {

  private final List<Operator> allowedOperators = asList(equal, not_equal, is_null, is_not_null, in, not_in);
  private final String name;
  private final String dimensionName;
  private final SingularAttribute<T, V> dimensionField;
  private final Function<Join<T, V>, Path<Long>> getId;
  private final Function<Join<T, V>, Path<String>> getEntity;

  public IdMeetingFilterDimension(
      String name,
      String dimensionName,
      SingularAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> getId,
      Function<Join<T, V>, Path<String>> getEntity) {
    this.name = name;
    this.dimensionName = dimensionName;
    this.dimensionField = dimensionField;
    this.getId = getId;
    this.getEntity = getEntity;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    switch (operator) {
      case equal:
        return isEntityIdAndEntityEqualTo(value);
      case not_equal:
        return isEntityIdAndEntityNotEqualTo(value).or(isNull());
      case is_null:
        return isNull();
      case is_not_null:
        return isNotNull();
      case in:
        return inOrNotInEntityIdsAndEntities(value, in);
      case not_in:
        return inOrNotInEntityIdsAndEntities(value, not_in).or(isNull());
      default:
        return null;
    }
  }

  private Specification<T> isEntityIdAndEntityEqualTo(Object value) {
    MeetingLookup meetingLookup = new ObjectMapper().convertValue(value, MeetingLookup.class);
    return ((root, query, builder) -> {
      var join = getOrCreateJoin(root);
      return builder.and(builder.equal(getId.apply(join), meetingLookup.getId()), builder.equal(getEntity.apply(join), meetingLookup.getEntity()));
    });
  }

  private Specification<T> isEntityIdAndEntityNotEqualTo(Object value) {
    MeetingLookup meetingLookup = new ObjectMapper().convertValue(value, MeetingLookup.class);
    return ((root, query, builder) -> {
      var join = getOrCreateJoin(root);
      return builder
          .and(builder.equal(getId.apply(join), meetingLookup.getId()), builder.equal(getEntity.apply(join), meetingLookup.getEntity())).not();
    });
  }

  Specification<T> isNull() {
    return ((root, query, builder) -> builder.isNull(getId.apply(getOrCreateJoin(root))));
  }

  Specification<T> isNotNull() {
    return ((root, query, builder) -> builder.isNotNull(getId.apply(getOrCreateJoin(root))));
  }

  private Specification<T> inOrNotInEntityIdsAndEntities(Object value, Operator operator) {
    List<MeetingLookup> meetingLookups = new ObjectMapper().convertValue(value, new TypeReference<>() {
      @Override
      public Type getType() {
        return super.getType();
      }
    });

    List<Long> entityIds = meetingLookups.
        stream()
        .map(MeetingLookup::getId)
        .collect(Collectors.toList());

    List<String> entities = meetingLookups.
        stream()
        .map(MeetingLookup::getEntity)
        .collect(Collectors.toList());

    return (root, query, builder) -> {
      var join = getOrCreateJoin(root);
      Expression<Long> idExpressions = getId.apply(join);
      Expression<String> entityExpressions = getEntity.apply(join);
      if (Operator.in == operator) {
        return builder.and(idExpressions.in(entityIds), entityExpressions.in(entities));
      }
      return builder.and(idExpressions.in(entityIds), entityExpressions.in(entities)).not();
    };
  }

  protected Join<T, V> getOrCreateJoin(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<T, V>) j))
        .findFirst()
        .orElseGet(() -> root.join(dimensionField, JoinType.LEFT));
  }
}
