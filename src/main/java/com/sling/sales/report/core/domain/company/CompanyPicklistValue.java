package com.sling.sales.report.core.domain.company;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor
public class CompanyPicklistValue implements EntityDimension {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private long id;
  @Column(name = "field_id")
  private long fieldId;
  @Column(name = "picklist_value_id")
  private Long picklistValueId;
  @Column(name = "display_name")
  private String displayName;

  @ManyToOne
  @JsonIgnore
  @JoinColumn(name = "company_id")
  private Company company;

  public CompanyPicklistValue(long fieldId, Long picklistValueId, String displayName, Company company) {
    this.fieldId = fieldId;
    this.picklistValueId = picklistValueId;
    this.displayName = displayName;
    this.company = company;
  }
}
