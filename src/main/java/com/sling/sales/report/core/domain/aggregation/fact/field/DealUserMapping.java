package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.deal.Deal;
import com.sling.sales.report.security.domain.User;
import javax.persistence.metamodel.SingularAttribute;
import lombok.Getter;

@Getter
public class DealUserMapping {

  private final String mappedColumnFieldName;
  private final SingularAttribute<Deal, User> mappedColumnFieldPath;

  public DealUserMapping(String mappedColumnFieldName, SingularAttribute<Deal, User> mappedColumnFieldPath) {
    this.mappedColumnFieldName = mappedColumnFieldName;
    this.mappedColumnFieldPath = mappedColumnFieldPath;
  }
}
