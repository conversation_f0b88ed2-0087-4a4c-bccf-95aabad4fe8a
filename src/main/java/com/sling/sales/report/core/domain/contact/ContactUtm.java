package com.sling.sales.report.core.domain.contact;

import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor
public class ContactUtm {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;
  private String subSource;
  private String utmSource;
  private String utmMedium;
  private String utmCampaign;
  private String utmTerm;
  private String utmContent;

  @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "contact_id", referencedColumnName = "id")
  @JsonIgnore
  private Contact contact;

  private ContactUtm(String subSource, String utmSource, String utmMedium, String utmCampaign, String utmTerm, String utmContent, Contact contact) {
    this.subSource = subSource;
    this.utmSource = utmSource;
    this.utmMedium = utmMedium;
    this.utmCampaign = utmCampaign;
    this.utmTerm = utmTerm;
    this.utmContent = utmContent;
    this.contact = contact;
  }

  public static ContactUtm createContactUtm(String subSource, String utmSource, String utmMedium, String utmCampaign, String utmTerm,
      String utmContent,
      Contact contact) {
    return new ContactUtm(subSource, utmSource, utmMedium, utmCampaign, utmTerm, utmContent, contact);
  }

  public ContactUtm updateContactUtm(String subSource, String utmSource, String utmMedium, String utmCampaign, String utmTerm, String utmContent) {
    this.subSource = subSource;
    this.utmSource = utmSource;
    this.utmMedium = utmMedium;
    this.utmCampaign = utmCampaign;
    this.utmTerm = utmTerm;
    this.utmContent = utmContent;
    return this;
  }
}
