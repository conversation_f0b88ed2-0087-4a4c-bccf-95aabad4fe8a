package com.sling.sales.report.core.api.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.ToString;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class GroupByField {

  @NotBlank
  private final String name;
  private final Format format;
  private final String primaryField;
  private final String property;

  @JsonCreator
  public GroupByField(@JsonProperty("name") String name, @JsonProperty("format") Format format,
      @JsonProperty("primaryField") String primaryField, @JsonProperty("property") String property) {
    this.name = name;
    this.format = format;
    this.primaryField = primaryField;
    this.property = property;
  }
}
