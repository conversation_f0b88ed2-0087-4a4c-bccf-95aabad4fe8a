package com.sling.sales.report.core.api;

import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Collections.emptySet;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.api.request.AggregationRequestV3;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.CreateReportRequest;
import com.sling.sales.report.core.api.request.FilterRequest;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.api.request.ReportCategory;
import com.sling.sales.report.core.api.request.UpdateReportRequest;
import com.sling.sales.report.core.api.response.Action;
import com.sling.sales.report.core.api.response.AggregateRecord;
import com.sling.sales.report.core.api.response.GoalAggregateResponse;
import com.sling.sales.report.core.api.response.IdName;
import com.sling.sales.report.core.api.response.ReportFilter;
import com.sling.sales.report.core.api.response.ReportGoalResponse;
import com.sling.sales.report.core.api.response.ReportResponse;
import com.sling.sales.report.core.api.response.ReportSearchResponse;
import com.sling.sales.report.core.api.response.ReportSummaries;
import com.sling.sales.report.core.api.response.ReportSummaries.ReportWiseSummary;
import com.sling.sales.report.core.api.response.ReportSummary;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.GoalDetail;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Operator;
import com.sling.sales.report.core.domain.aggregation.exception.InvalidFactForReporting;
import com.sling.sales.report.core.domain.aggregation.exception.UnsupportedFilterException;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.AbstractFactFieldFactory;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFieldFactory;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import com.sling.sales.report.core.domain.call.Call;
import com.sling.sales.report.core.domain.call.CallAggregationFacade;
import com.sling.sales.report.core.domain.company.Company;
import com.sling.sales.report.core.domain.company.CompanyAggregationFacade;
import com.sling.sales.report.core.domain.contact.Contact;
import com.sling.sales.report.core.domain.contact.ContactAggregationFacade;
import com.sling.sales.report.core.domain.deal.Deal;
import com.sling.sales.report.core.domain.deal.DealAggregationFacade;
import com.sling.sales.report.core.domain.email.Email;
import com.sling.sales.report.core.domain.email.EmailAggregationFacade;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.core.domain.goal.Frequency;
import com.sling.sales.report.core.domain.goal.Goal;
import com.sling.sales.report.core.domain.lead.Lead;
import com.sling.sales.report.core.domain.lead.LeadAggregationFacade;
import com.sling.sales.report.core.domain.meeting.Meeting;
import com.sling.sales.report.core.domain.meeting.MeetingAggregationFacade;
import com.sling.sales.report.core.domain.report.Currency;
import com.sling.sales.report.core.domain.report.CurrencyFacade;
import com.sling.sales.report.core.domain.report.Report;
import com.sling.sales.report.core.domain.report.ReportFacade;
import com.sling.sales.report.core.domain.report.ReportType;
import com.sling.sales.report.core.domain.report.exception.InvalidReportRequestException;
import com.sling.sales.report.core.domain.service.CustomFieldService;
import com.sling.sales.report.core.domain.task.Task;
import com.sling.sales.report.core.domain.task.TaskAggregationFacade;
import com.sling.sales.report.dto.DimensionDetail;
import com.sling.sales.report.dto.DownloadDetail;
import com.sling.sales.report.dto.GoalFilter;
import com.sling.sales.report.dto.GoalFrequency;
import com.sling.sales.report.dto.HeaderDetail;
import com.sling.sales.report.error.ErrorCode;
import com.sling.sales.report.error.InvalidPipelineOperatorException;
import com.sling.sales.report.mq.RefreshListLayoutCacheCommandPublisher;
import com.sling.sales.report.mq.ReportEventPublisher;
import com.sling.sales.report.mq.event.ReportUpdatedEvent;
import com.sling.sales.report.security.domain.HierarchyAggregateRecord;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.UserService;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class ReportService {

  private final ReportFacade reportFacade;
  private final UserService userService;
  private final AbstractFactFieldFactory abstractFactFieldFactory;
  private final LeadAggregationFacade leadAggregationFacade;
  private final DealAggregationFacade dealAggregationFacade;
  private final CallAggregationFacade callAggregationFacade;
  private final TaskAggregationFacade taskAggregationFacade;
  private final CompanyAggregationFacade companyAggregationFacade;
  private final MeetingAggregationFacade meetingAggregationFacade;
  private final ReportEventPublisher reportEventPublisher;
  private final CustomFieldService customFieldService;
  private final DownloadReportService downloadReportService;
  private final ReportHeaderBuilderCacheService reportHeaderBuilderCacheService;
  private final ContactAggregationFacade contactAggregationFacade;
  private final EmailAggregationFacade emailAggregationFacade;
  private final RefreshListLayoutCacheCommandPublisher refreshListLayoutCacheCommandPublisher;
  private final CurrencyFacade currencyFacade;

  @Autowired
  public ReportService(ReportFacade reportFacade, UserService userService,
      AbstractFactFieldFactory abstractFactFieldFactory,
      LeadAggregationFacade leadAggregationFacade,
      DealAggregationFacade dealAggregationFacade,
      CallAggregationFacade callAggregationFacade,
      TaskAggregationFacade taskAggregationFacade, CompanyAggregationFacade companyAggregationFacade,
      MeetingAggregationFacade meetingAggregationFacade, ReportEventPublisher reportEventPublisher,
      CustomFieldService customFieldService, DownloadReportService downloadReportService,
      ReportHeaderBuilderCacheService reportHeaderBuilderCacheService, ContactAggregationFacade contactAggregationFacade,
      EmailAggregationFacade emailAggregationFacade,
      RefreshListLayoutCacheCommandPublisher refreshListLayoutCacheCommandPublisher,
      CurrencyFacade currencyFacade) {
    this.reportFacade = reportFacade;
    this.userService = userService;
    this.abstractFactFieldFactory = abstractFactFieldFactory;
    this.leadAggregationFacade = leadAggregationFacade;
    this.dealAggregationFacade = dealAggregationFacade;
    this.callAggregationFacade = callAggregationFacade;
    this.taskAggregationFacade = taskAggregationFacade;
    this.companyAggregationFacade = companyAggregationFacade;
    this.meetingAggregationFacade = meetingAggregationFacade;
    this.reportEventPublisher = reportEventPublisher;
    this.customFieldService = customFieldService;
    this.downloadReportService = downloadReportService;
    this.reportHeaderBuilderCacheService = reportHeaderBuilderCacheService;
    this.contactAggregationFacade = contactAggregationFacade;
    this.emailAggregationFacade = emailAggregationFacade;
    this.refreshListLayoutCacheCommandPublisher = refreshListLayoutCacheCommandPublisher;
    this.currencyFacade = currencyFacade;
  }

  public ReportSummary createReport(CreateReportRequest createReportRequest) {
    validateDuplicateFilters(createReportRequest.getConfig());
    validatePipelineAndPipelineStage(createReportRequest.getConfig());
    Report report = reportFacade.create(createReportRequest);
    return new ReportSummary(report.getId());
  }

  private void validateDuplicateFilters(AggregationRequestV3 config) {
    var dateRange = config.getDateRange();
    var isDuplicateFilterPresent = config.getFilters()
        .stream()
        .anyMatch(filter -> filter.getFieldName().equals(dateRange.getFieldName()));
    if (isDuplicateFilterPresent) {
      throw new InvalidReportRequestException(ErrorCode.INVALID_REQUEST);
    }
  }

  public ReportResponse getReport(long reportId) {
    Report report = reportFacade.getReport(reportId);
    User loggedInUser = userService.getLoggedInUser();
    Action recordActions = getRecordActions(report.getReportType().getEntityClass(), loggedInUser, report.getCreatedBy(),
        report.isSystemDefault());
    Currency currency = report.getCurrency();
    com.sling.sales.report.mq.event.IdName currencyIdName =
        ObjectUtils.isEmpty(currency) ? null : new com.sling.sales.report.mq.event.IdName(currency.getId(), currency.getName());
    return new ReportResponse(
        report.getId(),
        report.getName(),
        report.getDescription(),
        report.getReportType().name().toLowerCase(),
        report.getChartType().name().toLowerCase(),
        new ObjectMapper().convertValue(report.getConfig(), AggregationRequestV3.class), recordActions, report.getCreatedBy(), report.getCategory(),
        toReportGoalResponse(report.getGoal(), loggedInUser), report.isSystemDefault(), report.isProrated(), currencyIdName);
  }

  private ReportGoalResponse toReportGoalResponse(Goal goal, User loggedInUser) {
    if (ObjectUtils.isEmpty(goal)) {
      return null;
    }
    Frequency frequency = goal.getFrequency();
    Action recordActions = getGoalRecordActions(goal.getEntityType().getEntityClass(), loggedInUser, goal.getCreatedBy(), goal.getOwners());

    List<IdName> ownersIdName = Collections.emptyList();
    if (ObjectUtils.isNotEmpty(goal.getOwners())) {
      ownersIdName = goal.getOwners().stream().map(user -> new IdName(user.getId(), user.getName()))
          .collect(toList());
    }
    return new ReportGoalResponse(goal.getId(), goal.getName(),
        new GoalFrequency(frequency.getType().name(), frequency.getStart(), frequency.getEnd(), frequency.getHeader()), goal.isActive(),
        toFilters(goal), recordActions, ownersIdName);
  }

  private List<GoalFilter> toFilters(Goal goal) {
    if (ObjectUtils.isEmpty(goal.getFilters())) {
      return Collections.emptyList();
    }
    return goal.getFilters()
        .stream()
        .map(goalFilter -> new GoalFilter(goalFilter.getOperator(), goalFilter.getField(), goalFilter.getHeader(), goalFilter.getType(),
            goalFilter.getValue(), goalFilter.getFieldType(), goalFilter.getPrimaryField(), goalFilter.getProperty()))
        .collect(Collectors.toList());
  }

  private <T extends Fact> Action getRecordActions(Class<T> entityClass, User loggedInUser, User creator, boolean isSystemDefault) {
    Action recordActions = new Action();
    if (Call.class.equals(entityClass) && loggedInUser.canReadCall() && !loggedInUser.returnEntitiesHavingCallPermission().isEmpty()) {
      getRecordActions(loggedInUser, creator, isSystemDefault, recordActions);
    }
    if (loggedInUser.hasAccessToFacts(entityClass)) {
      getRecordActions(loggedInUser, creator, isSystemDefault, recordActions);
    }
    return recordActions;
  }

  private void getRecordActions(User loggedInUser, User creator, boolean isSystemDefault, Action recordActions) {
    if (loggedInUser.canQueryAllReports() || (loggedInUser.canQueryHisReports() && loggedInUser.getId().equals(creator.getId()))
        || isSystemDefault) {
      recordActions.setRead(true);
    }
    if (loggedInUser.canUpdateAllReports() || (loggedInUser.canUpdateHisReport() && loggedInUser.getId().equals(creator.getId()))) {
      recordActions.setUpdate(true);
    }
  }

  private <T extends Fact> Action getGoalRecordActions(Class<T> entityClass, User loggedInUser, User creator, Set<User> goalOwners) {
    Action recordActions = new Action();
    if (Call.class.equals(entityClass) && loggedInUser.canReadCall() && !loggedInUser.returnEntitiesHavingCallPermission().isEmpty()) {
      getGoalRecordActions(loggedInUser, creator, recordActions, goalOwners);
    }
    if (loggedInUser.hasAccessToFacts(entityClass)) {
      getGoalRecordActions(loggedInUser, creator, recordActions, goalOwners);
    }
    return recordActions;
  }

  private void getGoalRecordActions(User loggedInUser, User creator, Action recordActions, Set<User> goalOwners) {
    if (loggedInUser.canQueryAllGoals() || (loggedInUser.canQueryHisGoals() && isCreatorOrGoalOwner(loggedInUser, creator, goalOwners))) {
      recordActions.setRead(true);
    }
    if (loggedInUser.canUpdateAllGoal() || (loggedInUser.canUpdateHisGoal() && isCreatorOrGoalOwner(loggedInUser, creator, goalOwners))) {
      recordActions.setUpdate(true);
    }
  }

  private boolean isCreatorOrGoalOwner(User loggedInUser, User creator, Set<User> goalOwners) {
    return loggedInUser.getId().equals(creator.getId()) || isGoalOwner(loggedInUser, goalOwners);
  }

  private boolean isGoalOwner(User loggedInUser, Set<User> goalOwners) {
    if (ObjectUtils.isEmpty(goalOwners)) {
      return false;
    }
    Set<Long> goalOwnerIds = goalOwners.stream().map(User::getId).collect(Collectors.toSet());
    return goalOwnerIds.contains(loggedInUser.getId());
  }

  public Mono<ReportResponse> updateReport(long reportId, UpdateReportRequest updateReportRequest) {
    validateDuplicateFilters(updateReportRequest.getConfig());
    validatePipelineAndPipelineStage(updateReportRequest.getConfig());
    User loggedInUser = userService.getLoggedInUser();
    Mono<ReportResponse> updatedReport = reportFacade
        .updateReport(reportId, updateReportRequest)
        .map(report -> {
              Currency currency = report.getCurrency();
              com.sling.sales.report.mq.event.IdName currencyIdName =
                  ObjectUtils.isEmpty(currency) ? null : new com.sling.sales.report.mq.event.IdName(currency.getId(), currency.getName());
              ReportResponse reportResponse = new ReportResponse(

                  report.getId(),
                  report.getName(),
                  report.getDescription(),
                  report.getReportType().name().toLowerCase(),
                  report.getChartType().name().toLowerCase(),
                  new ObjectMapper().convertValue(report.getConfig(), AggregationRequestV3.class),
                  getRecordActions(report.getReportType().getEntityClass(), loggedInUser, report.getCreatedBy(), report.isSystemDefault()),
                  report.getCreatedBy(),
                  ReportCategory.ONE_DIMENSIONAL, toReportGoalResponse(report.getGoal(), loggedInUser), report.isSystemDefault(), report.isProrated(),
                  currencyIdName);
              refreshListLayoutCacheCommandPublisher
                  .publish(loggedInUser.getTenantId(), EntityType.valueOf(updateReportRequest.getReportType().toUpperCase()), report.getId());
              return reportResponse;
            }
        );
    ReportUpdatedEvent reportUpdatedEventPayload = new ReportUpdatedEvent(reportId,
        updateReportRequest.getName(), updateReportRequest.getDescription());
    reportEventPublisher.publishReportUpdatedEvent(reportUpdatedEventPayload);
    return updatedReport;
  }

  private void validatePipelineAndPipelineStage(AggregationRequestV3 aggregationRequest) {
    boolean isInvalidPipeline = aggregationRequest.getFilters()
        .stream()
        .anyMatch(filter -> filter.getFieldName().equals("pipeline") && (filter.getOperator().equals(Operator.in.name()) || filter.getOperator()
            .equals(Operator.not_in.name())));
    boolean isInvalidPipelineStage = aggregationRequest.getFilters()
        .stream()
        .anyMatch(filter -> filter.getFieldName().equals("pipelineStage") && (filter.getOperator().equals(Operator.in.name()) || filter.getOperator()
            .equals(Operator.not_in.name())));
    if (isInvalidPipeline && isInvalidPipelineStage) {
      throw new InvalidPipelineOperatorException();
    }
  }

  public Page<ReportSearchResponse> search(Optional<FilterRequest> filterRequest, Pageable pageable) {
    Page<Report> searchResponse = getSearchResponse(filterRequest, pageable);
    User loggedInUser = userService.getLoggedInUser();
    List<ReportSearchResponse> responseList = searchResponse
        .getContent()
        .stream()
        .map(report -> {
              AggregationRequestV3 aggregationRequest = new ObjectMapper().convertValue(report.getConfig(), AggregationRequestV3.class);
              boolean isMultiMetric = !ObjectUtils.isEmpty(aggregationRequest.getMetrics()) && aggregationRequest.getMetrics().size() > 1;
              return new ReportSearchResponse
                  (report.getId(), report.getName(), report.getDescription(), report.getReportType(), report.getCreatedAt(),
                      new IdName(report.getCreatedBy().getId(), report.getCreatedBy().getName()),
                      getRecordActions(report.getReportType().getEntityClass(), loggedInUser, report.getCreatedBy(), report.isSystemDefault()),
                      report.getCategory(), report.getChartType(), report.getUpdatedAt(),
                      new IdName(report.getUpdatedBy().getId(), report.getUpdatedBy().getName()), report.isSystemDefault(), isMultiMetric);
            }
        ).collect(Collectors.toList());
    return new PageImpl<ReportSearchResponse>(responseList, pageable, searchResponse.getTotalElements());

  }

  private Page<Report> getSearchResponse(Optional<FilterRequest> filterRequest, Pageable pageable) {
    if (filterRequest.isPresent()) {
      Set<ReportFilter> reportFilters = filterRequest.get()
          .getJsonRules()
          .stream()
          .map(jsonRule -> new ReportFilter(jsonRule.getOperator(), jsonRule.getField(), jsonRule.getType(), jsonRule.getValue()))
          .collect(Collectors.toSet());
      return reportFacade.search(reportFilters, pageable);
    }
    return reportFacade.search(emptySet(), pageable);
  }

  public List<AggregateRecord> aggregateResultsForLead(AggregationRequestV3 aggregationRequest, String timezone, Long currencyId) {
    validatePipelineAndPipelineStage(aggregationRequest);
    User loggedInUser = userService.getLoggedInUser();
    DimensionDetail<Lead> dimensionDetail = customFieldService.getLeadDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Lead>> factFilters = createFactFilters(aggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Lead.class);
    List<AggregateRecord> aggregatedResults = leadAggregationFacade
        .getAggregatedResults(dimensionDetail.getGroupByDimensions(), factFilters, aggregationRequest.getGroupBy(),
            aggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone, currencyId);
    if (isHierarchyReport(aggregationRequest.getReportCategory())) {
      log.info("Hierarchy report build for tenantId {} and userId {} with request {}",loggedInUser.getTenantId(),loggedInUser.getId(),aggregationRequest);
      return getAggregatedResultsForHierarchyReport(aggregatedResults);
    }
    return aggregatedResults;
  }

  public List<AggregateRecord> aggregateResultsForDeal(AggregationRequestV3 aggregationRequest, String timezone, Long currencyId) {
    validatePipelineAndPipelineStage(aggregationRequest);
    DimensionDetail<Deal> dimensionDetail = customFieldService.getDealDimensionsForCustomFields(userService.getLoggedInUser());
    List<FactFilter<Deal>> factFilters = createFactFilters(aggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Deal.class);
    List<AggregateRecord> aggregatedResults = dealAggregationFacade
        .getAggregatedResults(dimensionDetail.getGroupByDimensions(), factFilters, aggregationRequest.getGroupBy(),
            aggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone, currencyId);
    if (isHierarchyReport(aggregationRequest.getReportCategory())) {
      return getAggregatedResultsForHierarchyReport(aggregatedResults);
    }
    return aggregatedResults;
  }

  public List<AggregateRecord> aggregateResultsForCall(AggregationRequestV3 aggregationRequest, String timezone, Long currencyId) {
    DimensionDetail<Call> dimensionDetail = customFieldService.getCallDimensionsForCustomFields(userService.getLoggedInUser());
    List<FactFilter<Call>> factFilters = createFactFilters(aggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Call.class);
    List<AggregateRecord> aggregatedResults = callAggregationFacade
        .getAggregatedResults(dimensionDetail.getGroupByDimensions(), factFilters, aggregationRequest.getGroupBy(), aggregationRequest.getMetrics(),
            dimensionDetail.getMetrics(), timezone, currencyId);
    if (isHierarchyReport(aggregationRequest.getReportCategory())) {
      return getAggregatedResultsForHierarchyReport(aggregatedResults);
    }
    return aggregatedResults;
  }

  public List<AggregateRecord> aggregateResultsForTask(AggregationRequestV3 aggregationRequest, String timezone, Long currencyId) {
    DimensionDetail<Task> dimensionDetail = customFieldService.getTaskDimensionsForCustomFields(userService.getLoggedInUser());
    List<FactFilter<Task>> factFilters = createFactFilters(aggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Task.class);
    List<AggregateRecord> aggregatedResults = taskAggregationFacade.getAggregatedResults(dimensionDetail.getGroupByDimensions(), factFilters,
        aggregationRequest.getGroupBy(),
        aggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone, currencyId);
    if (isHierarchyReport(aggregationRequest.getReportCategory())) {
      return getAggregatedResultsForHierarchyReport(aggregatedResults);
    }
    return aggregatedResults;
  }

  public List<AggregateRecord> aggregateResultsForCompany(AggregationRequestV3 aggregationRequest, String timezone, Long baseCurrencyId,
      Long currencyId) {
    DimensionDetail<Company> dimensionDetail = customFieldService.getCompanyDimensionsForCustomFields(userService.getLoggedInUser());
    List<FactFilter<Company>> factFilters = createFactFilters(aggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Company.class);
    List<AggregateRecord> aggregatedResults = companyAggregationFacade.getAggregatedResults(dimensionDetail.getGroupByDimensions(), factFilters,
        aggregationRequest.getGroupBy(),
        aggregationRequest.getMetrics(), dimensionDetail.getMetrics(), baseCurrencyId, timezone, currencyId);
    if (isHierarchyReport(aggregationRequest.getReportCategory())) {
      return getAggregatedResultsForHierarchyReport(aggregatedResults);
    }
    return aggregatedResults;
  }

  public List<AggregateRecord> aggregateResultsForMeeting(AggregationRequestV3 aggregationRequest, String timezone, Long baseCurrencyId,
      Long currencyId) {
    DimensionDetail<Meeting> dimensionDetail = customFieldService.getMeetingDimensionsForCustomFields(userService.getLoggedInUser());
    List<FactFilter<Meeting>> factFilters = createFactFilters(aggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Meeting.class);
    List<AggregateRecord> aggregatedResults = meetingAggregationFacade.getAggregatedResults(dimensionDetail.getGroupByDimensions(), factFilters,
        aggregationRequest.getGroupBy(),
        aggregationRequest.getMetrics(), dimensionDetail.getMetrics(), baseCurrencyId, currencyId);
    if (isHierarchyReport(aggregationRequest.getReportCategory())) {
      return getAggregatedResultsForHierarchyReport(aggregatedResults);
    }
    return aggregatedResults;
  }

  public List<AggregateRecord> aggregateResultsForContact(AggregationRequestV3 aggregationRequest, String timezone, Long baseCurrencyId,
      Long currencyId) {
    DimensionDetail<Contact> dimensionDetail = customFieldService.getContactDimensionsForCustomFields(userService.getLoggedInUser());
    List<FactFilter<Contact>> factFilters = createFactFilters(aggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Contact.class);
    List<AggregateRecord> aggregatedResults = contactAggregationFacade.getAggregatedResults(dimensionDetail.getGroupByDimensions(), factFilters,
        aggregationRequest.getGroupBy(),
        aggregationRequest.getMetrics(), dimensionDetail.getMetrics(), baseCurrencyId, timezone, currencyId);
    if (isHierarchyReport(aggregationRequest.getReportCategory())) {
      return getAggregatedResultsForHierarchyReport(aggregatedResults);
    }
    return aggregatedResults;
  }

  public List<AggregateRecord> aggregateResultsForEmail(AggregationRequestV3 aggregationRequest, String timezone, Long currencyId) {
    List<FactFilter<Email>> factFilters = createFactFilters(aggregationRequest, timezone, emptyMap(), Email.class);
    List<AggregateRecord> aggregatedResults = emailAggregationFacade.getAggregatedResults(emptyMap(), factFilters,
        aggregationRequest.getGroupBy(),
        aggregationRequest.getMetrics(), currencyId);
    if (isHierarchyReport(aggregationRequest.getReportCategory())) {
      return getAggregatedResultsForHierarchyReport(aggregatedResults);
    }
    return aggregatedResults;
  }

  private boolean isHierarchyReport(ReportCategory reportCategory) {
    return ObjectUtils.isNotEmpty(reportCategory)
        && reportCategory.equals(ReportCategory.HIERARCHY);
  }

  private List<AggregateRecord> getAggregatedResultsForHierarchyReport(List<AggregateRecord> aggregatedResults) {
    List<HierarchyAggregateRecord> hierarchyUsers = userService.getUsersHierarchy();
    if (ObjectUtils.isEmpty(hierarchyUsers)) {
      return Collections.emptyList();
    }

    Map<Long, List<Number>> aggregatedValuesMap = aggregatedResults.stream().collect(toMap(AggregateRecord::getId, AggregateRecord::getValues));
    List<AggregateRecord> results = new ArrayList<>();
    hierarchyUsers.forEach(userResponse -> {
      userResponse.withValues(Optional.ofNullable(aggregatedValuesMap.get(userResponse.getId())).orElse(Collections.emptyList()));
      results.add(userResponse);
    });
    return results;
  }

  public ReportSummaries aggregateResultsForReportType(ReportType reportType, List<Long> reportIds, String timezone, Long baseCurrencyId,
      Long currencyId) {
    List<Report> reportFlux = reportFacade.searchByIds(reportType, reportIds);
    List<ReportWiseSummary> reportSummaries = reportFlux
        .stream()
        .map(report -> {
          Long reportCurrencyId = getReportCurrency(currencyId, report);
          User loggedInUser = userService.getLoggedInUser();
          AggregationRequestV3 aggregationRequest = new ObjectMapper().convertValue(report.getConfig(), AggregationRequestV3.class)
              .withReportCategory(report.getCategory());
          HeaderDetail headerDetailMono = reportHeaderBuilderCacheService.
              buildHeaders(loggedInUser.getTenantId(), reportType.name().toLowerCase(), reportType, aggregationRequest.getGroupBy(),
                  aggregationRequest.getMetrics(), reportType.getEntityClass(),
                  userService.getAuthenticationToken(), report.getId(),aggregationRequest.getDateRange());
          List<AggregateRecord> aggregateRecords =
              report.getCategory() == ReportCategory.GOALS_VS_ACHIEVEMENT ? emptyList()
                  : aggregateResultsFor(reportType, aggregationRequest, timezone, baseCurrencyId, reportCurrencyId);
          GoalAggregateResponse goalAggregateResponse =
              report.getCategory() == ReportCategory.GOALS_VS_ACHIEVEMENT ?
                  goalAggregateResultFor(reportType, aggregationRequest, timezone,
                      baseCurrencyId, report.getGoal(), loggedInUser, report.isProrated(), reportCurrencyId)
                  : new GoalAggregateResponse(emptyList(), null);

          ReportGoalResponse reportGoalResponseMono =
              report.getCategory() == ReportCategory.GOALS_VS_ACHIEVEMENT ? toReportGoalResponse(report.getGoal(), loggedInUser)
                  : new ReportGoalResponse();
          Action recordActions = getRecordActions(report.getReportType().getEntityClass(), loggedInUser, report.getCreatedBy(),
              report.isSystemDefault());

          log.info("Cached Header detail for tenant id {} and entity type {} with report id {} and name {} with payload {}",
              loggedInUser.getTenantId(),
              report.getReportType(), report.getId(), report.getName(), headerDetailMono);
          Currency currency = report.getCurrency();
          com.sling.sales.report.mq.event.IdName currencyIdName =
              ObjectUtils.isEmpty(currency) ? null : new com.sling.sales.report.mq.event.IdName(currency.getId(), currency.getName());
          return new ReportWiseSummary(
              report.getId(),
              report.getName(),
              report.getChartType(),
              report.getCategory(), aggregateRecords,
              goalAggregateResponse, headerDetailMono, aggregationRequest, report.getDescription(), report.getReportType(), recordActions,
              report.getCreatedBy(), reportGoalResponseMono, report.isSystemDefault(), report.isProrated(), currencyIdName);

        }).collect(toList());
    return new ReportSummaries(reportSummaries);
  }

  private Long getReportCurrency(Long currencyId, Report report) {
    if (ObjectUtils.isEmpty(currencyId) && !ObjectUtils.isEmpty(report.getCurrency())) {
      return report.getCurrency().getId();
    }
    return currencyId;
  }

  private List<AggregateRecord> aggregateResultsFor(ReportType reportType, AggregationRequestV3 aggregationRequest, String timezone,
      Long baseCurrencyId, Long currencyId) {
    switch (reportType) {
      case LEAD:
        return aggregateResultsForLead(aggregationRequest, timezone, currencyId);
      case DEAL:
        return aggregateResultsForDeal(aggregationRequest, timezone, currencyId);
      case TASK:
        return aggregateResultsForTask(aggregationRequest, timezone, currencyId);
      case CALL:
        return aggregateResultsForCall(aggregationRequest, timezone, currencyId);
      case COMPANY:
        return aggregateResultsForCompany(aggregationRequest, timezone, baseCurrencyId, currencyId);
      case MEETING:
        return aggregateResultsForMeeting(aggregationRequest, timezone, baseCurrencyId, currencyId);
      case CONTACT:
        return aggregateResultsForContact(aggregationRequest, timezone, baseCurrencyId, currencyId);
      case EMAIL:
        return aggregateResultsForEmail(aggregationRequest, timezone, currencyId);
      default:
        return null;
    }
  }

  private GoalAggregateResponse goalAggregateResultFor(ReportType reportType, AggregationRequestV3 aggregationRequest, String timezone,
      Long baseCurrencyId, Goal goal, User loggedInUser, boolean prorated, Long currencyId) {
    switch (reportType) {
      case DEAL:
        return goalAggregateResultForDeal(aggregationRequest, timezone, goal, loggedInUser, prorated, currencyId);
      case MEETING:
        return goalAggregateResultForMeeting(aggregationRequest, timezone, goal, loggedInUser, prorated, currencyId);
      case LEAD:
        return goalAggregateResultForLead(aggregationRequest, timezone, goal, loggedInUser, prorated, currencyId);
      case TASK:
      case CALL:
        return goalAggregateResultForCall(aggregationRequest, timezone, goal, loggedInUser, prorated, currencyId);
      case COMPANY:
      case CONTACT:
      case EMAIL:
      default:
        return null;
    }
  }

  private GoalAggregateResponse goalAggregateResultForLead(AggregationRequestV3 aggregateRequest, String timezone, Goal goal, User loggedInUser,
      boolean prorated, Long currencyId) {
    if (!goal.isActive()) {
      return new GoalAggregateResponse(emptyList(), null);
    }
    DimensionDetail<Lead> dimensionDetail = customFieldService.getLeadDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Lead>> factFilters = createFactFilters(aggregateRequest, timezone, dimensionDetail.getFilterDimensions(), Lead.class);
    return leadAggregationFacade
        .getGoalAggregateRecords(dimensionDetail.getGroupByDimensions(), factFilters, aggregateRequest.getGroupBy(),
            aggregateRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
            new GoalDetail(goal.getId(), goal.getValue(), prorated, aggregateRequest.getDateRange(), goal.getFrequency().getType(), timezone,
                goal.getDimensions().get(0).getField(), goal.getOwners(), goal.getCreatedBy(), goal.isShowOthersProgress()),
            loggedInUser, goal.getFieldValues(), currencyId);
  }

  private GoalAggregateResponse goalAggregateResultForDeal(AggregationRequestV3 aggregateRequest, String timezone, Goal goal, User loggedInUser,
      boolean prorated, Long currencyId) {
    if (!goal.isActive()) {
      return new GoalAggregateResponse(emptyList(), null);
    }
    DimensionDetail<Deal> dimensionDetail = customFieldService.getDealDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Deal>> factFilters = createFactFilters(aggregateRequest, timezone, dimensionDetail.getFilterDimensions(), Deal.class);
    return dealAggregationFacade
        .getGoalAggregateRecords(dimensionDetail.getGroupByDimensions(), factFilters, aggregateRequest.getGroupBy(),
            aggregateRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
            new GoalDetail(goal.getId(), goal.getValue(), prorated, aggregateRequest.getDateRange(), goal.getFrequency().getType(), timezone,
                goal.getDimensions().get(0).getField(), goal.getOwners(), goal.getCreatedBy(), goal.isShowOthersProgress()),
            loggedInUser,
            goal.getFieldValues(), currencyId);
  }

  private GoalAggregateResponse goalAggregateResultForMeeting(AggregationRequestV3 aggregateRequest, String timezone, Goal goal, User loggedInUser,
      boolean prorated, Long currencyId) {
    if (!goal.isActive()) {
      return new GoalAggregateResponse(emptyList(), null);
    }
    DimensionDetail<Meeting> dimensionDetail = customFieldService.getMeetingDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Meeting>> factFilters = createFactFilters(aggregateRequest, timezone, dimensionDetail.getFilterDimensions(), Meeting.class);
    return meetingAggregationFacade
        .getGoalAggregateRecords(dimensionDetail.getGroupByDimensions(), factFilters, aggregateRequest.getGroupBy(),
            aggregateRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
            new GoalDetail(goal.getId(), goal.getValue(), prorated, aggregateRequest.getDateRange(), goal.getFrequency().getType(), timezone,
                goal.getDimensions().get(0).getField(), goal.getOwners(), goal.getCreatedBy(), goal.isShowOthersProgress()), loggedInUser, goal.getFieldValues(), currencyId);
  }

  private GoalAggregateResponse goalAggregateResultForCall(AggregationRequestV3 aggregateRequest, String timezone, Goal goal, User loggedInUser,
      boolean prorated, Long currencyId) {
    if (!goal.isActive()) {
      return new GoalAggregateResponse(emptyList(), null);
    }
    DimensionDetail<Call> dimensionDetail = customFieldService.getCallDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Call>> factFilters = createFactFilters(aggregateRequest, timezone, dimensionDetail.getFilterDimensions(), Call.class);
    return callAggregationFacade
        .getGoalAggregateRecords(dimensionDetail.getGroupByDimensions(), factFilters, aggregateRequest.getGroupBy(),
            aggregateRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
            new GoalDetail(goal.getId(), goal.getValue(), prorated, aggregateRequest.getDateRange(), goal.getFrequency().getType(), timezone,
                goal.getDimensions().get(0).getField(), goal.getOwners(), goal.getCreatedBy(), goal.isShowOthersProgress()),
            loggedInUser, goal.getFieldValues(), currencyId);
  }

  private <T extends Fact> List<FactFilter<T>> createFactFilters(AggregationRequestV3 aggregationRequest, String timezone,
      Map<String, FilterDimension<T>> customFilterDimensions,
      Class<T> factClass) {
    List<Filter> aggregateFilters = new ArrayList<>();
    if (aggregationRequest.getDateRange() != null) {
      aggregateFilters.add(aggregationRequest.getDateRange());
    }
    aggregateFilters.addAll(aggregationRequest.getFilters());

    return aggregateFilters.stream()
        .map(
            filter -> {
              var operator = tryGetOperator(filter.getOperator());
              var dimension = getFilterDimension(filter, customFilterDimensions, factClass, operator);
              if (ObjectUtils.isEmpty(dimension)) {
                return null;
              }
              return new FactFilter<>(
                  operator,
                  dimension,
                  filter,
                  timezone);
            })
        .filter(Objects::nonNull)
        .collect(toList());
  }

  private <T extends Fact> FilterDimension<T> getFilterDimension(Filter filter, Map<String, FilterDimension<T>> customFilterDimensions,
      Class<T> factClass, Operator operator) {
    if (ObjectUtils.isEmpty(filter.getPrimaryField()) && ObjectUtils.isEmpty(filter.getProperty())) {
      return abstractFactFieldFactory.getFieldFactory(factClass)
          .getFilterField(filter.getFieldName(), operator, customFilterDimensions);
    }
    return abstractFactFieldFactory.getFieldFactory(factClass).createUserPropertyFilterIdDimension(filter);
  }

  private Operator tryGetOperator(String operatorString) {
    try {
      return Operator.valueOf(operatorString);
    } catch (IllegalArgumentException e) {
      log.error("Unsupported operator requested", e);
      throw new UnsupportedFilterException();
    }
  }

  public void deleteReport(long reportId) {
    reportFacade.deleteReport(reportId);
  }

  public DownloadDetail downloadReport(long reportId, String timezone, String baseCurrency, Long currencyId) {
    User loggedInUser = userService.getLoggedInUser();
    String authToken = userService.getAuthenticationToken();
    Report report = reportFacade.getReport(reportId);
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy");
    String fileName = report.getName() + "-" + simpleDateFormat.format(new Date());
    AggregationRequestV3 aggregationRequest = new ObjectMapper().convertValue(report.getConfig(), AggregationRequestV3.class);
    String currency = currencyId == null ? baseCurrency : currencyFacade.getCurrencyById(currencyId).getName();
    Resource resourceMono = downloadReportByReportType(report, aggregationRequest, timezone, loggedInUser,
        authToken,
        currency, report.isProrated(), currencyId);
    return new DownloadDetail(fileName, resourceMono);
  }

  public DownloadDetail downloadReport(long reportId, String timezone, String baseCurrency, AggregationRequestV3 aggregationRequestV3,
      boolean prorated, Long currencyId) {
    User loggedInUser = userService.getLoggedInUser();
    String authToken = userService.getAuthenticationToken();
    Report report = reportFacade.getReport(reportId);
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy");
    String fileName = report.getName() + "-" + simpleDateFormat.format(new Date());
    String currency = currencyId == null ? baseCurrency : currencyFacade.getCurrencyById(currencyId).getName();
    Resource resourceMono = downloadReportByReportType(report, aggregationRequestV3, timezone, loggedInUser, authToken, currency, prorated,
        currencyId);
    return new DownloadDetail(fileName, resourceMono);
  }

  private Resource downloadReportByReportType(Report report, AggregationRequestV3 aggregationRequest,
      String timezone,
      User loggedInUser,
      String authToken, String baseCurrency, boolean prorated, Long currencyId) {
    ReportType reportType = report.getReportType();
    switch (reportType) {
      case LEAD:
        return downloadLeadReport(report, aggregationRequest, timezone, loggedInUser, authToken, baseCurrency, prorated, currencyId);
      case DEAL:
        return downloadDealReport(report, aggregationRequest, timezone, loggedInUser, authToken, baseCurrency, prorated, currencyId);
      case TASK:
        return downloadTaskReport(report, aggregationRequest, timezone, authToken, baseCurrency, currencyId);
      case CALL:
        return downloadCallReport(report, aggregationRequest, timezone, loggedInUser, authToken, baseCurrency, currencyId);
      case COMPANY:
        return downloadCompanyReport(report, aggregationRequest, timezone, loggedInUser, authToken, baseCurrency, currencyId);
      case MEETING:
        return downloadMeetingReport(report, aggregationRequest, timezone, loggedInUser, authToken, baseCurrency, prorated, currencyId);
      case CONTACT:
        return downloadContactReport(report, aggregationRequest, timezone, loggedInUser, authToken, baseCurrency, currencyId);
      case EMAIL:
        return downloadEmailReport(report, aggregationRequest, timezone, loggedInUser, authToken, baseCurrency, currencyId);
      default:
        throw new InvalidFactForReporting();
    }

  }

  private Resource downloadLeadReport(Report report, AggregationRequestV3 aggregationRequest, String timezone, User loggedInUser,
      String authToken,
      String baseCurrency, boolean prorated, Long currencyId) {
    if (report.getCategory() == ReportCategory.GOALS_VS_ACHIEVEMENT) {
      GoalAggregateResponse goalAggregateResponse = goalAggregateResultForLead(aggregationRequest, timezone, report.getGoal(), loggedInUser,
          prorated, currencyId);
      return downloadReportService.generateCsvFileForGoalReport(report, goalAggregateResponse, aggregationRequest, Lead.class, authToken,
          EntityType.LEAD, baseCurrency, timezone);
    }
    DimensionDetail<Lead> dimensionDetail = customFieldService.getLeadDimensionsForCustomFields(loggedInUser);
    Map<String, FilterDimension<Lead>> customFilterDimensions = dimensionDetail.getFilterDimensions();
    List<FactFilter<Lead>> leadFactFilters = createFactFilters(aggregationRequest, timezone, customFilterDimensions, Lead.class);
    List<GroupByDimension<Lead>> groupByDimensions = getGroupDimensions(aggregationRequest.getGroupBy(), dimensionDetail.getGroupByDimensions(),
        Lead.class);
    List<AggregateRecord> aggregateRecords = leadAggregationFacade
        .getAggregationResult(leadFactFilters, groupByDimensions, aggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
            currencyId);
    return downloadReportService
        .generateCsvFile(aggregateRecords, report, aggregationRequest, Lead.class, authToken, EntityType.LEAD, baseCurrency, timezone);
  }

  private Resource downloadDealReport(Report report, AggregationRequestV3 aggregationRequest, String timezone,
      User loggedInUser, String authToken,
      String baseCurrency, boolean prorated, Long currencyId) {
    DimensionDetail<Deal> dimensionDetail = customFieldService.getDealDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Deal>> dealFactFilters = createFactFilters(aggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Deal.class);
    List<GroupByDimension<Deal>> groupByDimensions = getGroupDimensions(aggregationRequest.getGroupBy(), dimensionDetail.getGroupByDimensions(),
        Deal.class);
    if (report.getCategory() == ReportCategory.GOALS_VS_ACHIEVEMENT) {
      GoalAggregateResponse goalAggregateResponse = goalAggregateResultForDeal(aggregationRequest, timezone, report.getGoal(), loggedInUser,
          prorated, currencyId);
      return downloadReportService.generateCsvFileForGoalReport(report, goalAggregateResponse, aggregationRequest, Deal.class, authToken,
          EntityType.DEAL, baseCurrency, timezone);
    }
    List<AggregateRecord> aggregateRecords = dealAggregationFacade
        .getAggregationResult(dealFactFilters, groupByDimensions, aggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
            currencyId);
    return downloadReportService
        .generateCsvFile(aggregateRecords, report, aggregationRequest, Deal.class,
            authToken,
            EntityType.DEAL, baseCurrency, timezone);
  }

  private Resource downloadTaskReport(Report report, AggregationRequestV3 aggregationRequest, String timezone, String authToken,
      String baseCurrency, Long currencyId) {
    DimensionDetail<Task> dimensionDetail = customFieldService.getTaskDimensionsForCustomFields(userService.getLoggedInUser());
    List<FactFilter<Task>> taskFactFilters = createFactFilters(aggregationRequest, timezone, dimensionDetail.getFilterDimensions(), Task.class);
    List<GroupByDimension<Task>> groupByDimensions = getGroupDimensions(aggregationRequest.getGroupBy(), dimensionDetail.getGroupByDimensions(),
        Task.class);
    List<AggregateRecord> aggregateRecords = taskAggregationFacade
        .getAggregationResult(taskFactFilters, groupByDimensions, aggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
            currencyId);
    return downloadReportService
        .generateCsvFile(aggregateRecords, report, aggregationRequest, Task.class,
            authToken,
            EntityType.TASK, baseCurrency, timezone);
  }

  private Resource downloadCallReport(Report report, AggregationRequestV3 aggregationRequest, String timezone, User loggedInUser,
      String authToken,
      String baseCurrency, Long currencyId) {
    var dimensionDetail = customFieldService.getCallDimensionsForCustomFields(loggedInUser);
    List<FactFilter<Call>> callFactFilters = createFactFilters(aggregationRequest, timezone, dimensionDetail.getFilterDimensions(),
        Call.class);
    List<GroupByDimension<Call>> groupByDimensions = getGroupDimensions(aggregationRequest.getGroupBy(), dimensionDetail.getGroupByDimensions(),
        Call.class);
    List<AggregateRecord> aggregateRecords = callAggregationFacade
        .getAggregationResult(callFactFilters, groupByDimensions, aggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
            currencyId);
    return downloadReportService
        .generateCsvFile(aggregateRecords, report, aggregationRequest, Call.class,
            authToken,
            EntityType.CALL, baseCurrency, timezone);
  }

  private Resource downloadEmailReport(Report report, AggregationRequestV3 aggregationRequest, String timezone, User loggedInUser,
      String authToken,
      String baseCurrency, Long currencyId) {

    List<FactFilter<Email>> emailFactFilters = createFactFilters(aggregationRequest, timezone, emptyMap(),
        Email.class);
    List<GroupByDimension<Email>> groupByDimensions = getGroupDimensions(aggregationRequest.getGroupBy(), emptyMap(),
        Email.class);
    List<AggregateRecord> aggregateRecords = emailAggregationFacade
        .getAggregationResult(emailFactFilters, groupByDimensions, aggregationRequest.getMetrics(), emptyList(), timezone,
            currencyId);
    return downloadReportService
        .generateCsvFile(aggregateRecords, report, aggregationRequest, Email.class,
            authToken,
            EntityType.EMAIL, baseCurrency, timezone);
  }

  private Resource downloadCompanyReport(Report report, AggregationRequestV3 aggregationRequest, String timezone, User loggedInUser,
      String authToken,
      String baseCurrency, Long currencyId) {
    DimensionDetail<Company> dimensionDetail = customFieldService.getCompanyDimensionsForCustomFields(loggedInUser);
    Map<String, FilterDimension<Company>> customFilterDimensions = dimensionDetail.getFilterDimensions();
    List<FactFilter<Company>> leadFactFilters = createFactFilters(aggregationRequest, timezone, customFilterDimensions, Company.class);
    List<GroupByDimension<Company>> groupByDimensions = getGroupDimensions(aggregationRequest.getGroupBy(), dimensionDetail.getGroupByDimensions(),
        Company.class);
    List<AggregateRecord> aggregateRecords = companyAggregationFacade
        .getAggregationResult(leadFactFilters, groupByDimensions, aggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone,
            currencyId);
    return downloadReportService
        .generateCsvFile(aggregateRecords, report, aggregationRequest, Company.class,
            authToken,
            EntityType.COMPANY, baseCurrency, timezone);
  }

  private Resource downloadMeetingReport(Report report, AggregationRequestV3 aggregationRequest, String timezone, User loggedInUser,
      String authToken,
      String baseCurrency, boolean prorated, Long currencyId) {
    if (report.getCategory() == ReportCategory.GOALS_VS_ACHIEVEMENT) {
      GoalAggregateResponse goalAggregateResponse = goalAggregateResultForMeeting(aggregationRequest, timezone, report.getGoal(), loggedInUser,
          prorated, currencyId);
      return downloadReportService.generateCsvFileForGoalReport(report, goalAggregateResponse, aggregationRequest, Meeting.class, authToken,
          EntityType.MEETING, baseCurrency, timezone);
    }

    DimensionDetail<Meeting> dimensionDetail = customFieldService.getMeetingDimensionsForCustomFields(loggedInUser);
    Map<String, FilterDimension<Meeting>> customFilterDimensions = dimensionDetail.getFilterDimensions();
    List<FactFilter<Meeting>> factFilters = createFactFilters(aggregationRequest, timezone, customFilterDimensions, Meeting.class);
    List<GroupByDimension<Meeting>> groupByDimensions = getGroupDimensions(aggregationRequest.getGroupBy(), dimensionDetail.getGroupByDimensions(),
        Meeting.class);
    List<AggregateRecord> aggregateRecords = meetingAggregationFacade
        .getAggregationResult(factFilters, groupByDimensions, aggregationRequest.getMetrics(), dimensionDetail.getMetrics(), currencyId);
    return downloadReportService
        .generateCsvFile(aggregateRecords, report, aggregationRequest, Meeting.class,
            authToken,
            EntityType.MEETING, baseCurrency, timezone);
  }

  private Resource downloadContactReport(Report report, AggregationRequestV3 aggregationRequest, String timezone, User loggedInUser,
      String authToken,
      String baseCurrency, Long currencyId) {
    DimensionDetail<Contact> dimensionDetail = customFieldService.getContactDimensionsForCustomFields(loggedInUser);
    Map<String, FilterDimension<Contact>> customFilterDimensions = dimensionDetail.getFilterDimensions();
    List<FactFilter<Contact>> contactFilters = createFactFilters(aggregationRequest, timezone, customFilterDimensions, Contact.class);
    List<GroupByDimension<Contact>> groupByDimensions = getGroupDimensions(aggregationRequest.getGroupBy(), dimensionDetail.getGroupByDimensions(),
        Contact.class);
    List<AggregateRecord> aggregateRecords = contactAggregationFacade
        .getAggregationResult(contactFilters, groupByDimensions, aggregationRequest.getMetrics(), dimensionDetail.getMetrics(), timezone, currencyId);
    return downloadReportService
        .generateCsvFile(aggregateRecords, report, aggregationRequest, Contact.class,
            authToken,
            EntityType.CONTACT, baseCurrency, timezone);
  }

  private <T extends Fact> List<GroupByDimension<T>> getGroupDimensions(List<GroupByField> groupByFields,
      Map<String, GroupByDimension<T>> customGroupByDimensions, Class<T> clazz) {
    FactFieldFactory<T> factFieldFactory = abstractFactFieldFactory.getFieldFactory(clazz);
    Map<String, GroupByDimension<T>> groupByDimensions = factFieldFactory.getDimensionDetail().getGroupByDimensions();
    return groupByFields.stream()
        .map(groupByField -> getGroupDimension(groupByDimensions, customGroupByDimensions, groupByField, factFieldFactory))
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private <T extends Fact> GroupByDimension<T> getGroupDimension(Map<String, GroupByDimension<T>> groupByDimensions,
      Map<String, GroupByDimension<T>> customGroupByDimensions, GroupByField groupByField, FactFieldFactory<T> factFieldFactory) {
    if (customGroupByDimensions.containsKey(groupByField.getName())) {
      return customGroupByDimensions.get(groupByField.getName()).withFormat(groupByField.getFormat());
    }
    if (ObjectUtils.isEmpty(groupByField.getPrimaryField()) && ObjectUtils.isEmpty(groupByField.getProperty())) {
      return groupByDimensions.get(groupByField.getName()).withFormat(groupByField.getFormat());
    }
    return factFieldFactory.createUserPropertyGroupByIdDimension(groupByField);
  }

  public Report createReportFromGoal(Goal goal, User loggedInUser) {
    return reportFacade.createReportFromGoal(goal, loggedInUser);
  }
}
