package com.sling.sales.report.core.domain.contact;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import com.sling.sales.report.core.domain.lead.Lead;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Entity
@Getter
@Setter
@Slf4j
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContactEmail implements Serializable, EntityDimension {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private  Long id;
  private  String type;
  private  String value;
  private  boolean isPrimary;
  @ManyToOne
  @JoinColumn(name = "contact_id")
  @JsonIgnore
  private Contact contact;

  @JsonCreator
  public ContactEmail(
      @JsonProperty("type") String type,
      @JsonProperty("value") String value,
      @JsonProperty("primary") boolean isPrimary) {
    this.type=type;
    this.value=value;
    this.isPrimary=isPrimary;
  }

}
