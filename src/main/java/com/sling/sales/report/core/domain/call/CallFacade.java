package com.sling.sales.report.core.domain.call;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.StringUtils.capitalize;
import static org.springframework.util.CollectionUtils.isEmpty;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.core.domain.field.Field;
import com.sling.sales.report.core.domain.field.FieldFacade;
import com.sling.sales.report.core.domain.field.FieldType;
import com.sling.sales.report.dto.FieldDetail;
import com.sling.sales.report.error.JsonParseException;
import com.sling.sales.report.mq.event.CallDeletedEvent;
import com.sling.sales.report.mq.event.CallEvent;
import com.sling.sales.report.mq.event.CallEvent.IdNamePhoneNumber;
import com.sling.sales.report.mq.event.CallEvent.RelatedEntity;
import com.sling.sales.report.mq.event.DeleteRecordsEvent;
import com.sling.sales.report.mq.event.IdName;
import com.sling.sales.report.mq.event.PicklistValueDetail;
import com.sling.sales.report.security.domain.UserFacade;
import java.lang.reflect.Type;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeParseException;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class CallFacade {

  private final CallRepository callRepository;
  private final UserFacade userFacade;
  private final FieldFacade fieldFacade;
  private final CallCustomPicklistValueRepository callCustomPicklistValueRepository;
  private final ObjectMapper objectMapper;
  private final CallRelatedToRepository callRelatedToRepository;
  private final CallAssociatedToRepository callAssociatedToRepository;

  @Autowired
  public CallFacade(CallRepository callRepository, UserFacade userFacade, FieldFacade fieldFacade,
      CallCustomPicklistValueRepository callCustomPicklistValueRepository, ObjectMapper objectMapper, CallRelatedToRepository callRelatedToRepository,
      CallAssociatedToRepository callAssociatedToRepository) {
    this.callRepository = callRepository;
    this.userFacade = userFacade;
    this.fieldFacade = fieldFacade;
    this.callCustomPicklistValueRepository = callCustomPicklistValueRepository;
    this.objectMapper = objectMapper;
    this.callRelatedToRepository = callRelatedToRepository;
    this.callAssociatedToRepository = callAssociatedToRepository;
  }

  public void create(CallEvent event, HashMap<String, Object> payload, boolean deleted) {
    Optional<Call> optionalCall = callRepository
        .findById(event.getId());
    if (optionalCall.isPresent()) {
      return;
    }
    createCall(event, payload, deleted);
  }

  private void createCall(CallEvent event, HashMap<String, Object> payload, boolean deleted) {
    Call callToBeCreated = new Call();
    callToBeCreated.setId(event.getId());
    callToBeCreated.setTenantId(event.getTenantId());
    updateCallWithEventInformation(event, payload, callToBeCreated, deleted);
  }

  @Transactional
  public void update(CallEvent event, HashMap<String, Object> payload, boolean deleted) {
    Optional<Call> optionalCall = callRepository
        .findById(event.getId());
    if (optionalCall.isPresent() && (optionalCall.get().isDeleted() ||
        event.getUpdatedAt().before(optionalCall.get().getUpdatedAt()))) {
      return;
    }
    if(optionalCall.isPresent()){
      callRepository.delete(optionalCall.get());
    }
    createCall(event, payload, false);
  }

  public void delete(CallDeletedEvent event) {
    long tenantId = event.getTenantId();
    long id = event.getId();
    callRepository
        .findByTenantIdAndId(tenantId, id)
        .ifPresentOrElse(call -> callRepository.softDeleteCall(id), () -> createCallOnDeleteIfNotExists(event));
  }

  private void createCallOnDeleteIfNotExists(CallDeletedEvent event) {
    CallEvent callEvent = new CallEvent();
    callEvent.setId(event.getId());
    callEvent.setTenantId(event.getTenantId());
    callEvent.setOwner(new IdName(event.getTenantId(), "Owner"));
    callEvent.setRelatedTo(Collections.emptyList());
    callEvent.setAssociatedTo(Collections.emptyList());
    callEvent.setPhoneNumber("9999999999");
    callEvent.setIsManual(true);
    IdNamePhoneNumber createdBy = getIdNamePhoneNumber(event.getTenantId(), "CreatedBy");
    IdNamePhoneNumber updatedBy = getIdNamePhoneNumber(event.getTenantId(), "UpdatedBy");
    callEvent.setCreatedBy(createdBy);
    callEvent.setUpdatedBy(updatedBy);
    callEvent.setCreatedAt(Date.from(Instant.now()));
    callEvent.setUpdatedAt(Date.from(Instant.now()));
    try {
      HashMap<String, Object> payload = objectMapper.readValue(objectMapper.writeValueAsString(callEvent), new TypeReference<>() {
        @Override
        public Type getType() {
          return super.getType();
        }
      });
      createCall(callEvent, payload, true);
    } catch (JsonProcessingException e) {
      throw new JsonParseException(e);
    }
  }

  private IdNamePhoneNumber getIdNamePhoneNumber(long tenantId, String userName) {
    var idNamePhoneNumber = new IdNamePhoneNumber();
    idNamePhoneNumber.setId(tenantId);
    idNamePhoneNumber.setName(userName);
    idNamePhoneNumber.setPhoneNumber("9999999999");
    return idNamePhoneNumber;
  }

  private void updateCallWithEventInformation(CallEvent event, HashMap<String, Object> payload, Call call, boolean deleted) {
    var tenantId = event.getTenantId();
    IdName owner = event.getOwner();
    call.setCallConductedAt(event.getStartTime());
    call.setCreatedAt(event.getCreatedAt());
    call.setUpdatedAt(event.getUpdatedAt());
    call.setOwnerId(owner.getId());
    call.setCallType(isNull(event.getCallType()) ? null : event.getCallType());
    call.setOutcome(isNull(event.getOutcome()) ? null : event.getOutcome());
    call.setDeleted(deleted);

    var durationInMinutes = isNullOrZero(event.getDuration()) ? null : ((double) event.getDuration() / 60);
    call.setDurationInMinutes(durationInMinutes);

    var durationInHours = isNullOrZero(event.getDuration()) ? null : ((double) event.getDuration() / (60 * 60));
    call.setDurationInHours(durationInHours);

    var callbackDurationInMinutes = isNullOrZero(event.getCallbackDuration()) ? null : ((double) event.getCallbackDuration() / 60);
    call.setCallbackDurationInMinutes(callbackDurationInMinutes);

    call.setRecordingAvailable(nonNull(event.getCallRecording()));

    call.setCreatedById(event.getCreatedBy().getId());
    call.setUpdatedById(event.getUpdatedBy().getId());
    call.setIvrNumber(event.getIvrNumber());

    if (!isEmpty(event.getRelatedTo())) {
      var entities = event.getRelatedTo().stream()
          .map(this::getFormattedEntityName)
          .filter(Objects::nonNull)
          .collect(Collectors.toList());
      call.setEntities(entities);
    }

    var callEventData = new CallEventData();
    callEventData.putAll(payload);
    call.setEventPayload(callEventData);
    call.setPhoneNumber(event.getPhoneNumber());
    call.setOriginator(event.getOriginator());
    call.setReceiver(event.getReceiver());
    call.setDeviceId(event.getDeviceId());
    call.setManual(event.getIsManual() != null);
    call.addCallRelatedTos(getCallRelateTos(call, event.getRelatedTo()));
    call.addCallAssociatedTos(getCallAssociatedTos(call, event.getAssociatedTo()));
    CallDisposition callDisposition = event.getCallDisposition() == null ? null
        : new CallDisposition(event.getCallDisposition().getId(), event.getCallDisposition().getName(), tenantId);
    call.setCallDisposition(callDisposition);
    call.setOverallSentiment(event.getOverallSentiment());
    call.addCustomerEmotion(getCustomerEmotions(event.getCustomerEmotion(), call));

    Map<String, Object> customFieldValues =
        event.getCustomFieldValues() == null ? Collections.emptyMap() : event.getCustomFieldValues();
    Map<FieldType, List<FieldDetail>> fieldDetails = getFieldDetailsByTenantId(event.getTenantId(), customFieldValues);
    addCustomFieldValues(fieldDetails, call);
    callRepository.saveAndFlush(call);
  }

  private Set<CustomerEmotion> getCustomerEmotions(List<IdName> customerEmotions, Call call) {
    return Optional.ofNullable(customerEmotions)
        .orElse(Collections.emptyList())
        .stream()
        .map(customerEmotion -> new CustomerEmotion(
            customerEmotion.getId(),
            customerEmotion.getName(),
            call.getTenantId(),
            call.getId()))
        .collect(Collectors.toSet());
  }

  private Set<CallRelatedTo> getCallRelateTos(Call call, List<RelatedEntity> relatedTo) {
    return relatedTo
        .stream()
        .map(relatedEntity -> new CallRelatedTo(call.getId(), relatedEntity.getId(), relatedEntity.getEntity(), relatedEntity.getName(),
            relatedEntity.getOwnerId()))
        .collect(Collectors.toSet());
  }

  private Set<CallAssociatedTo> getCallAssociatedTos(Call call, List<RelatedEntity> relatedTo) {
    return relatedTo
        .stream()
        .map(relatedEntity -> new CallAssociatedTo(call.getId(), relatedEntity.getId(), relatedEntity.getEntity(), relatedEntity.getName(),
            relatedEntity.getOwnerId()))
        .collect(Collectors.toSet());
  }

  private void addCustomFieldValues(Map<FieldType, List<FieldDetail>> fieldDetails, Call call) {
    call.addCallPicklistValues(getCallPicklistValues(fieldDetails, call));
    call.addCallCustomTextValues(getCallCustomTextValues(fieldDetails, call));
    call.addCallCustomNumberValues(getCallCustomNumberValues(fieldDetails, call));
    call.addCallCustomDatePickerValues(getCallCustomDatePickerValues(fieldDetails, call));
    call.addCallCustomDatetimePickerValues(getCallCustomDatetimePickerValues(fieldDetails, call));
    call.addCallCustomCheckboxValues(getCallCustomCheckboxValues(fieldDetails, call));
  }

  private Map<FieldType, List<FieldDetail>> getFieldDetailsByTenantId(long tenantId, Map<String, Object> customFieldValues) {
    List<Field> fields = fieldFacade.getFieldsByTenantIdAndEntityType(tenantId, EntityType.CALL);
    return fields
        .stream()
        .filter(field -> customFieldValues.containsKey(field.getName()))
        .map(field -> new FieldDetail(field, customFieldValues.get(field.getName())))
        .collect(Collectors.groupingBy(fieldDetail -> fieldDetail.getField().getType()));
  }

  private Set<CallCustomPicklistValue> getCallPicklistValues(Map<FieldType, List<FieldDetail>> fieldDetails, Call call) {
    if (!fieldDetails.containsKey(FieldType.PICK_LIST)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.PICK_LIST)
        .stream()
        .map(fieldDetail -> {
          IdName idName = new ObjectMapper().convertValue(fieldDetail.getValue(), IdName.class);
          return new CallCustomPicklistValue(fieldDetail.getField().getId(), idName.getId(), idName.getName(), call.getId());
        }).collect(Collectors.toSet());
  }

  private Set<CallCustomTextValue> getCallCustomTextValues(Map<FieldType, List<FieldDetail>> fieldDetails, Call call) {
    if (!fieldDetails.containsKey(FieldType.TEXT_FIELD)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.TEXT_FIELD)
        .stream()
        .map(fieldDetail -> {
          String valueToBeSaved = fieldDetail.getValue() == null ? null : String.valueOf(fieldDetail.getValue());
          return new CallCustomTextValue(fieldDetail.getField().getId(), valueToBeSaved, call.getId());
        }).collect(Collectors.toSet());
  }

  private Set<CallCustomNumberValue> getCallCustomNumberValues(Map<FieldType, List<FieldDetail>> fieldDetails, Call call) {
    if (!fieldDetails.containsKey(FieldType.NUMBER)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.NUMBER)
        .stream()
        .map(fieldDetail -> {
          Double valueToBeSaved = fieldDetail.getValue() == null ? null : Double.parseDouble(String.valueOf(fieldDetail.getValue()));
          return new CallCustomNumberValue(fieldDetail.getField().getId(), valueToBeSaved, call.getId());
        }).collect(Collectors.toSet());
  }

  private Set<CallCustomDatePickerValue> getCallCustomDatePickerValues(Map<FieldType, List<FieldDetail>> fieldDetails, Call call) {
    if (!fieldDetails.containsKey(FieldType.DATE_PICKER)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.DATE_PICKER)
        .stream()
        .map(fieldDetail -> getCallCustomDatePickerValue(call, fieldDetail))
        .collect(Collectors.toSet());
  }

  private Set<CallCustomDatetimePickerValue> getCallCustomDatetimePickerValues(Map<FieldType, List<FieldDetail>> fieldDetails,
      Call call) {
    if (!fieldDetails.containsKey(FieldType.DATETIME_PICKER)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.DATETIME_PICKER)
        .stream()
        .map(fieldDetail -> getCallCustomDatetimePickerValue(call, fieldDetail))
        .collect(Collectors.toSet());
  }

  private CallCustomDatePickerValue getCallCustomDatePickerValue(Call call, FieldDetail fieldDetail) {
    if (fieldDetail.getValue() == null) {
      return null;
    }
    try {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString()).toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new CallCustomDatePickerValue(fieldDetail.getField().getId(), valueToBeSaved, call.getId());
    } catch (DateTimeParseException pe) {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString().substring(0, 23) + "Z").toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new CallCustomDatePickerValue(fieldDetail.getField().getId(), valueToBeSaved, call.getId());
    }
  }
  
  private CallCustomDatetimePickerValue getCallCustomDatetimePickerValue(Call call, FieldDetail fieldDetail) {
    if (fieldDetail.getValue() == null) {
      return null;
    }
    try {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString()).toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new CallCustomDatetimePickerValue(fieldDetail.getField().getId(), valueToBeSaved, call.getId());
    } catch (DateTimeParseException pe) {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString().substring(0, 23) + "Z").toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new CallCustomDatetimePickerValue(fieldDetail.getField().getId(), valueToBeSaved, call.getId());
    }
  }

  private Set<CallCustomCheckboxValue> getCallCustomCheckboxValues(Map<FieldType, List<FieldDetail>> fieldDetails, Call call) {
    if (!fieldDetails.containsKey(FieldType.CHECKBOX)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.CHECKBOX)
        .stream()
        .map(fieldDetail -> {
          Boolean valueToBeSaved = fieldDetail.getValue() == null ? Boolean.FALSE : Boolean.parseBoolean(String.valueOf(fieldDetail.getValue()));
          return new CallCustomCheckboxValue(fieldDetail.getField().getId(), valueToBeSaved, call.getId());
        }).collect(Collectors.toSet());
  }

  private boolean isNullOrZero(Long number) {
    return isNull(number) || number.equals(0L);
  }

  private String getFormattedEntityName(CallEvent.RelatedEntity relatedEntity) {
    String entity = isNull(relatedEntity) ? null : relatedEntity.getEntity();
    return isNull(entity) ? null : capitalize(entity.toLowerCase()).trim();
  }

  public void updateDisplayNameByPicklistValueId(PicklistValueDetail picklistValueDetail) {
    callCustomPicklistValueRepository.updateDisplayNameByPicklistValueId(picklistValueDetail.getId(), picklistValueDetail.getDisplayName());
  }

  @EventListener
  public void deleteSoftDeletedRecords(DeleteRecordsEvent deleteRecordsEvent) {
    callRepository.deleteRecordsOlderThan10Days();
  }

  public void updateCallRelatedToOwnersByEntityIdAndEntity(Long ownerId, Long entityId, String entity) {
    callRelatedToRepository.updateOwnerIdByEntityIdAndEntity(ownerId, entityId, entity);
  }

  public void updateCallAssociatedToOwnersByEntityIdAndEntity(Long ownerId, Long entityId, String entity) {
    callAssociatedToRepository.updateOwnerIdByEntityIdAndEntity(ownerId, entityId, entity);
  }

  public void updateCallRelatedToEntityNameByEntityIdAndEntity(String entityName, Long entityId, String entity) {
    callRelatedToRepository.updateEntityNameByEntityIdAndEntity(entityName, entityId, entity);
  }

  public void updateCallAssociatedToEntityNameByEntityIdAndEntity(String entityName, Long entityId, String entity) {
    callAssociatedToRepository.updateEntityNameByEntityIdAndEntity(entityName, entityId, entity);
  }
}
