package com.sling.sales.report.core.domain.aggregation.fact.field;

import static java.util.Collections.emptyList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.domain.aggregation.dimension.DateDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.IdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.TextDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.UserPropertyIdDimension;
import com.sling.sales.report.core.domain.company.Company;
import com.sling.sales.report.core.domain.company.CompanyEmail;
import com.sling.sales.report.core.domain.company.CompanyEmail_;
import com.sling.sales.report.core.domain.company.CompanyPhoneNumber;
import com.sling.sales.report.core.domain.company.CompanyPhoneNumber_;
import com.sling.sales.report.core.domain.company.Company_;
import com.sling.sales.report.core.domain.company.PickListValue;
import com.sling.sales.report.core.domain.company.PickListValue_;
import com.sling.sales.report.core.domain.contact.ContactEmail_;
import com.sling.sales.report.dto.DimensionDetail;
import com.sling.sales.report.mq.event.Revenue_;
import com.sling.sales.report.security.domain.Team;
import com.sling.sales.report.security.domain.Team_;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.User_;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;

@Service
class CompanyFieldFactory extends FactFieldFactory<Company> {

  private final static Map<String, CompanyUserMapping> MAPPED_PRIMARY_PROPERTIES = new HashMap<>() {{
    put("ownedBy", new CompanyUserMapping(Company_.OWNED_BY, Company_.ownedBy));
    put("createdBy", new CompanyUserMapping(Company_.CREATED_BY, Company_.createdBy));
    put("updatedBy", new CompanyUserMapping(Company_.UPDATED_BY, Company_.updatedBy));
    put("importedBy", new CompanyUserMapping(Company_.IMPORTED_BY, Company_.importedBy));
  }};
  
  private final IdDimension<Company, User> ownedBy = new IdDimension<>(
      "ownedBy",
      Company_.OWNED_BY,
      Company_.ownedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdDimension<Company, PickListValue> industry = new IdDimension<>(
      "industry",
      Company_.INDUSTRY,
      Company_.industry,
      join -> join.get(PickListValue_.id),
      join -> join.get(PickListValue_.name),
      emptyList());

  private final IdDimension<Company, PickListValue> numberOfEmployees = new IdDimension<>(
      "numberOfEmployees",
      Company_.NUMBER_OF_EMPLOYEES,
      Company_.numberOfEmployees,
      join -> join.get(PickListValue_.id),
      join -> join.get(PickListValue_.name),
      emptyList());

  private final IdDimension<Company, PickListValue> businessType = new IdDimension<>(
      "businessType",
      Company_.BUSINESS_TYPE,
      Company_.businessType,
      join -> join.get(PickListValue_.id),
      join -> join.get(PickListValue_.name),
      emptyList());

  private final IdDimension<Company, PickListValue> country = new IdDimension<>(
      "country",
      Company_.COUNTRY,
      Company_.country,
      join -> join.get(PickListValue_.id),
      join -> join.get(PickListValue_.name),
      emptyList());

  private final IdDimension<Company, User> importedBy = new IdDimension<>(
      "importedBy",
      Company_.IMPORTED_BY,
      Company_.importedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final TextDimension<Company> name = new TextDimension<>(
      "name",
      root -> root.get(Company_.name),
      emptyList());

  private final TextDimension<Company> city = new TextDimension<>(
      "city",
      root -> root.get(Company_.city),
      emptyList());

  private final TextDimension<Company> state = new TextDimension<>(
      "state",
      root -> root.get(Company_.state),
      emptyList());

  private final IdDimension<Company, User> createdBy = new IdDimension<>(
      "createdBy",
      Company_.CREATED_BY,
      Company_.createdBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdDimension<Company, User> updatedBy = new IdDimension<>(
      "updatedBy",
      Company_.UPDATED_BY,
      Company_.updatedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final TextDimension<Company> companyPhoneNumbers = new TextDimension<>(
      "phoneNumbers",
      root -> getOrCreateLeftJoinPhone(root, CompanyPhoneNumber_.VALUE).get(CompanyPhoneNumber_.value),
      emptyList());

  private final TextDimension<Company> companyEmails = new TextDimension<>(
      "emails",
      root -> getOrCreateLeftJoinEmail(root, CompanyEmail_.VALUE).get(CompanyEmail_.value),
      emptyList());

  private final TextDimension<Company> uniqueText1 = new TextDimension<>(
      "uniqueText1",
      root -> root.get(Company_.uniqueText1),
      emptyList());

  private final TextDimension<Company> uniqueText2 = new TextDimension<>(
      "uniqueText2",
      root -> root.get(Company_.uniqueText2),
      emptyList());

  private final DateDimension<Company> createdAt = new DateDimension<>("createdAt", root -> root.get(Company_.createdAt));
  private final DateDimension<Company> updatedAt = new DateDimension<>("updatedAt", root -> root.get(Company_.updatedAt));

  private final Map<String, GroupByDimension<Company>> GROUP_BY_DIMENSIONS =
      new HashMap<>() {
        {
          put(ownedBy.getName(), ownedBy);
          put(industry.getName(), industry);
          put(numberOfEmployees.getName(), numberOfEmployees);
          put(businessType.getName(), businessType);
          put(name.getName(), name);
          put(city.getName(), city);
          put(state.getName(), state);
          put(country.getName(), country);
          put(createdAt.getName(), createdAt);
          put(updatedAt.getName(), updatedAt);
          put(ownedBy.getName(), ownedBy);
          put(createdBy.getName(), createdBy);
          put(updatedBy.getName(), updatedBy);
          put(importedBy.getName(), importedBy);
          put(uniqueText1.getName(), uniqueText1);
          put(uniqueText2.getName(), uniqueText2);
        }
      };

  private final HashMap<String, FilterDimension<Company>> DIMENSIONS =
      new HashMap<>() {
        {
          put(ownedBy.getName(), ownedBy);
          put(industry.getName(), industry);
          put(numberOfEmployees.getName(), numberOfEmployees);
          put(businessType.getName(), businessType);
          put(name.getName(), name);
          put(city.getName(), city);
          put(state.getName(), state);
          put(country.getName(), country);
          put(createdAt.getName(), createdAt);
          put(updatedAt.getName(), updatedAt);
          put("annualRevenue", new NumberFilterDimension<>(
              "annualRevenue",
              root -> root.get(Company_.annualRevenue).get(Revenue_.value),
              Double::valueOf
          ));
          put(ownedBy.getName(), ownedBy);
          put(createdBy.getName(), createdBy);
          put(updatedBy.getName(), updatedBy);
          put(importedBy.getName(), importedBy);
          put(companyPhoneNumbers.getName(), companyPhoneNumbers);
          put(companyEmails.getName(), companyEmails);
          put(uniqueText1.getName(), uniqueText1);
          put(uniqueText2.getName(), uniqueText2);
        }
      };

  private final List<Metric<Company>> SUPPORTED_METRICS =
      new ArrayList<>() {{
        add(new Metric<>(
            "id",
            MetricType.COUNT,
            (root, builder) -> builder.countDistinct(root),
            null));
        add(new Metric<>(
            "annualRevenue",
            MetricType.SUM,
            (root, builder) -> builder.sum(root.get(Company_.annualRevenue).get(Revenue_.value)),
            null));
        add(new Metric<>(
            "annualRevenue",
            MetricType.AVERAGE,
            (root, builder) -> builder.avg(root.get(Company_.annualRevenue).get(Revenue_.value)),
            null));
      }};

  @Override
  public DimensionDetail<Company> getDimensionDetail() {
    return new DimensionDetail<>(GROUP_BY_DIMENSIONS, DIMENSIONS, SUPPORTED_METRICS);
  }

  @Override
  public GroupByDimension<Company> createUserPropertyGroupByIdDimension(GroupByField groupByField) {
    CompanyUserMapping companyUserMapping = MAPPED_PRIMARY_PROPERTIES.get(groupByField.getPrimaryField());
    if ("teams".equals(groupByField.getProperty())) {
      return new UserPropertyIdDimension<>(
          groupByField.getName(),
          companyUserMapping.getMappedColumnFieldName(),
          companyUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateLeftJoin(join, groupByField.getProperty()).get(Team_.id),
          join -> getOrCreateLeftJoin(join, groupByField.getProperty()).get(Team_.name),
          emptyList(),
          groupByField.getPrimaryField(),
          groupByField.getProperty()
      );
    }
    return null;
  }

  @Override
  public FilterDimension<Company> createUserPropertyFilterIdDimension(Filter filter) {
    CompanyUserMapping companyUserMapping = MAPPED_PRIMARY_PROPERTIES.get(filter.getPrimaryField());
    if ("teams".equals(filter.getProperty())) {
      return new UserPropertyIdDimension<>(
          filter.getFieldName(),
          companyUserMapping.getMappedColumnFieldName(),
          companyUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateLeftJoin(join, filter.getProperty()).get(Team_.id),
          join -> getOrCreateLeftJoin(join, filter.getProperty()).get(Team_.name),
          emptyList(),
          filter.getPrimaryField(),
          filter.getProperty()
      );
    }
    return null;
  }

  private Join<User, Team> getOrCreateLeftJoin(Join<Company, User> companyUserJoin, String dimensionName) {
    return companyUserJoin.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<User, Team>) j))
        .findFirst()
        .orElseGet(() -> companyUserJoin.join(User_.teams, JoinType.LEFT));
  }

  private Join<Company, CompanyPhoneNumber> getOrCreateLeftJoinPhone(Root<Company> root, String dimensionName) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<Company, CompanyPhoneNumber>) j))
        .findFirst()
        .orElseGet(() -> root.join(Company_.companyPhoneNumbers, JoinType.LEFT));
  }

  private Join<Company, CompanyEmail> getOrCreateLeftJoinEmail(Root<Company> root, String dimensionName) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<Company, CompanyEmail>) j))
        .findFirst()
        .orElseGet(() -> root.join(Company_.companyEmails, JoinType.LEFT));
  }
}
