package com.sling.sales.report.core.domain.aggregation.dimension;

import java.util.HashSet;
import java.util.Set;

public enum Operator {
  equal("Equals"),
  not_equal("Not Equals"),
  greater("Greater Than"),
  greater_or_equal("Greater Than or Equals to"),
  less("Less Than"),
  less_or_equal("Less Than or Equals to"),
  between("Between"),
  not_between("Not Between"),
  contains("Contains"),
  not_contains("Not Contains"),
  in("In"),
  not_in("Not In"),
  is_empty("Is Empty"),
  is_not_empty("Is Not Empty"),
  begins_with("Begins With"),
  is_null("Is Not Set"),
  is_not_null("Is Set"),
  today("Today - Between"),
  before_current_date_and_time("Before Current Date and Time - Less Than"),
  after_current_date_and_time("After Current Date and Time - Greater Than"),
  current_week("Current Week - Between"),
  last_week("Last Week - Between"),
  current_month("Current Month - Between"),
  last_month("Last Month - Between"),
  current_year("Current Year- Between"),
  last_year("Last Year- Between"),
  current_quarter("Current Quarter - Between"),
  last_quarter("Last Quarter- Between"),
  next_week("Next Week - Between"),
  next_month("Next Month - Between"),
  next_quarter("Next Quarter - Between"),
  next_year("Next Year - Between"),
  last_seven_days("Last Seven Days"),
  next_seven_days("Next Seven Days"),
  last_fifteen_days("Last Fifteen Days"),
  next_fifteen_days("Next Fifteen Days"),
  last_thirty_days("Last Thirty Days"),
  next_thirty_days("Next Thirty Days"),

  week_to_date("Week To Date"),
  month_to_date("Month To Date"),
  quarter_to_date("Quarter To Date"),
  year_to_date("Week To Date"),
  multi_field("Multi Field"),
  yesterday("Yesterday"),
  tomorrow("Tomorrow");

  private String displayName;

  Operator(String displayName) {
    this.displayName = displayName;
  }

  public String getDisplayName() {
    return displayName;
  }

  private static Set<Operator> ROLLING_DATE_OPERATORS = new HashSet<>() {{
    add(before_current_date_and_time);
    add(after_current_date_and_time);
    add(today);
    add(current_week);
    add(last_week);
    add(current_month);
    add(last_month);
    add(current_quarter);
    add(last_quarter);
    add(current_year);
    add(last_year);
    add(next_week);
    add(next_month);
    add(next_quarter);
    add(next_year);
    add(last_seven_days);
    add(next_seven_days);
    add(last_fifteen_days);
    add(next_fifteen_days);
    add(last_thirty_days);
    add(next_thirty_days);
    add(week_to_date);
    add(month_to_date);
    add(quarter_to_date);
    add(year_to_date);
    add(yesterday);
    add(tomorrow);
  }};

  public boolean isRollingDateOperator() {
    return ROLLING_DATE_OPERATORS.contains(this);
  }
}
