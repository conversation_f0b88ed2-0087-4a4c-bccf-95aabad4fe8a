package com.sling.sales.report.core.api.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoalAggregationRequest extends AggregationRequestV3 {

  @JsonCreator
  public GoalAggregationRequest(
      @JsonProperty("rules") List<Filter> filters, @JsonProperty("groupBy") List<GroupByField> groupBy,
      @JsonProperty("dateRange") Filter dateRange, @JsonProperty("metrics") List<Metric> metrics,
      @JsonProperty("colorCodes") List<ColorCode> colorCodes) {
    super(filters, groupBy, dateRange, metrics, colorCodes);
  }
}
