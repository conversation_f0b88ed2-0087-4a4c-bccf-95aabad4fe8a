package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_equal;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.Format;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.util.List;
import java.util.function.Function;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.jpa.domain.Specification;

public class BooleanDimension<T extends Fact> implements FilterDimension<T>, GroupByDimension<T> {

  private final String name;
  private final List<Operator> allowedOperators = asList(equal, not_equal, is_null, is_not_null);
  private final Function<Root<T>, Path<Boolean>> pathToColumn;
  private Format format;

  public BooleanDimension(String name, Function<Root<T>, Path<Boolean>> pathToColumn) {
    this.name = name;
    this.pathToColumn = pathToColumn;
  }

  @Override
  public BooleanDimension<T> withFormat(Format format) {
    this.format = format;
    return this;
  }

  @Override
  public Format getFormat() {
    return this.format;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    switch (operator) {
      case equal:
        return equalTo(Boolean.parseBoolean(value.toString()));
      case not_equal:
        return notEqualTo(Boolean.parseBoolean(value.toString()));
      case is_null:
        return isNull();
      case is_not_null:
        return isNotNull();
      default:
        return null;
    }
  }

  private Specification<T> equalTo(boolean value) {
    return ((root, query, builder) ->
        builder.equal(pathToColumn.apply(root), value));
  }

  private Specification<T> notEqualTo(boolean value) {
    return ((root, query, builder) ->
        builder.notEqual(pathToColumn.apply(root), value));
  }

  @Override
  public List<Expression<?>> getGroupByPath(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, String timezone) {
    return singletonList(pathToColumn.apply(root));
  }

  Specification<T> isNull() {
    return ((root, query, builder) -> builder.isNull(
        pathToColumn.apply(root)
    ));
  }

  Specification<T> isNotNull() {
    return ((root, query, builder) -> builder.isNotNull(
        pathToColumn.apply(root)
    ));
  }

  @Override
  public Aggregate toResultKey(Tuple tuple, int columnNumber) {
    int nextColumnNumber = columnNumber + 1;
    if (ObjectUtils.isEmpty(tuple.get(columnNumber, Boolean.class))) {
      return new Aggregate(null, "No", nextColumnNumber, null);
    }
    String booleanAsString = tuple.get(columnNumber, Boolean.class).equals(Boolean.TRUE) ? "Yes" : "No";
    return new Aggregate(null, booleanAsString, nextColumnNumber, null);
  }
}
