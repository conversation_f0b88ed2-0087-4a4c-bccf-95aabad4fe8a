package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.aggregation.dimension.DateCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyBooleanCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyTextCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberCustomFieldFilterDimension;
import com.sling.sales.report.core.domain.task.Task;
import com.sling.sales.report.core.domain.task.TaskCustomCheckboxValue;
import com.sling.sales.report.core.domain.task.TaskCustomCheckboxValue_;
import com.sling.sales.report.core.domain.task.TaskCustomDatePickerValue;
import com.sling.sales.report.core.domain.task.TaskCustomDatePickerValue_;
import com.sling.sales.report.core.domain.task.TaskCustomDatetimePickerValue;
import com.sling.sales.report.core.domain.task.TaskCustomDatetimePickerValue_;
import com.sling.sales.report.core.domain.task.TaskCustomNumberValue;
import com.sling.sales.report.core.domain.task.TaskCustomNumberValue_;
import com.sling.sales.report.core.domain.task.TaskCustomPicklistValue;
import com.sling.sales.report.core.domain.task.TaskCustomPicklistValue_;
import com.sling.sales.report.core.domain.task.TaskCustomTextValue;
import com.sling.sales.report.core.domain.task.TaskCustomTextValue_;
import com.sling.sales.report.core.domain.task.Task_;
import com.sling.sales.report.dto.DimensionDetail;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class TaskCustomFieldFactory {

  public static DimensionDetail<Task> createManyIdCustomFieldDimension(String dimensionName, long fieldId) {
    ManyIdCustomFieldDimension<Task, TaskCustomPicklistValue> manyIdCustomFieldDimension = new ManyIdCustomFieldDimension<>(
        dimensionName,
        Task_.TASK_PICKLIST_VALUES,
        Task_.taskPicklistValues,
        join -> join.get(TaskCustomPicklistValue_.picklistValueId),
        join -> join.get(TaskCustomPicklistValue_.displayName),
        join -> join.get(TaskCustomPicklistValue_.fieldId),
        fieldId, TaskCustomPicklistValue_.DISPLAY_NAME);

    List<Metric<Task>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Task_.taskPicklistValues, join -> join.get(TaskCustomPicklistValue_.fieldId),
              TaskCustomPicklistValue_.PICKLIST_VALUE_ID, fieldId),
          null));
    }};

    return new DimensionDetail<>(manyIdCustomFieldDimension, manyIdCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Task> createManyTextCustomFieldDimension(String dimensionName, long fieldId) {
    ManyTextCustomFieldDimension<Task, TaskCustomTextValue> manyTextCustomFieldDimension = new ManyTextCustomFieldDimension<>(
        dimensionName,
        Task_.TASK_CUSTOM_TEXT_VALUES,
        Task_.taskCustomTextValues,
        join -> join.get(TaskCustomTextValue_.value),
        join -> join.get(TaskCustomTextValue_.fieldId),
        fieldId
    );

    List<Metric<Task>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Task_.taskCustomTextValues, join -> join.get(TaskCustomTextValue_.fieldId),
              TaskCustomTextValue_.VALUE, fieldId),
          null));
    }};

    return new DimensionDetail<>(manyTextCustomFieldDimension, manyTextCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Task> createNumberCustomFieldFilterDimension(String dimensionName, long fieldId) {
    NumberCustomFieldFilterDimension<Task, TaskCustomNumberValue> numberCustomFieldFilterDimension = new NumberCustomFieldFilterDimension<>(
        dimensionName,
        Task_.TASK_CUSTOM_NUMBER_VALUES,
        Task_.taskCustomNumberValues,
        join -> join.get(TaskCustomNumberValue_.value),
        join -> join.get(TaskCustomNumberValue_.fieldId), fieldId);

    List<Metric<Task>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Task_.taskCustomNumberValues, join -> join.get(TaskCustomNumberValue_.fieldId),
              TaskCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.SUM,
          getBiFunction(MetricType.SUM, Task_.taskCustomNumberValues, join -> join.get(TaskCustomNumberValue_.fieldId),
              TaskCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.AVERAGE,
          getBiFunction(MetricType.AVERAGE, Task_.taskCustomNumberValues, join -> join.get(TaskCustomNumberValue_.fieldId),
              TaskCustomNumberValue_.VALUE, fieldId),
          null));
    }};

    return new DimensionDetail<>(null, numberCustomFieldFilterDimension, metrics);
  }

  public static DimensionDetail<Task> createDatePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Task, TaskCustomDatePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Task_.TASK_CUSTOM_DATE_PICKER_VALUES,
        Task_.taskCustomDatePickerValues,
        join -> join.get(TaskCustomDatePickerValue_.value),
        join -> join.get(TaskCustomDatePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Task> createDatetimePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Task, TaskCustomDatetimePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Task_.TASK_CUSTOM_DATETIME_PICKER_VALUES,
        Task_.taskCustomDatetimePickerValues,
        join -> join.get(TaskCustomDatetimePickerValue_.value),
        join -> join.get(TaskCustomDatetimePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Task> createBooleanCustomFieldDimension(String dimensionName, long fieldId) {
    ManyBooleanCustomFieldDimension<Task, TaskCustomCheckboxValue> manyBooleanCustomFieldDimension = new ManyBooleanCustomFieldDimension<>(
        dimensionName,
        Task_.TASK_CUSTOM_CHECKBOX_VALUES,
        Task_.taskCustomCheckboxValues,
        join -> join.get(TaskCustomCheckboxValue_.value),
        join -> join.get(TaskCustomCheckboxValue_.fieldId),
        fieldId
    );
    return new DimensionDetail<>(manyBooleanCustomFieldDimension, manyBooleanCustomFieldDimension, Collections.emptyList());
  }

  private static <T, V> BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>> getBiFunction(MetricType metricType,
      SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> getFieldId, String attributeName, long fieldId) {

    return (root, builder) -> CustomFieldMetricBuilder
        .buildExpressionByMetricType(metricType, root, builder, dimensionField, getFieldId,
            attributeName, fieldId);
  }
}
