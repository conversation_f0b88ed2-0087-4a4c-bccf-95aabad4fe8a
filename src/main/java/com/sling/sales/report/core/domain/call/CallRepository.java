package com.sling.sales.report.core.domain.call;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface CallRepository extends JpaRepository<Call, Long>, JpaSpecificationExecutor<Call> {

  Optional<Call> findByTenantIdAndId(Long tenantId, Long id);

  @Modifying(flushAutomatically = true)
  @Transactional
  void deleteByTenantIdAndId(Long tenantId, Long id);

  @Modifying(flushAutomatically = true)
  @Transactional
  @Query(value = "DELETE FROM call WHERE deleted = true AND updated_at < now() - INTERVAL '10 days'", nativeQuery = true)
  void deleteRecordsOlderThan10Days();

  @Modifying(flushAutomatically = true)
  @Transactional
  @Query(value = "UPDATE call set deleted = TRUE WHERE id = :DealId", nativeQuery = true)
  void softDeleteCall(@Param("DealId") long dealId);
}
