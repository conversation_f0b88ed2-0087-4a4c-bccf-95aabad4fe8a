package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.company.Company;
import com.sling.sales.report.security.domain.User;
import javax.persistence.metamodel.SingularAttribute;
import lombok.Getter;

@Getter
public class CompanyUserMapping {

  private final String mappedColumnFieldName;
  private final SingularAttribute<Company, User> mappedColumnFieldPath;

  public CompanyUserMapping(String mappedColumnFieldName, SingularAttribute<Company, User> mappedColumnFieldPath) {
    this.mappedColumnFieldName = mappedColumnFieldName;
    this.mappedColumnFieldPath = mappedColumnFieldPath;
  }
}
