package com.sling.sales.report.core.domain.company;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor
public class CompanyCustomMultiPicklistValue implements EntityDimension {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;
  private long fieldId;
  @Column(name = "company_id", insertable = false, updatable = false)
  private long companyId;
  private Long picklistValueId;
  private String displayName;

  @ManyToOne
  @JoinColumn(name = "company_id")
  @JsonIgnore
  private Company company;

  public CompanyCustomMultiPicklistValue(long fieldId, long companyId, Long picklistValueId, String displayName, Company company) {
    this.fieldId = fieldId;
    this.companyId = companyId;
    this.picklistValueId = picklistValueId;
    this.displayName = displayName;
    this.company = company;
  }
}
