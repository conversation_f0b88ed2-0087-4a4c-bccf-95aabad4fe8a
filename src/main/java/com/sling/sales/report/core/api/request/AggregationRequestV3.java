package com.sling.sales.report.core.api.request;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.ToString;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class AggregationRequestV3 {

  @JsonProperty("rules")
  protected final ArrayList<Filter> filters = new ArrayList<>();

  @NotEmpty
  @Valid
  @JsonProperty("groupBy")
  protected final List<GroupByField> groupBy;

  @NotNull
  @Valid
  @JsonProperty("dateRange")
  protected final Filter dateRange;

  @NotEmpty
  @JsonProperty("metrics")
  protected final List<Metric> metrics;

  @JsonProperty("colorCodes")
  protected final List<ColorCode> colorCodes;
  @JsonProperty(access = Access.WRITE_ONLY)
  protected ReportCategory reportCategory;

  @JsonCreator
  public AggregationRequestV3(
      @JsonProperty("rules") List<Filter> filters, @JsonProperty("groupBy") List<GroupByField> groupBy,
      @JsonProperty("dateRange") Filter dateRange, @JsonProperty("metrics") List<Metric> metrics,
      @JsonProperty("colorCodes") List<ColorCode> colorCodes, @JsonProperty("category") ReportCategory reportCategory) {
    this.groupBy = groupBy;
    this.dateRange = dateRange;
    this.metrics = metrics;
    this.colorCodes = colorCodes;
    this.filters.addAll(filters);
    this.reportCategory = reportCategory;
  }

  public AggregationRequestV3(List<Filter> filters, List<GroupByField> groupBy, Filter dateRange, List<Metric> metrics, List<ColorCode> colorCodes) {
    this.groupBy = groupBy;
    this.dateRange = dateRange;
    this.metrics = metrics;
    this.colorCodes = colorCodes;
    this.filters.addAll(filters);
  }

  @JsonIgnore
  public AggregationRequestV3 withReportCategory(ReportCategory reportCategory) {
    this.reportCategory = reportCategory;
    return this;
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @ToString
  public static class Filter {

    @NotBlank
    private final String operator;
    @NotBlank
    private final String fieldName;
    private final String id;
    private final String fieldType;
    private final Object value;
    private final String from;
    private final String to;
    private final String fieldInputType;
    private final String primaryField;
    private final String property;

    @JsonCreator
    public Filter(
        @JsonProperty("operator") String operator,
        @JsonAlias("fieldName")
        @JsonProperty("field") String fieldName,
        @JsonProperty("id") String id,
        @JsonAlias("fieldType")
        @JsonProperty("type") String fieldType,
        @JsonProperty("value") Object value,
        @JsonProperty("from") String from,
        @JsonProperty("to") String to,
        @JsonProperty("fieldInputType") String fieldInputType,
        @JsonProperty("primaryField") String primaryField, @JsonProperty("property") String property) {
      this.operator = operator;
      this.fieldName = fieldName;
      this.id = id;
      this.fieldType = fieldType;
      this.value = value;
      this.from = from;
      this.to = to;
      this.fieldInputType = fieldInputType;
      this.primaryField = primaryField;
      this.property = property;
    }
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @ToString
  public static class Metric implements Serializable {
    private final MetricType type;
    private final String field;
    private final String header;

    @JsonCreator
    public Metric(
        @JsonProperty("type") MetricType type,
        @JsonProperty("field") String field,
        @JsonProperty("header") String header) {
      this.type = type;
      this.field = field;
      this.header = header;
    }

    @Override
    public String toString() {
      return "Metric{" +
          "type=" + type +
          ", field='" + field + '\'' +
          '}';
    }
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class ColorCode implements Serializable {

    private final String hexCode;
    private final String operator;
    private final Number from;
    private final Number to;
    private final Number value;

    @JsonCreator
    public ColorCode(
        @JsonProperty("hexCode") String hexCode,
        @JsonProperty("operator") String operator,
        @JsonProperty("from") Number from,
        @JsonProperty("to") Number to,
        @JsonProperty("value") Number value) {
      this.hexCode = hexCode;
      this.operator = operator;
      this.from = from;
      this.to = to;
      this.value = value;
    }
  }
}
