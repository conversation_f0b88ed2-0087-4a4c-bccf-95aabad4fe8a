package com.sling.sales.report.core.domain.aggregation.fact;

import static java.util.stream.Collectors.toList;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.api.ProrateHandler;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import com.sling.sales.report.core.api.response.AggregateRecord;
import com.sling.sales.report.core.api.response.GoalAggregateRecord;
import com.sling.sales.report.core.api.response.GoalAggregateResponse;
import com.sling.sales.report.core.api.response.GoalCount;
import com.sling.sales.report.core.api.response.GoalCountSummary;
import com.sling.sales.report.core.api.response.IdName;
import com.sling.sales.report.core.domain.aggregation.dimension.Aggregate;
import com.sling.sales.report.core.domain.aggregation.dimension.GoalAggregate;
import com.sling.sales.report.core.domain.aggregation.dimension.GoalDetail;
import com.sling.sales.report.core.domain.aggregation.dimension.GoalGroupByDimensionDetail;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.Operator;
import com.sling.sales.report.core.domain.aggregation.dimension.RollingDateFilter;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import com.sling.sales.report.core.domain.goal.GoalFieldValue;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Selection;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
class FactAggregationRepositoryImpl implements FactAggregationRepository {

  @PersistenceContext
  private EntityManager entityManager;

  @Autowired
  private ProrateHandler prorateHandler;
  @Autowired
  private ObjectMapper objectMapper;

  @Override
  public <T extends Fact> List<AggregateRecord> groupByMetric(
      Specification<T> withSpecification,
      List<GroupByDimension<T>> groupByDimensions,
      List<BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>>> metricFunctions,
      Class<T> clazz, String timezone) {
    List<Tuple> resultList = getTuples(withSpecification, groupByDimensions, metricFunctions, clazz, timezone);

    boolean isTimeBasedDimensionPresent =
        groupByDimensions.stream()
            .anyMatch(groupByDimension -> ObjectUtils.isNotEmpty(groupByDimension.getFormat()));

    Map<Aggregate, AggregateRecord> recordMap = new LinkedHashMap<>();
    resultList.stream()
        .filter(Objects::nonNull)
        .forEach(tuple -> tupleToAggregateRecords(recordMap, tuple, groupByDimensions, metricFunctions.size(), isTimeBasedDimensionPresent)
        );
    List<AggregateRecord> aggregateRecords = new ArrayList<>(recordMap.values());
    if (isTimeBasedDimensionPresent) {
      aggregateRecords.sort(this::orderByDate);
    }
    return aggregateRecords;
  }

  @Override
  public <T extends Fact> GoalAggregateResponse groupByGoalMetric(Specification<T> withSpecification, List<GroupByDimension<T>> groupByDimensions,
      List<BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>>> metricFn, Class<T> clazz, String timezone, GoalDetail goalDetail,
      Set<GoalFieldValue> goalFieldValues, FactFilter<T> dimensionAsFilter, List<Metric> metrics) {

    Set<GoalFieldValue> filteredGoalFieldValues = goalFieldValues.stream()
        .filter(goalFieldValue -> goalFieldValue.getFieldName().equals(goalDetail.getFieldName()))
        .collect(Collectors.toSet());

    List<Tuple> resultList = getGoalTuples(withSpecification, groupByDimensions.get(0), metricFn, clazz, timezone, goalDetail,
        filteredGoalFieldValues);

    Map<Long, GoalFieldValue> goalFieldValuesByFieldValueId = getGoalFieldValuesByFieldValueId(filteredGoalFieldValues);

    GroupByDimension<T> groupByDimension = groupByDimensions.get(0);
    List<GoalAggregateRecord> allGoalAggregateRecords = getGoalAggregateRecords(filteredGoalFieldValues,
        resultList, groupByDimension, dimensionAsFilter, goalDetail, goalFieldValuesByFieldValueId);
    GoalCountSummary goalCountSummary = getGoalCountSummary(allGoalAggregateRecords, goalDetail.getValue(), metrics);
    GoalAggregateResponse goalAggregateResponse = new GoalAggregateResponse(allGoalAggregateRecords, goalCountSummary);
    return prorate(goalAggregateResponse, goalDetail).withSortedAggregateRecords();
  }

  private Map<Long, GoalFieldValue> getGoalFieldValuesByFieldValueId(Set<GoalFieldValue> goalFieldValues) {
    Map<Long, GoalFieldValue> goalFieldValuesByFieldValueId = new HashMap<>();
    goalFieldValues
        .forEach(goalFieldValue -> {
          Long goalFieldValueId = ObjectUtils.isEmpty(goalFieldValue.getFieldValueId()) ? 0 : goalFieldValue.getFieldValueId();
          goalFieldValuesByFieldValueId.putIfAbsent(goalFieldValueId, goalFieldValue);
        });
    return goalFieldValuesByFieldValueId;
  }

  private GoalCountSummary getGoalCountSummary(List<GoalAggregateRecord> allGoalAggregateRecords, double goalValue, List<Metric> metrics) {
    if (ObjectUtils.isEmpty(allGoalAggregateRecords)) {
      return new GoalCountSummary(0, 0, 0.0, 0.0, 0.0);
    }
    List<Double> summaryCounts = new ArrayList<>(Collections.nCopies(1, 0.0));
    allGoalAggregateRecords
        .forEach(
            goalAggregateRecord -> summaryCounts.set(0, summaryCounts.get(0) + goalAggregateRecord.getValue().get(0).getAchieved().doubleValue()));

    Double sumOfAchieved = summaryCounts.get(0);
    Double percentage = goalValue > 0.0 ? (sumOfAchieved / goalValue) * 100 : 0.0;
    Double averageOfAchieved = sumOfAchieved / allGoalAggregateRecords.size();
    Double averageOfGoal = goalValue / allGoalAggregateRecords.size();
    if (ObjectUtils.isNotEmpty(metrics.get(0)) && MetricType.AVERAGE.equals(metrics.get(0).getType())){
      return new GoalCountSummary(averageOfAchieved, goalValue, percentage, averageOfAchieved, averageOfGoal);
    }
    return new GoalCountSummary(sumOfAchieved, goalValue, percentage, averageOfAchieved, averageOfGoal);
  }

  private <T extends Fact> List<GoalAggregateRecord> getGoalAggregateRecords(Set<GoalFieldValue> goalFieldValues, List<Tuple> resultList,
      GroupByDimension<T> groupByDimension, FactFilter<T> dimensionAsFilter, GoalDetail goalDetail,
      Map<Long, GoalFieldValue> goalFieldValuesByFieldValueId) {
    Set<GoalAggregateRecord> goalAggregateRecords = resultList.isEmpty() ? new HashSet<>() :
        resultList
            .stream()
            .filter(Objects::nonNull)
            .map(tuple -> tupleToGoalAggregateRecord(tuple, groupByDimension, goalFieldValuesByFieldValueId))
            .collect(Collectors.toCollection(() -> new TreeSet<>(
                Comparator.comparing(GoalAggregateRecord::getId).thenComparing(GoalAggregateRecord::getGoalFieldValueId)
                    .thenComparing(GoalAggregateRecord::getName))));

    Set<GoalAggregateRecord> goalAggregateRecordsForNonMatchingValues = goalFieldValues
        .stream()
        .map(goalFieldValue -> {
          var percentage = goalFieldValue.getValue() > 0.0 ? (0.0 / goalFieldValue.getValue()) * 100 : 0.0;
          GoalCount goalCount = new GoalCount(0, goalFieldValue.getValue(), percentage);
          return new GoalAggregateRecord(goalFieldValue.getFieldValueId(), goalFieldValue.getFieldValueName(),
              List.of(goalCount)).withGoalFieldValueId(goalFieldValue.getId());
        }).
        collect(Collectors.toCollection(() -> new TreeSet<>(
            Comparator.comparing(GoalAggregateRecord::getId).thenComparing(GoalAggregateRecord::getGoalFieldValueId)
                .thenComparing(GoalAggregateRecord::getName))));

    goalAggregateRecords.addAll(goalAggregateRecordsForNonMatchingValues);
    return ObjectUtils.isEmpty(dimensionAsFilter) ? new ArrayList<>(goalAggregateRecords)
        : getFilteredAggregateRecords(goalAggregateRecords, dimensionAsFilter);
  }

  private GoalAggregateResponse prorate(GoalAggregateResponse goalAggregateResponse, GoalDetail goalDetail) {
    if (goalDetail.isProrated()) {
      return prorateHandler.prorate(goalAggregateResponse, goalDetail, new RollingDateFilter());
    }
    return goalAggregateResponse;
  }

  private <T extends Fact> List<GoalAggregateRecord> getFilteredAggregateRecords(Set<GoalAggregateRecord> goalAggregateRecords,
      FactFilter<T> dimensionAsFilter) {
    Operator operator = dimensionAsFilter.getOperator();
    Object value = getValue(dimensionAsFilter);
    if (ObjectUtils.isEmpty(value)) {
      return new ArrayList<>(goalAggregateRecords);
    }

    if (operator == Operator.equal) {
      Number id = (Number) value;
      return goalAggregateRecords.stream()
          .filter(goalAggregateRecord -> goalAggregateRecord.getId().equals(id.longValue()))
          .collect(toList());
    }
    if (operator == Operator.not_equal) {
      Number id = (Number) value;
      return goalAggregateRecords.stream()
          .filter(goalAggregateRecord -> !goalAggregateRecord.getId().equals(id.longValue()))
          .collect(toList());
    }
    if (operator == Operator.in) {
      List<Long> ids = getParameters(value);
      return goalAggregateRecords.stream()
          .filter(goalAggregateRecord -> ids.contains(goalAggregateRecord.getId()))
          .collect(toList());
    }
    if (operator == Operator.not_in) {
      List<Long> ids = getParameters(value);
      return goalAggregateRecords.stream()
          .filter(goalAggregateRecord -> !ids.contains(goalAggregateRecord.getId()))
          .collect(toList());
    }
    return new ArrayList<>(goalAggregateRecords);
  }

  private <T extends Fact> Object getValue(FactFilter<T> dimensionAsFilter) {
    if (!"participants".equals(dimensionAsFilter.getDimension().getName())) {
      return dimensionAsFilter.getFilter().getValue();
    }

    Object value = dimensionAsFilter.getFilter().getValue();
    try {
      if (value instanceof List) {
        return getListOfIdNameFrom(value).stream().map(IdName::getId).collect(toList());
      }
      IdName idNameValue = objectMapper.readValue(objectMapper.writeValueAsString(value), IdName.class);
      return idNameValue.getId();
    } catch (Exception e) {
      log.error("Exception while getting participants value, {}", value);
      return null;
    }
  }

  private List<IdName> getListOfIdNameFrom(Object value) {
    try {
      return objectMapper.readValue(objectMapper.writeValueAsString(value), new TypeReference<>() {
        @Override
        public Type getType() {
          return super.getType();
        }
      });
    } catch (JsonProcessingException e) {
      log.error("Exception while serializing value {}", value);
      throw new IllegalArgumentException();
    }
  }

  private List<Long> getParameters(Object value) {
    if (value instanceof List) {
      List<Number> values = (List<Number>) value;
      return values.stream()
          .map(Number::longValue)
          .collect(toList());
    }
    return Arrays.stream(String.valueOf(value).split(","))
        .map(String::trim)
        .map(NumberUtils::createNumber)
        .map(Number::longValue)
        .collect(toList());
  }

  @Override
  public <T extends Fact> List<AggregateRecord> getAggregateResult(Specification<T> withSpecification,
      List<GroupByDimension<T>> groupByDimensions,
      List<BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>>> metricFunctions,
      Class<T> clazz, String timezone) {
    List<Tuple> resultList = getTuples(withSpecification, groupByDimensions, metricFunctions, clazz, timezone);

    boolean isTimeBasedDimensionPresent =
        groupByDimensions.stream()
            .anyMatch(groupByDimension -> ObjectUtils.isNotEmpty(groupByDimension.getFormat()));

    Map<Aggregate, AggregateRecord> recordMap = new LinkedHashMap<>();
    resultList.stream()
        .filter(Objects::nonNull)
        .forEach(tuple -> tupleToAggregateRecordsWithValue(recordMap, tuple, groupByDimensions, metricFunctions.size(), isTimeBasedDimensionPresent)
        );
    List<AggregateRecord> aggregateRecords = new ArrayList<>(recordMap.values());
    if (isTimeBasedDimensionPresent) {
      aggregateRecords.sort(this::orderByDate);
      return aggregateRecords;
    }
    aggregateRecords.sort(Collections.reverseOrder(this::compare));
    return aggregateRecords;
  }

  private int compare(AggregateRecord aggregateRecordOne, AggregateRecord aggregateRecordTwo) {
    double aggregateRecordOneTotal = getMaxValue(aggregateRecordOne).doubleValue();
    double aggregateRecordTwoTotal = getMaxValue(aggregateRecordTwo).doubleValue();

    return Double.compare(aggregateRecordOneTotal, aggregateRecordTwoTotal);
  }

  private int orderByDate(AggregateRecord aggregateRecordOne, AggregateRecord aggregateRecordTwo) {
    if (ObjectUtils.isEmpty(aggregateRecordOne.getOrderByDate()) || ObjectUtils.isEmpty(aggregateRecordTwo.getOrderByDate())) {
      return 0;
    }
    return aggregateRecordOne.getOrderByDate().compareTo(aggregateRecordTwo.getOrderByDate());
  }

  private Number getMaxValue(AggregateRecord aggregateRecordOne) {
    return aggregateRecordOne.getValues()
        .stream()
        .max(Comparator.comparing(Number::doubleValue))
        .orElse(0.0);
  }

  private <T extends Fact> List<Tuple> getTuples(Specification<T> withSpecification, List<GroupByDimension<T>> groupByDimensions,
      List<BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>>> metricFunctions, Class<T> clazz, String timezone) {
    var criteriaBuilder = entityManager.getCriteriaBuilder();
    var query = criteriaBuilder.createQuery(Tuple.class);
    var root = query.from(clazz);

    List<Selection> selectedColumns = new ArrayList<>();
    List<Expression<?>> groupByExpressions = new ArrayList<>();
    groupByDimensions
        .forEach(groupByDimension -> groupByExpressions.addAll(groupByDimension.getGroupByPath(root, query, criteriaBuilder, timezone)));

    selectedColumns.addAll(groupByExpressions);
    metricFunctions
        .forEach(metricFn -> selectedColumns.add(metricFn.apply(root, criteriaBuilder)));
    query.where(withSpecification.toPredicate(root, query, criteriaBuilder));
    query.multiselect(selectedColumns.toArray(new Selection[selectedColumns.size()]));
    query.groupBy(groupByExpressions);
    return entityManager.createQuery(query).getResultList();
  }

  private <T extends Fact> List<Tuple> getGoalTuples(Specification<T> withSpecification, GroupByDimension<T> groupByDimension,
      List<BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>>> metricFunctions, Class<T> clazz, String timezone,
      GoalDetail goalDetail, Set<GoalFieldValue> goalFieldValues) {

    Set<Long> fieldValues = goalFieldValues.stream()
        .map(GoalFieldValue::getFieldValueId)
        .collect(Collectors.toSet());

    var criteriaBuilder = entityManager.getCriteriaBuilder();
    var query = criteriaBuilder.createQuery(Tuple.class);
    var root = query.from(clazz);

    List<Selection> selectedColumns = new ArrayList<>();
    List<Expression<?>> groupByExpressions = new ArrayList<>();
    GoalGroupByDimensionDetail<T> goalGroupByDimensionDetail = groupByDimension.getGoalGroupByDimensionDetail(root, query, criteriaBuilder, timezone,
        goalDetail.withFieldValues(fieldValues));
    groupByExpressions.addAll(goalGroupByDimensionDetail.getGroupByExpressions());

    selectedColumns.addAll(groupByExpressions);
    metricFunctions
        .forEach(metricFn -> selectedColumns.add(metricFn.apply(root, criteriaBuilder)));
    query.groupBy(groupByExpressions);
    applyFilters(withSpecification, criteriaBuilder, query, root, goalGroupByDimensionDetail);
    query.multiselect(selectedColumns.toArray(new Selection[selectedColumns.size()]));
    return entityManager.createQuery(query).getResultList();
  }

  private <T extends Fact> void applyFilters(Specification<T> withSpecification, CriteriaBuilder criteriaBuilder, CriteriaQuery<Tuple> query,
      Root<T> root, GoalGroupByDimensionDetail<T> goalGroupByDimensionDetail) {
    if (goalGroupByDimensionDetail.isJoinPresent()) {
      query.where(criteriaBuilder.and(withSpecification.toPredicate(root, query, criteriaBuilder)), goalGroupByDimensionDetail.getGoalIdPredicate(),
          goalGroupByDimensionDetail.getGoalFieldNamePredicate());
      return;
    }
    query.where(criteriaBuilder.and(withSpecification.toPredicate(root, query, criteriaBuilder),
        goalGroupByDimensionDetail.getInFieldValues().toPredicate(root, query, criteriaBuilder)));
  }

  private <T extends Fact> GoalAggregateRecord tupleToGoalAggregateRecord(Tuple tuple, GroupByDimension<T> groupByDimension,
      Map<Long, GoalFieldValue> goalFieldValuesByFieldValueId) {
    GoalAggregate goalAggregate = groupByDimension.toGoalResultKey(tuple, 0, goalFieldValuesByFieldValueId);
    var achieved = groupByDimension.toResultValues(tuple, 1).get(0);
    var goal = goalAggregate.getGoal();
    var achievedNumber = ObjectUtils.isNotEmpty(achieved) ? achieved : 0;
    var percentage = goal.doubleValue() > 0.0 ? (achievedNumber.doubleValue() / goal.doubleValue()) * 100 : 0.0;
    GoalCount goalCount = new GoalCount(achievedNumber, goal, percentage);
    return new GoalAggregateRecord(goalAggregate.getId(), goalAggregate.getName(), List.of(goalCount)).withGoalFieldValueId(
        goalAggregate.getGoalFieldValueId());
  }

  private <T extends Fact> void tupleToAggregateRecords(Map<Aggregate, AggregateRecord> recordMap, Tuple tuple,
      List<GroupByDimension<T>> groupByDimensions, int metricsSize, boolean isTimeBasedDimensionPresent) {
    GroupByDimension<T> firstGroupByDimension = groupByDimensions.get(0);
    Aggregate aggregate = firstGroupByDimension.toResultKey(tuple, 0);
    AggregateRecord firstRecord = new AggregateRecord(aggregate.getId(), aggregate.getName(), new ArrayList<>())
        .withOrderByDate(aggregate.getOrderByDate()).withRecordDetails(aggregate.getRecordDetails());
    recordMap.putIfAbsent(aggregate, firstRecord);
    buildAggregateRecords(recordMap.get(aggregate), aggregate.getNextColumnNumber(), tuple, 1, groupByDimensions, metricsSize,
        isTimeBasedDimensionPresent);
  }

  private <T extends Fact> void buildAggregateRecords(AggregateRecord prevRecord, int nextColumnNumber, Tuple tuple,
      int groupByDimensionNextIndex, List<GroupByDimension<T>> groupByDimensions, int metricsSize, boolean isTimeBasedDimensionPresent) {

    if (nextColumnNumber >= tuple.getElements().size() - metricsSize) {
      List<Number> values = groupByDimensions.get(groupByDimensions.size() - 1).toResultValues(tuple, metricsSize);
      prevRecord.updateValues(values);
      return;
    }

    GroupByDimension<T> groupByDimension = groupByDimensions.get(groupByDimensionNextIndex);
    Aggregate aggregate = groupByDimension.toResultKey(tuple, nextColumnNumber);
    addDimensionIfNotPresent(prevRecord, aggregate, isTimeBasedDimensionPresent);
    groupByDimensionNextIndex = groupByDimensionNextIndex + 1;
    buildAggregateRecords(prevRecord.getAggregateRecordById(aggregate), aggregate.getNextColumnNumber(), tuple, groupByDimensionNextIndex,
        groupByDimensions, metricsSize, isTimeBasedDimensionPresent);
  }

  private void addDimensionIfNotPresent(AggregateRecord prevRecord, Aggregate aggregate, boolean isTimeBasedDimensionPresent) {
    if (!prevRecord.containsKey(aggregate)) {
      AggregateRecord record = new AggregateRecord(aggregate.getId(), aggregate.getName(), new ArrayList<>())
          .withOrderByDate(aggregate.getOrderByDate()).withRecordDetails(aggregate.getRecordDetails());
      prevRecord.addDimensionToMap(aggregate, record);
      prevRecord.addDimension(record, isTimeBasedDimensionPresent);
    }
  }

  private <T extends Fact> void tupleToAggregateRecordsWithValue(Map<Aggregate, AggregateRecord> recordMap, Tuple tuple,
      List<GroupByDimension<T>> groupByDimensions, int metricsSize, boolean isTimeBasedDimensionPresent) {
    GroupByDimension<T> firstGroupByDimension = groupByDimensions.get(0);
    Aggregate aggregate = firstGroupByDimension.toResultKey(tuple, 0);
    AggregateRecord firstRecord = new AggregateRecord(aggregate.getId(), aggregate.getName(), new ArrayList<>())
        .withOrderByDate(aggregate.getOrderByDate()).withRecordDetails(aggregate.getRecordDetails());
    recordMap.putIfAbsent(aggregate, firstRecord);
    buildAggregateRecordsWithValue(recordMap.get(aggregate), aggregate.getNextColumnNumber(), tuple, 1, groupByDimensions, metricsSize,
        isTimeBasedDimensionPresent);
  }

  private <T extends Fact> void buildAggregateRecordsWithValue(AggregateRecord prevRecord, int nextColumnNumber, Tuple tuple,
      int groupByDimensionNextIndex, List<GroupByDimension<T>> groupByDimensions, int metricsSize, boolean isTimeBasedDimensionPresent) {

    if (nextColumnNumber >= tuple.getElements().size() - metricsSize) {
      prevRecord
          .updateValues(prevRecord.getValues(), groupByDimensions.get(groupByDimensions.size() - 1).toResultValues(tuple, metricsSize));
      return;
    }
    prevRecord
        .updateValues(prevRecord.getValues(), groupByDimensions.get(groupByDimensions.size() - 1).toResultValues(tuple, metricsSize));
    GroupByDimension<T> groupByDimension = groupByDimensions.get(groupByDimensionNextIndex);
    Aggregate aggregate = groupByDimension.toResultKey(tuple, nextColumnNumber);
    addDimensionWithValueIfNotPresent(prevRecord, aggregate, isTimeBasedDimensionPresent);
    groupByDimensionNextIndex = groupByDimensionNextIndex + 1;
    buildAggregateRecordsWithValue(prevRecord.getAggregateRecordById(aggregate), aggregate.getNextColumnNumber(), tuple, groupByDimensionNextIndex,
        groupByDimensions, metricsSize, isTimeBasedDimensionPresent);
  }

  private void addDimensionWithValueIfNotPresent(AggregateRecord prevRecord, Aggregate aggregate, boolean isTimeBasedDimensionPresent) {
    if (!prevRecord.containsKey(aggregate)) {
      AggregateRecord record = new AggregateRecord(aggregate.getId(), aggregate.getName(), new ArrayList<>())
          .withOrderByDate(aggregate.getOrderByDate()).withRecordDetails(aggregate.getRecordDetails());
      prevRecord.addDimensionToMap(aggregate, record);
      prevRecord.addAndSortDimensions(record, isTimeBasedDimensionPresent);
    }
  }


}
