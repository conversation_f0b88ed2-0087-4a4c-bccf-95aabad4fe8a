package com.sling.sales.report.core.api;

import com.sling.sales.report.cache.CacheFacade;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.report.ReportType;
import com.sling.sales.report.dto.HeaderDetail;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class ReportHeaderBuilderCacheService {

  private final ReportHeaderBuilder reportHeaderBuilder;
  private final CacheFacade cacheFacade;

  @Autowired
  public ReportHeaderBuilderCacheService(ReportHeaderBuilder reportHeaderBuilder, CacheFacade cacheFacade) {
    this.reportHeaderBuilder = reportHeaderBuilder;
    this.cacheFacade = cacheFacade;
  }

  public <T extends Fact> HeaderDetail buildHeaders(long tenantId, String entityType, ReportType reportType,
      List<GroupByField> groupBy,
      List<Metric> metrics,
      Class<T> clazz,
      String authenticationToken, long reportId, Filter filter) {
    HeaderDetail reportHeaderDetail = cacheFacade.getReportHeaderDetail(tenantId, entityType, reportId);
    if (reportHeaderDetail != null) {
      return reportHeaderDetail;
    }
    HeaderDetail headerDetail1 = reportHeaderBuilder.buildHeaders(tenantId, reportType, groupBy, metrics, clazz, authenticationToken, filter);
    cacheFacade.putReportHeaderDetail(tenantId, entityType, headerDetail1, reportId);
    return headerDetail1;

  }


  public void refreshHeaderBuilder(long tenantId, String entityType, long reportId) {
    if (reportId == 0) {
      return;
    }
    cacheFacade.refreshReportHeader(tenantId, entityType, reportId);

  }
}
