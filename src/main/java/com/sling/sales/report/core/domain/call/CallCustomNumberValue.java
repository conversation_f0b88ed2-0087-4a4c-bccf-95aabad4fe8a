package com.sling.sales.report.core.domain.call;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor
public class CallCustomNumberValue implements EntityDimension {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private long id;
  @Column(name = "field_id")
  private long fieldId;
  @Column(name = "value")
  private Double value;


  @Column(name = "call_id")
  private Long callId;

  public CallCustomNumberValue(long fieldId, Double value, Long callId) {
    this.fieldId = fieldId;
    this.value = value;
    this.callId = callId;
  }
}
