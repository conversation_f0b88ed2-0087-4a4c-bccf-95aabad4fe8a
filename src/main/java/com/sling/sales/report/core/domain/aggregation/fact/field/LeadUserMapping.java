package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.lead.Lead;
import com.sling.sales.report.security.domain.User;
import javax.persistence.metamodel.SingularAttribute;
import lombok.Getter;

@Getter
public class LeadUserMapping {

  private final String mappedColumnFieldName;
  private final SingularAttribute<Lead, User> mappedColumnFieldPath;

  public LeadUserMapping(String mappedColumnFieldName, SingularAttribute<Lead, User> mappedColumnFieldPath) {
    this.mappedColumnFieldName = mappedColumnFieldName;
    this.mappedColumnFieldPath = mappedColumnFieldPath;
  }
}
