package com.sling.sales.report.core.domain.aggregation.dimension;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

@Getter
public class DateTimeParser {

  public static Date parseDate(Object value) {
    var dateString = String.valueOf(value);
      return Date.from(Instant.parse(dateString));
  }

  public static Date endDateWithCustomTime(LocalDate end, ZoneId zoneId, String to) {
    if (ObjectUtils.isEmpty(to)) {
      return Date.from(end.atTime(LocalTime.MAX).atZone(zoneId).toInstant());
    }
    LocalTime toTime = LocalTime.parse(to).plusSeconds(60).minusNanos(1);
    return Date.from(end.atTime(toTime).atZone(zoneId).toInstant());
  }

  public static Date startDateWithCustomTime(LocalDate start, ZoneId zoneId, String from) {
    if (ObjectUtils.isEmpty(from)) {
      return Date.from(start.atStartOfDay(zoneId).toInstant());
    }
    LocalTime fromTime = LocalTime.parse(from);
    return Date.from(start.atTime(fromTime).atZone(zoneId).toInstant());
  }
}
