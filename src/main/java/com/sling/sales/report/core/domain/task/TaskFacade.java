package com.sling.sales.report.core.domain.task;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.core.domain.field.Field;
import com.sling.sales.report.core.domain.field.FieldFacade;
import com.sling.sales.report.core.domain.field.FieldType;
import com.sling.sales.report.core.domain.field.PicklistValue;
import com.sling.sales.report.core.domain.field.PicklistValueFacade;
import com.sling.sales.report.dto.FieldDetail;
import com.sling.sales.report.error.JsonParseException;
import com.sling.sales.report.mq.event.DeleteRecordsEvent;
import com.sling.sales.report.mq.event.IdName;
import com.sling.sales.report.mq.event.PicklistValueDetail;
import com.sling.sales.report.mq.event.TaskDeletedEvent;
import com.sling.sales.report.mq.event.TaskEvent;
import com.sling.sales.report.mq.event.TaskEvent.EventEntity;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.UserFacade;
import java.lang.reflect.Type;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeParseException;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class TaskFacade {

  private final TaskRepository taskRepository;
  private final UserFacade userFacade;
  private final ObjectMapper objectMapper;
  private final FieldFacade fieldFacade;
  private final TaskCustomPicklistValueRepository taskCustomPicklistValueRepository;
  private final PicklistValueFacade picklistValueFacade;
  private final TaskPriorityRepository taskPriorityRepository;
  private final TaskStatusRepository taskStatusRepository;
  private final TaskTypeRepository taskTypeRepository;
  private final TaskRelatedToRepository taskRelatedToRepository;

  @Autowired
  public TaskFacade(TaskRepository taskRepository, UserFacade userFacade, ObjectMapper objectMapper,
      FieldFacade fieldFacade, TaskCustomPicklistValueRepository taskCustomPicklistValueRepository,
      PicklistValueFacade picklistValueFacade, TaskPriorityRepository taskPriorityRepository,
      TaskStatusRepository taskStatusRepository, TaskTypeRepository taskTypeRepository, TaskRelatedToRepository taskRelatedToRepository) {
    this.taskRepository = taskRepository;
    this.userFacade = userFacade;
    this.objectMapper = objectMapper;
    this.fieldFacade = fieldFacade;
    this.taskCustomPicklistValueRepository = taskCustomPicklistValueRepository;
    this.picklistValueFacade = picklistValueFacade;
    this.taskPriorityRepository = taskPriorityRepository;
    this.taskStatusRepository = taskStatusRepository;
    this.taskTypeRepository = taskTypeRepository;
    this.taskRelatedToRepository = taskRelatedToRepository;
  }

  public void create(TaskEvent event, HashMap<String, Object> payload, boolean deleted) {
    Optional<Task> optionalTask = taskRepository
        .findById(event.getEntity().getId());
    if (optionalTask.isPresent()) {
      return;
    }
    createTask(event, payload, deleted);
  }

  private void createTask(TaskEvent event, HashMap<String, Object> payload, boolean deleted) {
    var task = new Task();
    task.setId(event.getEntity().getId());
    task.setTenantId(event.getEntity().getTenantId());
    updateTaskWithEventInformation(event.getEntity(), payload, task, deleted);
  }

  @Transactional
  public void update(TaskEvent event, HashMap<String, Object> payload, boolean deleted) {
    Optional<Task> optionalTask = taskRepository
        .findById(event.getEntity().getId());
    if (optionalTask.isPresent() && (optionalTask.get().isDeleted() ||
        event.getEntity().getUpdatedAt().before(optionalTask.get().getUpdatedAt()))) {
      return;
    }
    optionalTask.ifPresentOrElse(
        existing -> updateTaskWithEventInformation(event.getEntity(), payload, existing, deleted),
        () -> createTask(event, payload, false));
  }

  public void delete(TaskDeletedEvent event) {
    var tenantId = event.getTenantId();
    var id = event.getId();
    taskRepository
        .findByTenantIdAndId(tenantId, id)
        .ifPresentOrElse(task -> taskRepository.deleteByTenantIdAndId(tenantId, id), () -> createTaskOnDeleteIfNotExists(event));
  }

  private void createTaskOnDeleteIfNotExists(TaskDeletedEvent event) {
    TaskEvent taskEvent = new TaskEvent();
    EventEntity eventEntity = new EventEntity();
    eventEntity.setId(event.getId());
    eventEntity.setTenantId(event.getTenantId());
    eventEntity.setDueDate(Date.from(Instant.now()));
    eventEntity.setAssignedTo(new IdName(event.getTenantId(), "AssignedTo"));
    eventEntity.setOwner(new IdName(event.getTenantId(), "Owner"));
    eventEntity.setCreatedBy(new IdName(event.getTenantId(), "CreatedBy User"));
    eventEntity.setUpdatedBy(new IdName(event.getTenantId(), "UpdatedBy User"));
    eventEntity.setCreatedAt(Date.from(Instant.now()));
    eventEntity.setUpdatedAt(Date.from(Instant.now()));
    taskEvent.setEntity(eventEntity);
    try {
      HashMap<String, Object> payload = objectMapper.readValue(
          objectMapper.writeValueAsString(taskEvent.getEntity()),
          new TypeReference<>() {
            @Override
            public Type getType() {
              return super.getType();
            }
          });
      createTask(taskEvent, payload, true);
    } catch (JsonProcessingException e) {
      throw new JsonParseException(e);
    }
  }

  private void updateTaskWithEventInformation(EventEntity entity, HashMap<String, Object> payload, Task task, boolean deleted) {
    var tenantId = entity.getTenantId();
    task.setName(entity.getName());
    var owner = new User(entity.getOwner().getId(), tenantId, entity.getOwner().getName());
    task.setOwner(userFacade.getExistingOrCreateNewUser(owner));
    var assignedTo = new User(entity.getAssignedTo().getId(), tenantId, entity.getAssignedTo().getName());
    task.setAssignedTo(userFacade.getExistingOrCreateNewUser(assignedTo));
    var createdBy = new User(entity.getCreatedBy().getId(), tenantId, entity.getCreatedBy().getName());
    task.setCreatedBy(userFacade.getExistingOrCreateNewUser(createdBy));
    var updatedBy = new User(entity.getUpdatedBy().getId(), tenantId, entity.getUpdatedBy().getName());
    task.setUpdatedBy(userFacade.getExistingOrCreateNewUser(updatedBy));
    task.setDueDate(entity.getDueDate());
    task.setCreatedAt(entity.getCreatedAt());
    task.setUpdatedAt(entity.getUpdatedAt());
    task.setCompletedAt(entity.getCompletedAt());
    task.setOriginalDueDate(entity.getOriginalDueDate());
    task.setTaskStatus(getTaskStatus(entity, tenantId));
    task.setTaskType(getTaskType(entity, tenantId));
    task.setTaskPriority(getTaskPriority(entity, tenantId));
    task.setDeleted(deleted);
    task.addTaskRelatedTo(getTaskRelatedTo(task, entity));

    var taskEventData = new TaskEventData();
    taskEventData.putAll(payload);
    task.setEventPayload(taskEventData);
    Map<String, Object> customFieldValues =
        entity.getCustomFieldValues() == null ? Collections.emptyMap() : entity.getCustomFieldValues();
    Map<FieldType, List<FieldDetail>> fieldDetails = getFieldDetailsByTenantId(entity.getTenantId(), customFieldValues);
    addCustomFieldValues(fieldDetails, task);
    taskRepository.save(task);
  }

  private Set<TaskRelatedTo> getTaskRelatedTo(Task task, TaskEvent.EventEntity taskDetail) {
    return Optional.ofNullable(taskDetail.getRelatedTo())
        .orElse(Collections.emptyList())
        .stream()
        .map(relatedTo ->
            new TaskRelatedTo(task.getId(), relatedTo.getEntityId(), relatedTo.getEntityType(),
                relatedTo.getEntityName(),relatedTo.getOwnerId(), taskDetail.getTenantId())
        )
        .collect(Collectors.toSet());
  }

  private void addCustomFieldValues(Map<FieldType, List<FieldDetail>> fieldDetails, Task task) {
    task.addTaskPicklistValues(getTaskPicklistValues(fieldDetails, task));
    task.addTaskCustomTextValues(getTaskCustomTextValues(fieldDetails, task));
    task.addTaskCustomNumberValues(getTaskCustomNumberValues(fieldDetails, task));
    task.addTaskCustomDatePickerValues(getTaskCustomDatePickerValues(fieldDetails, task));
    task.addTaskCustomDatetimePickerValues(getTaskCustomDatetimePickerValues(fieldDetails, task));
    task.addTaskCustomCheckboxValues(getTaskCustomCheckboxValues(fieldDetails, task));
  }

  private Map<FieldType, List<FieldDetail>> getFieldDetailsByTenantId(long tenantId, Map<String, Object> customFieldValues) {
    List<Field> fields = fieldFacade.getFieldsByTenantIdAndEntityType(tenantId, EntityType.TASK);
    return fields
        .stream()
        .filter(field -> customFieldValues.containsKey(field.getName()))
        .map(field -> new FieldDetail(field, customFieldValues.get(field.getName())))
        .collect(Collectors.groupingBy(fieldDetail -> fieldDetail.getField().getType()));
  }

  private Set<TaskCustomPicklistValue> getTaskPicklistValues(Map<FieldType, List<FieldDetail>> fieldDetails, Task task) {
    if (!fieldDetails.containsKey(FieldType.PICK_LIST)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.PICK_LIST)
        .stream()
        .map(fieldDetail -> {
          Long picklistValueId = new ObjectMapper().convertValue(fieldDetail.getValue(), Long.class);
          Optional<PicklistValue> picklistValueOptional = picklistValueFacade
              .getPicklistValueByFieldIdAndPicklistValueId(fieldDetail.getField().getId(), picklistValueId);
          return picklistValueOptional
              .map(picklistValue -> new TaskCustomPicklistValue(fieldDetail.getField().getId(), picklistValueId, picklistValue.getDisplayName(),
                  task))
              .orElse(null);
        }).filter(Objects::nonNull)
        .collect(Collectors.toSet());
  }

  private Set<TaskCustomTextValue> getTaskCustomTextValues(Map<FieldType, List<FieldDetail>> fieldDetails, Task task) {
    if (!fieldDetails.containsKey(FieldType.TEXT_FIELD)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.TEXT_FIELD)
        .stream()
        .map(fieldDetail -> {
          String valueToBeSaved = fieldDetail.getValue() == null ? null : String.valueOf(fieldDetail.getValue());
          return new TaskCustomTextValue(fieldDetail.getField().getId(), valueToBeSaved, task);
        }).collect(Collectors.toSet());
  }

  private Set<TaskCustomNumberValue> getTaskCustomNumberValues(Map<FieldType, List<FieldDetail>> fieldDetails, Task task) {
    if (!fieldDetails.containsKey(FieldType.NUMBER)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.NUMBER)
        .stream()
        .map(fieldDetail -> {
          Double valueToBeSaved = fieldDetail.getValue() == null ? null : Double.parseDouble(String.valueOf(fieldDetail.getValue()));
          return new TaskCustomNumberValue(fieldDetail.getField().getId(), valueToBeSaved, task);
        }).collect(Collectors.toSet());
  }

  private Set<TaskCustomDatePickerValue> getTaskCustomDatePickerValues(Map<FieldType, List<FieldDetail>> fieldDetails, Task task) {
    if (!fieldDetails.containsKey(FieldType.DATE_PICKER)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.DATE_PICKER)
        .stream()
        .map(fieldDetail -> getTaskCustomDatePickerValue(task, fieldDetail))
        .collect(Collectors.toSet());
  }

  private Set<TaskCustomDatetimePickerValue> getTaskCustomDatetimePickerValues(Map<FieldType, List<FieldDetail>> fieldDetails,
      Task task) {
    if (!fieldDetails.containsKey(FieldType.DATETIME_PICKER)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.DATETIME_PICKER)
        .stream()
        .map(fieldDetail -> getTaskCustomDatetimePickerValue(task, fieldDetail))
        .collect(Collectors.toSet());
  }

  private Set<TaskCustomCheckboxValue> getTaskCustomCheckboxValues(Map<FieldType, List<FieldDetail>> fieldDetails, Task task) {
    if (!fieldDetails.containsKey(FieldType.CHECKBOX)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.CHECKBOX)
        .stream()
        .map(fieldDetail -> {
          Boolean valueToBeSaved = fieldDetail.getValue() == null ? Boolean.FALSE : Boolean.parseBoolean(String.valueOf(fieldDetail.getValue()));
          return new TaskCustomCheckboxValue(fieldDetail.getField().getId(), valueToBeSaved, task);
        }).collect(Collectors.toSet());
  }

  private TaskCustomDatePickerValue getTaskCustomDatePickerValue(Task task, FieldDetail fieldDetail) {
    if (fieldDetail.getValue() == null) {
      return null;
    }
    try {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString()).toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new TaskCustomDatePickerValue(fieldDetail.getField().getId(), valueToBeSaved, task);
    } catch (DateTimeParseException pe) {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString().substring(0, 23) + "Z").toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new TaskCustomDatePickerValue(fieldDetail.getField().getId(), valueToBeSaved, task);
    }
  }

  private TaskCustomDatetimePickerValue getTaskCustomDatetimePickerValue(Task task, FieldDetail fieldDetail) {
    if (fieldDetail.getValue() == null) {
      return null;
    }
    try {
      java.util.Date valueToBeSaved = java.util.Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString()).toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new TaskCustomDatetimePickerValue(fieldDetail.getField().getId(), valueToBeSaved, task);
    } catch (DateTimeParseException pe) {
      java.util.Date valueToBeSaved = java.util.Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString().substring(0, 23) + "Z").toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new TaskCustomDatetimePickerValue(fieldDetail.getField().getId(), valueToBeSaved, task);
    }
  }

  private TaskStatus getTaskStatus(EventEntity entity, Long tenantId) {
    return Objects.isNull(entity.getStatus())
        ? null
        : taskStatusRepository.findById(entity.getStatus().getId())
            .orElseGet(() -> {
              TaskStatus taskStatus = new TaskStatus(entity.getStatus().getId(), entity.getStatus().getName(), tenantId);
              return taskStatusRepository.saveAndFlush(taskStatus);
            });
  }

  private TaskPriority getTaskPriority(EventEntity entity, Long tenantId) {
    return Objects.isNull(entity.getPriority())
        ? null
        : taskPriorityRepository.findById(entity.getPriority().getId())
            .orElseGet(() -> {
              TaskPriority taskPriority = new TaskPriority(entity.getPriority().getId(), entity.getPriority().getName(), tenantId);
              return taskPriorityRepository.saveAndFlush(taskPriority);
            });
  }

  private TaskType getTaskType(EventEntity entity, Long tenantId) {
    return Objects.isNull(entity.getType())
        ? null
        : taskTypeRepository.findById(entity.getType().getId())
            .orElseGet(() -> {
              TaskType taskType = new TaskType(entity.getType().getId(), entity.getType().getName(), tenantId);
              return taskTypeRepository.saveAndFlush(taskType);
            });
  }

  public void updateDisplayNameByPicklistValueId(PicklistValueDetail picklistValueDetail) {
    taskCustomPicklistValueRepository.updateDisplayNameByPicklistValueId(picklistValueDetail.getId(), picklistValueDetail.getDisplayName());
  }

  @EventListener
  public void deleteSoftDeletedRecords(DeleteRecordsEvent deleteRecordsEvent) {
    taskRepository.deleteRecordsOlderThan10Days();
  }

  public void updateTaskRelatedToNameByEntityIdAndEntity(long tenantId, long entityId, String entity, String name) {
    taskRelatedToRepository.updateEntityNameByEntityIdAndEntity(tenantId, entityId, entity, name);
  }

  public void updateTaskRelatedToOwnerIdByEntityIdAndEntity(long tenantId, long ownerId, long entityId, String entity) {
    taskRelatedToRepository.updateOwnerIdByEntityIdAndEntity(tenantId, entityId, entity, ownerId);
  }
}
