package com.sling.sales.report.core.domain.aggregation.fact.field;

import static java.util.Collections.emptyList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.domain.aggregation.dimension.BooleanDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.DateDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdAssociatedEntityFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.TextDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.UserPropertyEmailRecipientDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.UserPropertyEmailSenderDimension;
import com.sling.sales.report.core.domain.email.Email;
import com.sling.sales.report.core.domain.email.EmailClickedAt;
import com.sling.sales.report.core.domain.email.EmailClickedAt_;
import com.sling.sales.report.core.domain.email.EmailOpenedAt;
import com.sling.sales.report.core.domain.email.EmailOpenedAt_;
import com.sling.sales.report.core.domain.email.EmailRelatedTo;
import com.sling.sales.report.core.domain.email.EmailRelatedTo_;
import com.sling.sales.report.core.domain.email.Email_;
import com.sling.sales.report.dto.DimensionDetail;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;

@Service
class EmailFieldFactory extends FactFieldFactory<Email> {

  private final TextDimension<Email> status = new TextDimension<>(
      "status",
      root -> root.get(Email_.status.getName()),
      emptyList());

  private final TextDimension<Email> subject = new TextDimension<>(
      "subject",
      root -> root.get(Email_.subject.getName()),
      emptyList());


  private final TextDimension<Email> direction = new TextDimension<>(
      "direction",
      root -> root.get(Email_.direction.getName()),
      emptyList());

  private final ManyIdAssociatedEntityFilterDimension<Email, EmailRelatedTo> associatedLeads = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedLeads",
      Email_.EMAIL_RELATED_TO,
      Email_.emailRelatedTo,
      join -> join.get(EmailRelatedTo_.entityId),
      join -> join.get(EmailRelatedTo_.entity),
      join -> join.get(EmailRelatedTo_.entityName),
      "lead"
  );


  private final ManyIdAssociatedEntityFilterDimension<Email, EmailRelatedTo> associatedContacts = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedContacts",
      Email_.EMAIL_RELATED_TO,
      Email_.emailRelatedTo,
      join -> join.get(EmailRelatedTo_.entityId),
      join -> join.get(EmailRelatedTo_.entity),
      join -> join.get(EmailRelatedTo_.entityName),
      "contact"
  );

  private final ManyIdAssociatedEntityFilterDimension<Email, EmailRelatedTo> associatedDeals = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedDeals",
      Email_.EMAIL_RELATED_TO,
      Email_.emailRelatedTo,
      join -> join.get(EmailRelatedTo_.entityId),
      join -> join.get(EmailRelatedTo_.entity),
      join -> join.get(EmailRelatedTo_.entityName),
      "deal"
  );


  private final BooleanDimension<Email> attachments = new BooleanDimension<>(
      "attachments",
      root -> root.get(Email_.attachment)
  );

  private final DateDimension<Email> mailedAt = new DateDimension<>("createdAt", root -> root.get(Email_.sentAt));

  private final DateDimension<Email> clickedAt = new DateDimension<>(
      "clickedAt",
      root -> getOrCreateLeftJoinClickedAt(root, EmailClickedAt_.CLICKED_AT).get(EmailClickedAt_.clickedAt));

  private final DateDimension<Email> openedAt = new DateDimension<>(
      "openedAt",
      root -> getOrCreateLeftJoinOpenedAt(root, EmailOpenedAt_.OPENED_AT).get(EmailOpenedAt_.openedAt));


  private final Map<String, GroupByDimension<Email>> SUPPORTED_GROUP_BY_DIMENSIONS = new HashMap<>() {
    {
      put(status.getName(), status);
      put(subject.getName(), subject);
      put(direction.getName(), direction);
      put(attachments.getName(), attachments);
      put(mailedAt.getName(), mailedAt);
      put(clickedAt.getName(), clickedAt);
      put(openedAt.getName(), openedAt);
    }
  };

  private final Map<String, FilterDimension<Email>> SUPPORTED_FILTER_DIMENSIONS = new HashMap<>() {
    {
      put(status.getName(), status);
      put(associatedLeads.getName(), associatedLeads);
      put(associatedContacts.getName(), associatedContacts);
      put(associatedDeals.getName(), associatedDeals);
      put(mailedAt.getName(), mailedAt);
      put(subject.getName(), subject);
      put(attachments.getName(), attachments);
      put(clickedAt.getName(), clickedAt);
      put(openedAt.getName(), openedAt);
      put(direction.getName(), direction);
    }
  };

  private final List<Metric<Email>> SUPPORTED_METRICS = new ArrayList<>() {
    {
      add(new Metric<>(
          "id",
          MetricType.COUNT,
          (root, builder) -> builder.countDistinct(root),
          null));
    }
  };

  private Join<Email, EmailClickedAt> getOrCreateLeftJoinClickedAt(Root<Email> root, String dimensionName) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<Email, EmailClickedAt>) j))
        .findFirst()
        .orElseGet(() -> root.join(Email_.emailClickedAt, JoinType.LEFT));
  }

  private Join<Email, EmailOpenedAt> getOrCreateLeftJoinOpenedAt(Root<Email> root, String dimensionName) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<Email, EmailOpenedAt>) j))
        .findFirst()
        .orElseGet(() -> root.join(Email_.emailOpenedAt, JoinType.LEFT));
  }

  @Override
  public DimensionDetail<Email> getDimensionDetail() {
    return new DimensionDetail<>(SUPPORTED_GROUP_BY_DIMENSIONS, SUPPORTED_FILTER_DIMENSIONS, SUPPORTED_METRICS);
  }

  @Override
  public GroupByDimension<Email> createUserPropertyGroupByIdDimension(GroupByField groupByField) {
    if ("teams".equals(groupByField.getProperty())) {
      if ("sentBy".equals(groupByField.getPrimaryField())) {
        return UserPropertyEmailSenderDimension.getInstance(groupByField.getName(), groupByField.getProperty());
      }
      if ("receivedBy".equals(groupByField.getPrimaryField())) {
        return UserPropertyEmailRecipientDimension.getInstance(groupByField.getName(), groupByField.getProperty());
      }
    }
    return null;
  }

  @Override
  public FilterDimension<Email> createUserPropertyFilterIdDimension(Filter filter) {
    if ("teams".equals(filter.getProperty())) {
      if ("sentBy".equals(filter.getPrimaryField())) {
        return UserPropertyEmailSenderDimension.getInstance(filter.getFieldName(), filter.getProperty());
      }
      if ("receivedBy".equals(filter.getPrimaryField())) {
        return UserPropertyEmailRecipientDimension.getInstance(filter.getFieldName(), filter.getProperty());
      }
    }
    return null;
  }
}
