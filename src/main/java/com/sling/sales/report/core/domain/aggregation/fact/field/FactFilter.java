package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Operator;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public class FactFilter<T extends Fact> {

  private final Operator operator;
  private final FilterDimension<T> dimension;
  private final Filter filter;
  private final String timezone;

  public FactFilter(Operator operator, FilterDimension<T> dimension, Filter filter, String timezone) {
    this.operator = operator;
    this.dimension = dimension;
    this.filter = filter;
    this.timezone = timezone;
  }
}
