package com.sling.sales.report.core.domain.task;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface TaskRepository extends JpaRepository<Task, Long>, JpaSpecificationExecutor<Task> {

  Optional<Task> findByTenantIdAndId(Long tenantId, Long id);

  @Modifying(flushAutomatically = true)
  @Transactional
  void deleteByTenantIdAndId(Long tenantId, Long id);

  @Modifying(flushAutomatically = true)
  @Transactional
  @Query(value = "DELETE FROM task WHERE deleted = true AND updated_at < now() - INTERVAL '10 days'", nativeQuery = true)
  void deleteRecordsOlderThan10Days();
}
