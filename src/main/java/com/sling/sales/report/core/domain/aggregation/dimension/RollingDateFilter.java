package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.DateTimeParser.endDateWithCustomTime;
import static com.sling.sales.report.core.domain.aggregation.dimension.DateTimeParser.startDateWithCustomTime;
import static java.time.temporal.TemporalAdjusters.nextOrSame;
import static java.time.temporal.TemporalAdjusters.ofDateAdjuster;
import static java.time.temporal.TemporalAdjusters.previousOrSame;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.IsoFields;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
public class RollingDateFilter {

  public RollingDate from(Operator operator, Instant input, ZoneId zoneId, String from, String to) {
    LocalDateTime localDateTime = input.atZone(zoneId).toLocalDateTime();
    LocalDate now = localDateTime.toLocalDate();

    switch (operator) {
      case before_current_date_and_time:
        LocalDateTime beforeCurrentDateTimeUtc = LocalDateTime.ofInstant(input, ZoneId.of(ZoneOffset.UTC.getId()));
        LocalDateTime beforeCurrentDateTimeInTimeZone = beforeCurrentDateTimeUtc.atZone(zoneId).toLocalDateTime();
        return createRollingDateFilter(
            null,
            beforeCurrentDateTimeInTimeZone.toLocalDate(),
            beforeCurrentDateTimeInTimeZone.toLocalTime()
        );

      case after_current_date_and_time:
        LocalDateTime afterCurrentDateTimeUtc = LocalDateTime.ofInstant(input, ZoneId.of(ZoneOffset.UTC.getId()));
        LocalDateTime afterCurrentDateTimeInTimeZone = afterCurrentDateTimeUtc.atZone(zoneId).toLocalDateTime();
        return createRollingDateFilter(
            afterCurrentDateTimeInTimeZone.toLocalDate(),
            null,
            afterCurrentDateTimeInTimeZone.toLocalTime()
        );

      case today:
        return createRollingDateFilter(
            now.with(ofDateAdjuster(date -> LocalDate.from(date.atStartOfDay()))),
            now.with(ofDateAdjuster(date -> LocalDate.from(date.atStartOfDay()))),
            zoneId,
            from,
            to
        );

      case last_week:
        LocalDate lastWeek = now.minusWeeks(1);
        return createRollingDateFilter(
            lastWeek.with(previousOrSame(DayOfWeek.MONDAY)),
            lastWeek.with(nextOrSame(DayOfWeek.SUNDAY)),
            zoneId,
            from,
            to
        );

      case current_month:
        return createRollingDateFilter(
            now.with(TemporalAdjusters.firstDayOfMonth()),
            now.with(TemporalAdjusters.lastDayOfMonth()),
            zoneId,
            from,
            to
        );

      case last_month:
        LocalDate previousMonth = now.minusMonths(1);
        return createRollingDateFilter(
            previousMonth.with(TemporalAdjusters.firstDayOfMonth()),
            previousMonth.with(TemporalAdjusters.lastDayOfMonth()),
            zoneId,
            from,
            to
        );

      case current_quarter:
        int currentQuarter = now.get(IsoFields.QUARTER_OF_YEAR);
        return getRollingDateFilter(now, currentQuarter, zoneId, from, to);

      case last_quarter:
        int lastQuarter = now.get(IsoFields.QUARTER_OF_YEAR) - 1;
        if (lastQuarter == 0) {
          LocalDate lastYear = now.minusYears(1);
          return getRollingDateFilter(lastYear, 4, zoneId, from, to);
        }
        return getRollingDateFilter(now, lastQuarter, zoneId, from, to);

      case current_year:
        return createRollingDateFilter(
            now.with(TemporalAdjusters.firstDayOfYear()),
            now.with(TemporalAdjusters.lastDayOfYear()),
            zoneId,
            from,
            to
        );

      case last_year:
        LocalDate previousYear = now.minusYears(1);
        return createRollingDateFilter(
            previousYear.with(TemporalAdjusters.firstDayOfYear()),
            previousYear.with(TemporalAdjusters.lastDayOfYear()),
            zoneId,
            from,
            to
        );

      case next_week:
        LocalDate nextWeek = now.plusWeeks(1);
        return createRollingDateFilter(
            nextWeek.with(previousOrSame(DayOfWeek.MONDAY)),
            nextWeek.with(nextOrSame(DayOfWeek.SUNDAY)),
            zoneId,
            from,
            to
        );

      case next_month:
        LocalDate nextMonth = now.plusMonths(1);
        return createRollingDateFilter(
            nextMonth.with(TemporalAdjusters.firstDayOfMonth()),
            nextMonth.with(TemporalAdjusters.lastDayOfMonth()),
            zoneId,
            from,
            to
        );

      case next_quarter:
        int nextQuarter = now.get(IsoFields.QUARTER_OF_YEAR) + 1;
        if (nextQuarter == 5) {
          LocalDate nextYear = now.plusYears(1);
          return getRollingDateFilter(nextYear, 1, zoneId, from, to);
        }
        return getRollingDateFilter(now, nextQuarter, zoneId, from, to);

      case next_year:
        LocalDate nextYear = now.plusYears(1);
        return createRollingDateFilter(
            nextYear.with(TemporalAdjusters.firstDayOfYear()),
            nextYear.with(TemporalAdjusters.lastDayOfYear()),
            zoneId,
            from,
            to
        );
      case last_seven_days:
        return createRollingDateFilter(
            now.minusDays(7),
            now.minusDays(1),
                zoneId,
                from,
                to
        );
      case next_seven_days:
        return createRollingDateFilter(
                now.plusDays(1),
                now.plusDays(7),
                zoneId,
                from,
                to
        );

      case last_fifteen_days:
        return createRollingDateFilter(
            now.minusDays(15),
            now.minusDays(1),
            zoneId,
            from,
            to
        );

      case next_fifteen_days:
        return createRollingDateFilter(
            now.plusDays(1),
            now.plusDays(15),
            zoneId,
            from,
            to
        );

      case last_thirty_days:
        return createRollingDateFilter(
            now.minusDays(30),
            now.minusDays(1),
                zoneId,
                from,
                to
        );
      case next_thirty_days:
        return createRollingDateFilter(
                now.plusDays(1),
                now.plusDays(30),
                zoneId,
                from,
                to
        );

      case week_to_date:
        return createRollingDateFilter(
            now.with(DayOfWeek.MONDAY),
            now.atTime(LocalTime.MAX).toLocalDate(),
            zoneId,
            from,
            to
        );

      case month_to_date:
        return createRollingDateFilter(
            now.with(TemporalAdjusters.firstDayOfMonth()),
            now.atTime(LocalTime.MAX).toLocalDate(),
            zoneId,
            from,
            to
        );

      case quarter_to_date:
        return getRollingDateFilter(now, zoneId, from, to);
      case year_to_date:
        return createRollingDateFilter(
            now.with(TemporalAdjusters.firstDayOfYear()),
            now.atTime(LocalTime.MAX).toLocalDate(),
            zoneId,
            from,
            to
        );

      case yesterday:
        return createRollingDateFilter(
            now.minusDays(1),
            now.minusDays(1),
            zoneId,
            from,
            to
        );

      case tomorrow:
        return createRollingDateFilter(
            now.plusDays(1),
            now.plusDays(1),
            zoneId,
            from,
            to
        );

      default:
        return createRollingDateFilter(
            now.with(previousOrSame(DayOfWeek.MONDAY)),
            now.with(nextOrSame(DayOfWeek.SUNDAY)),
            zoneId,
            from,
            to
        );
    }
  }

  private static RollingDate getRollingDateFilter(LocalDate now, int quarter, ZoneId zoneId, String from, String to) {
    return createRollingDateFilter(
        now.withMonth(quarter * 3 - 2).with(TemporalAdjusters.firstDayOfMonth()),
        now.withMonth(quarter * 3).with(TemporalAdjusters.lastDayOfMonth()),
        zoneId,
        from,
        to
    );
  }

  private static RollingDate getRollingDateFilter(LocalDate now, ZoneId zoneId, String from, String to) {
    int quarter = now.get(IsoFields.QUARTER_OF_YEAR);
    return createRollingDateFilter(
        now.withMonth(quarter * 3 - 2).with(TemporalAdjusters.firstDayOfMonth()),
        now.atTime(LocalTime.MAX).toLocalDate(),
        zoneId,
        from,
        to
    );
  }

  private static RollingDate createRollingDateFilter(LocalDate start, LocalDate end, ZoneId zoneId, String from, String to) {
    Date startDate, endDate;
    startDate = (start == null) ? null : startDateWithCustomTime(start, zoneId, from);
    endDate = (end == null) ? null : endDateWithCustomTime(end, zoneId, to);
    return new RollingDate(startDate, endDate);
  }

  private static RollingDate createRollingDateFilter(LocalDate start, LocalDate end, LocalTime localTime) {
    Date startDate, endDate;
    startDate = (start == null) ? null : Date.from(start.atTime(localTime).toInstant(ZoneOffset.UTC));
    endDate = (end == null) ? null : Date.from(end.atTime(localTime).toInstant(ZoneOffset.UTC));
    return new RollingDate(startDate, endDate);
  }

  @Getter
  @AllArgsConstructor
  public static class RollingDate{
    private final Date startDate;
    private final Date endDate;
  }
}