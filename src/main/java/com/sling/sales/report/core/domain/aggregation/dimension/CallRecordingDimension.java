package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;

import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.util.List;
import java.util.function.Function;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;

public class CallRecordingDimension<T extends Fact> implements FilterDimension<T> {

  private final String name;
  private final List<Operator> allowedOperators = asList(is_null, is_not_null);
  private final Function<Root<T>, Path<Boolean>> pathToColumn;

  public CallRecordingDimension(String name, Function<Root<T>, Path<Boolean>> pathToColumn) {
    this.name = name;
    this.pathToColumn = pathToColumn;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    switch (operator) {
      case is_null:
        return equalTo(Boolean.FALSE);
      case is_not_null:
        return equalTo(Boolean.TRUE);
      default:
        return null;
    }
  }

  private Specification<T> equalTo(boolean value) {
    return ((root, query, builder) ->
        builder.equal(pathToColumn.apply(root), value));
  }
}
