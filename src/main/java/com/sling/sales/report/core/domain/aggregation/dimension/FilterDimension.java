package com.sling.sales.report.core.domain.aggregation.dimension;

import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.util.List;
import org.springframework.data.jpa.domain.Specification;

public interface FilterDimension<T extends Fact> extends Dimension<T> {

  List<Operator> getAllowedOperators();
  List<FilterDimension<T>> getRequiredFilters();

  default boolean supports(Operator operator) {
    return this.getAllowedOperators().stream().anyMatch(o -> o.equals(operator));
  };

  Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter);
}
