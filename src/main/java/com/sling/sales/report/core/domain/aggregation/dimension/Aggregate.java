package com.sling.sales.report.core.domain.aggregation.dimension;

import com.sling.sales.report.core.api.response.RecordDetails;
import java.util.Date;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode
public class Aggregate {

  private final Long id;
  private final String name;
  private final int nextColumnNumber;
  private final Date orderByDate;
  private RecordDetails recordDetails;

  public Aggregate(Long id, String name, int nextColumnNumber, Date orderByDate) {
    this.id = id;
    this.name = name;
    this.nextColumnNumber = nextColumnNumber;
    this.orderByDate = orderByDate;
  }

  public Aggregate withRecordDetails(RecordDetails recordDetails) {
    this.recordDetails = recordDetails;
    return this;
  }
}
