package com.sling.sales.report.core.api.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class NudgeUserRequest {

  private String colorCode;
  private List<Long> users;
  private String content;

  @JsonCreator
  public NudgeUserRequest(@JsonProperty("users") List<Long> users, @JsonProperty("content") String content,
      @JsonProperty("colorCode") String colorCode) {
    this.users = users;
    this.content = content;
    this.colorCode = colorCode;
  }

}
