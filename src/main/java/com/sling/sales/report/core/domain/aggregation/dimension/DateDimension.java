package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.DateTimeParser.parseDate;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.after_current_date_and_time;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.before_current_date_and_time;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.between;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.current_month;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.current_quarter;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.current_week;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.current_year;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.greater;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.greater_or_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_fifteen_days;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_month;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_quarter;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_seven_days;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_thirty_days;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_week;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.last_year;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.less;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.less_or_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.month_to_date;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_fifteen_days;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_month;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_quarter;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_seven_days;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_thirty_days;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_week;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.next_year;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_between;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.quarter_to_date;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.today;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.tomorrow;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.week_to_date;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.year_to_date;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.yesterday;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.Format;
import com.sling.sales.report.core.domain.aggregation.dimension.RollingDateFilter.RollingDate;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.function.Function;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.jpa.domain.Specification;

@Slf4j
public class DateDimension<T extends Fact> implements FilterDimension<T>, GroupByDimension<T> {

  private List<Operator> allowedOperators =
      asList(greater, greater_or_equal, less, less_or_equal, between, not_between, before_current_date_and_time, after_current_date_and_time, today,
          current_week, last_week, current_month, last_month,
          current_quarter, last_quarter, current_year, last_year, next_week, next_month, next_quarter, next_year, last_seven_days, next_seven_days,
          last_fifteen_days, next_fifteen_days, last_thirty_days, next_thirty_days, week_to_date, month_to_date, quarter_to_date, year_to_date,
          is_not_null, is_null, yesterday, tomorrow);
  private final String name;
  private final Function<Root<T>, Path<Date>> pathToColumn;
  private Format format;

  public DateDimension(String fieldName, Function<Root<T>, Path<Date>> pathToColumn) {
    this.name = fieldName;
    this.pathToColumn = pathToColumn;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return this.allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  @Override
  public String getName() {
    return this.name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    switch (operator) {
      case greater:
        return hasDateGreaterThan(value, factFilter.getTimezone());
      case greater_or_equal:
        return hasDateGreaterThanOrEqual(value, factFilter.getTimezone());
      case less:
        return hasDateLesserThan(value, factFilter.getTimezone());
      case less_or_equal:
        return hasDateLesserThanOrEqual(value, factFilter.getTimezone());
      case between:
        return hasDateInBetween(value);
      case not_between:
        return hasDateNotInBetween(value);
      case is_null:
        return isNull();
      case is_not_null:
        return isNotNull();

      case before_current_date_and_time:
      case after_current_date_and_time:
      case today:
      case current_week:
      case last_week:
      case current_month:
      case last_month:
      case current_quarter:
      case last_quarter:
      case current_year:
      case last_year:
      case next_week:
      case next_month:
      case next_quarter:
      case next_year:
      case last_seven_days:
      case next_seven_days:
      case last_fifteen_days:
      case next_fifteen_days:
      case last_thirty_days:
      case next_thirty_days:
      case week_to_date:
      case month_to_date:
      case quarter_to_date:
      case year_to_date:
      case yesterday:
      case tomorrow:
        RollingDate rollingDate = new RollingDateFilter().from(operator, Instant.now(), ZoneId.of(String.valueOf(factFilter.getTimezone())),
            filter.getFrom(), filter.getTo());
        return hasDateInBetween(rollingDate.getStartDate(), rollingDate.getEndDate());

      default:
        return null;
    }
  }

  Specification<T> hasDateGreaterThan(Object value, String timezone) {
    var date = parseDate(value);
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) ->
        builder.greaterThan(pathToColumn.apply(root), date));
  }

  Specification<T> hasDateGreaterThanOrEqual(Object value, String timezone) {
    var date = parseDate(value);
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) ->
        builder.greaterThanOrEqualTo(pathToColumn.apply(root), date));
  }

  Specification<T> hasDateLesserThan(Object value, String timezone) {
    var date = parseDate(value);
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) ->
        builder.lessThan(pathToColumn.apply(root), date));
  }

  Specification<T> hasDateLesserThanOrEqual(Object value, String timezone) {
    var date = parseDate(value);
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) ->
        builder.lessThanOrEqualTo(pathToColumn.apply(root), date));
  }

  Specification<T> hasDateInBetween(Object valuesRange) {
    var boundedValues = new BoundedValue(valuesRange);
    return hasDateInBetween(boundedValues.lowerBound, boundedValues.upperBound);
  }

  Specification<T> hasDateInBetween(Date startDate, Date endDate) {
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) ->
        (startDate == null) ? builder.lessThan(pathToColumn.apply(root), endDate) :
            (endDate == null) ? builder.greaterThanOrEqualTo(pathToColumn.apply(root), startDate) :
                builder.between(pathToColumn.apply(root), startDate, endDate));

  }

  Specification<T> hasDateNotInBetween(Object valuesRange) {
    var boundedValues = new BoundedValue(valuesRange);
    return ((Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) ->
        builder
            .between(
                pathToColumn.apply(root), boundedValues.getLowerBound(), boundedValues.getUpperBound())
            .not());
  }

  Specification<T> isNull() {
    return ((root, query, builder) -> builder.isNull(pathToColumn.apply(root)));
  }

  Specification<T> isNotNull() {
    return ((root, query, builder) -> builder.isNotNull(pathToColumn.apply(root)));
  }

  @Override
  public List<Expression<?>> getGroupByPath(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, String timezone) {
    if (ObjectUtils.isEmpty(format)) {
      return Collections.singletonList(pathToColumn.apply(root));
    }
    var dateInUtcTimezone = criteriaBuilder.function("timezone", Date.class, criteriaBuilder.literal("UTC"), pathToColumn.apply(root));
    var dateInUserTimeZone = criteriaBuilder.function("timezone", Date.class, criteriaBuilder.literal(timezone), dateInUtcTimezone);
    var toChar = criteriaBuilder.function("to_char", String.class, dateInUserTimeZone, criteriaBuilder.literal(format.getRangePattern()));
    var toDate = criteriaBuilder.function("to_date", Date.class, toChar, criteriaBuilder.literal(format.getRangePattern()));
    return List.of(toChar, toDate);
  }

  @Override
  public Aggregate toResultKey(Tuple tuple, int columnNumber) {
    if (ObjectUtils.isEmpty(format)) {
      return new Aggregate(null, tuple.get(columnNumber, String.class), columnNumber + 1, null);
    }
    return new Aggregate(null, tuple.get(columnNumber, String.class), columnNumber + 2, tuple.get(columnNumber + 1, Date.class));
  }

  @Override
  public DateDimension<T> withFormat(Format format) {
    this.format = format;
    return this;
  }

  @Override
  public Format getFormat() {
    return this.format;
  }

  @Getter
  static class BoundedValue {

    private final Date lowerBound;
    private final Date upperBound;

    private BoundedValue(Object valuesRange) {
      var stringArray = (ArrayList) valuesRange;
      this.lowerBound = parseDate(stringArray.get(0));
      this.upperBound = parseDate(stringArray.get(1));
    }
  }
}
