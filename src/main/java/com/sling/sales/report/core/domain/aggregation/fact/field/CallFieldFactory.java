package com.sling.sales.report.core.domain.aggregation.fact.field;

import static java.util.Collections.emptyList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.domain.aggregation.dimension.BooleanDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.CallRecordingDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.DateDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.IdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdAssociatedEntityFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyTextDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.TextDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.UserPropertyIdDimension;
import com.sling.sales.report.core.domain.call.Call;
import com.sling.sales.report.core.domain.call.CallDisposition;
import com.sling.sales.report.core.domain.call.CallDisposition_;
import com.sling.sales.report.core.domain.call.CallRelatedTo;
import com.sling.sales.report.core.domain.call.CallRelatedTo_;
import com.sling.sales.report.core.domain.call.Call_;
import com.sling.sales.report.core.domain.call.CustomerEmotion;
import com.sling.sales.report.core.domain.call.CustomerEmotion_;
import com.sling.sales.report.dto.DimensionDetail;
import com.sling.sales.report.security.domain.Team;
import com.sling.sales.report.security.domain.Team_;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.User_;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
class CallFieldFactory extends FactFieldFactory<Call> {

  private final static Map<String, CallUserMapping> MAPPED_PRIMARY_PROPERTIES = new HashMap<>() {{
    put("owner", new CallUserMapping(Call_.OWNER, Call_.owner));
    put("createdBy", new CallUserMapping(Call_.CREATED_BY, Call_.createdBy));
    put("updatedBy", new CallUserMapping(Call_.UPDATED_BY, Call_.updatedBy));
  }};

  private final IdDimension<Call, User> owner = new IdDimension<>(
      "owner",
      Call_.OWNER,
      Call_.owner,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final TextDimension<Call> callType = new TextDimension<>(
      "callType",
      root -> root.get(Call_.callType),
      emptyList());

  private final TextDimension<Call> outcome = new TextDimension<>(
      "outcome",
      root -> root.get(Call_.outcome),
      emptyList());

  private final ManyTextDimension<Call> entities = new ManyTextDimension<>(
      "entities",
      Call_.ENTITIES,
      Call_.entities
  );

  private final CallRecordingDimension<Call> recording = new CallRecordingDimension<>(
      "callRecording",
      root -> root.get(Call_.recordingAvailable)
  );

  private final NumberFilterDimension<Call, Double> durationInMinutes = new NumberFilterDimension<>(
      "duration",
      root -> root.get(Call_.durationInMinutes),
      Double::valueOf
  );

  private final NumberFilterDimension<Call, Double> callBackDurationInMinutes = new NumberFilterDimension<>(
      "callbackDuration",
      root -> root.get(Call_.callbackDurationInMinutes),
      Double::valueOf
  );

  private final IdDimension<Call, User> createdBy = new IdDimension<>(
      "createdBy",
      Call_.CREATED_BY,
      Call_.createdBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdDimension<Call, User> updatedBy = new IdDimension<>(
      "updatedBy",
      Call_.UPDATED_BY,
      Call_.updatedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final TextDimension<Call> ivrNumber = new TextDimension<>(
      "ivrNumber",
      root -> root.get(Call_.ivrNumber),
      emptyList());

  private final TextDimension<Call> phoneNumber = new TextDimension<>(
      "phoneNumber",
      callRoot -> callRoot.get(Call_.phoneNumber),
      emptyList()
  );

  private final TextDimension<Call> deviceId = new TextDimension<>(
      "deviceId",
      callRoot -> callRoot.get(Call_.deviceId),
      emptyList()
  );

  private final BooleanDimension<Call> manual = new BooleanDimension<>(
      "isManual",
      callRoot -> callRoot.get(Call_.manual)
  );

  private final ManyIdAssociatedEntityFilterDimension<Call, CallRelatedTo> associatedLeads = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedLeads",
      Call_.CALL_RELATED_TOS,
      Call_.callRelatedTos,
      join -> join.get(CallRelatedTo_.entityId),
      join -> join.get(CallRelatedTo_.entity),
      join -> join.get(CallRelatedTo_.entityName),
      "lead"
  );

  private final ManyIdAssociatedEntityFilterDimension<Call, CallRelatedTo> associatedContacts = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedContacts",
      Call_.CALL_RELATED_TOS,
      Call_.callRelatedTos,
      join -> join.get(CallRelatedTo_.entityId),
      join -> join.get(CallRelatedTo_.entity),
      join -> join.get(CallRelatedTo_.entityName),
      "contact"
  );

  private final ManyIdAssociatedEntityFilterDimension<Call, CallRelatedTo> associatedDeals = new ManyIdAssociatedEntityFilterDimension<>(
      "associatedDeals",
      Call_.CALL_RELATED_TOS,
      Call_.callRelatedTos,
      join -> join.get(CallRelatedTo_.entityId),
      join -> join.get(CallRelatedTo_.entity),
      join -> join.get(CallRelatedTo_.entityName),
      "deal"
  );

  private final DateDimension<Call> callConductedAt = new DateDimension<>("startTime", root -> root.get(Call_.callConductedAt));
  private final DateDimension<Call> createdAt = new DateDimension<>("createdAt", root -> root.get(Call_.createdAt));
  private final DateDimension<Call> updatedAt = new DateDimension<>("updatedAt", root -> root.get(Call_.updatedAt));

  private final IdDimension<Call, CallDisposition> callDisposition = new IdDimension<>(
      "callDisposition",
      Call_.CALL_DISPOSITION,
      Call_.callDisposition,
      join -> join.get(CallDisposition_.id),
      join -> join.get(CallDisposition_.name),
      emptyList());

  private final ManyIdDimension<Call, CustomerEmotion> customerEmotion = new ManyIdDimension<>(
      "customerEmotion",
      Call_.CUSTOMER_EMOTION,
      Call_.customerEmotion,
      join -> join.get(CustomerEmotion_.customerEmotionId),
      join -> join.get(CustomerEmotion_.name),
      emptyList()
  );

  private final TextDimension<Call> overallSentiment = new TextDimension<>(
      "overallSentiment",
      root -> root.get(Call_.overallSentiment),
      emptyList());

  private final Map<String, GroupByDimension<Call>> GROUP_BY_DIMENSIONS =
      new HashMap<>() {
        {
          put(owner.getName(), owner);
          put(outcome.getName(), outcome);
          put(callType.getName(), callType);
          put(callConductedAt.getName(), callConductedAt);
          put(createdAt.getName(), createdAt);
          put(updatedAt.getName(), updatedAt);
          put(createdBy.getName(), createdBy);
          put(updatedBy.getName(), updatedBy);
          put(ivrNumber.getName(), ivrNumber);
          put(deviceId.getName(), deviceId);
          put(callDisposition.getName(), callDisposition);
          put(overallSentiment.getName(), overallSentiment);
          put(customerEmotion.getName(), customerEmotion);
        }
      };

  private final HashMap<String, FilterDimension<Call>> DIMENSIONS =
      new HashMap<>() {
        {
          put(owner.getName(), owner);
          put(outcome.getName(), outcome);
          put(callType.getName(), callType);
          put(recording.getName(), recording);
          put(durationInMinutes.getName(), durationInMinutes);
          put(entities.getName(), entities);
          put(callConductedAt.getName(), callConductedAt);
          put(createdAt.getName(), createdAt);
          put(updatedAt.getName(), updatedAt);
          put(createdBy.getName(), createdBy);
          put(updatedBy.getName(), updatedBy);
          put(ivrNumber.getName(), ivrNumber);
          put(phoneNumber.getName(), phoneNumber);
          put(deviceId.getName(), deviceId);
          put(manual.getName(), manual);
          put(associatedLeads.getName(), associatedLeads);
          put(associatedContacts.getName(), associatedContacts);
          put(associatedDeals.getName(), associatedDeals);
          put(callBackDurationInMinutes.getName(), callBackDurationInMinutes);
          put(callDisposition.getName(), callDisposition);
          put(overallSentiment.getName(), overallSentiment);
          put(customerEmotion.getName(), customerEmotion);
        }
      };

  private final List<Metric<Call>> SUPPORTED_METRICS =
      new ArrayList<>() {{
        add(new Metric<>(
            "id",
            MetricType.COUNT,
            (root, builder) -> builder.countDistinct(root),
            null));
        add(new Metric<>(
            durationInMinutes.getName(),
            MetricType.SUM,
            (root, builder) -> builder.sum(root.get(Call_.durationInMinutes)),
            null));
        add(new Metric<>(
            durationInMinutes.getName(),
            MetricType.AVERAGE,
            (root, builder) -> builder.avg(root.get(Call_.durationInMinutes)), null));
        add(new Metric<>(
            callBackDurationInMinutes.getName(),
            MetricType.SUM,
            (root, builder) -> builder.sum(root.get(Call_.callbackDurationInMinutes)),
            null));
        add(new Metric<>(
            callBackDurationInMinutes.getName(),
            MetricType.AVERAGE,
            (root, builder) -> builder.avg(root.get(Call_.callbackDurationInMinutes)), null));
      }};

  @Override
  public DimensionDetail<Call> getDimensionDetail() {
    return new DimensionDetail<>(GROUP_BY_DIMENSIONS, DIMENSIONS, SUPPORTED_METRICS);
  }

  @Override
  public GroupByDimension<Call> createUserPropertyGroupByIdDimension(GroupByField groupByField) {
    CallUserMapping callUserMapping = MAPPED_PRIMARY_PROPERTIES.get(groupByField.getPrimaryField());
    if ("teams".equals(groupByField.getProperty())) {
      return new UserPropertyIdDimension<>(
          groupByField.getName(),
          callUserMapping.getMappedColumnFieldName(),
          callUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateLeftJoin(join, groupByField.getProperty()).get(Team_.id),
          join -> getOrCreateLeftJoin(join, groupByField.getProperty()).get(Team_.name),
          emptyList(),
          groupByField.getPrimaryField(),
          groupByField.getProperty()
      );
    }
    return null;
  }

  @Override
  public FilterDimension<Call> createUserPropertyFilterIdDimension(Filter filter) {
    CallUserMapping callUserMapping = MAPPED_PRIMARY_PROPERTIES.get(filter.getPrimaryField());
    if ("teams".equals(filter.getProperty())) {
      return new UserPropertyIdDimension<>(
          filter.getFieldName(),
          callUserMapping.getMappedColumnFieldName(),
          callUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateLeftJoin(join, filter.getProperty()).get(Team_.id),
          join -> getOrCreateLeftJoin(join, filter.getProperty()).get(Team_.name),
          emptyList(),
          filter.getPrimaryField(),
          filter.getProperty()
      );
    }
    return null;
  }

  private Join<User, Team> getOrCreateLeftJoin(Join<Call, User> callUserJoin, String dimensionName) {
    return callUserJoin.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<User, Team>) j))
        .findFirst()
        .orElseGet(() -> callUserJoin.join(User_.teams, JoinType.LEFT));
  }
}
