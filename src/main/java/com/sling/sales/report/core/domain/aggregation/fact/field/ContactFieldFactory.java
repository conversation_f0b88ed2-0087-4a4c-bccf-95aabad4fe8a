package com.sling.sales.report.core.domain.aggregation.fact.field;

import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.domain.aggregation.dimension.BooleanDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.DateDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.IdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.TextDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.UserPropertyIdDimension;
import com.sling.sales.report.core.domain.contact.Contact;
import com.sling.sales.report.core.domain.contact.ContactCampaign;
import com.sling.sales.report.core.domain.contact.ContactCampaignActivity;
import com.sling.sales.report.core.domain.contact.ContactCampaignActivity_;
import com.sling.sales.report.core.domain.contact.ContactCampaign_;
import com.sling.sales.report.core.domain.contact.ContactCompany;
import com.sling.sales.report.core.domain.contact.ContactCompany_;
import com.sling.sales.report.core.domain.contact.ContactDeal;
import com.sling.sales.report.core.domain.contact.ContactDeal_;
import com.sling.sales.report.core.domain.contact.ContactEmail;
import com.sling.sales.report.core.domain.contact.ContactEmail_;
import com.sling.sales.report.core.domain.contact.ContactPhoneNumber;
import com.sling.sales.report.core.domain.contact.ContactPhoneNumber_;
import com.sling.sales.report.core.domain.contact.ContactSource;
import com.sling.sales.report.core.domain.contact.ContactSource_;
import com.sling.sales.report.core.domain.contact.ContactUtm;
import com.sling.sales.report.core.domain.contact.ContactUtm_;
import com.sling.sales.report.core.domain.contact.Contact_;
import com.sling.sales.report.dto.DimensionDetail;
import com.sling.sales.report.security.domain.Team;
import com.sling.sales.report.security.domain.Team_;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.User_;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ContactFieldFactory extends FactFieldFactory<Contact> {

  private final static Map<String, ContactUserMapping> MAPPED_PRIMARY_PROPERTIES = new HashMap<>() {{
    put("ownerId", new ContactUserMapping(Contact_.OWNED_BY, Contact_.ownedBy));
    put("createdBy", new ContactUserMapping(Contact_.CREATED_BY, Contact_.createdBy));
    put("updatedBy", new ContactUserMapping(Contact_.UPDATED_BY, Contact_.updatedBy));
    put("importedBy", new ContactUserMapping(Contact_.IMPORTED_BY, Contact_.importedBy));
  }};

  private final IdDimension<Contact, User> owner = new IdDimension<>(
      "ownerId",
      Contact_.OWNED_BY,
      Contact_.ownedBy,
      contactUserJoin -> contactUserJoin.get(User_.id),
      contactUserJoin -> contactUserJoin.get(User_.name),
      Collections.emptyList()
  );

  private final TextDimension<Contact> firstName = new TextDimension<>(
      "firstName",
      contactRoot -> contactRoot.get(Contact_.firstName),
      Collections.emptyList()
  );

  private final TextDimension<Contact> lastName = new TextDimension<>(
      "lastName",
      contactRoot -> contactRoot.get(Contact_.lastName),
      Collections.emptyList()
  );

  private final IdDimension<Contact, ContactCompany> company = new IdDimension<>(
      "company",
      Contact_.CONTACT_COMPANY,
      Contact_.contactCompany,
      contactCompanyJoin -> contactCompanyJoin.get(ContactCompany_.id),
      contactCompanyJoin -> contactCompanyJoin.get(ContactCompany_.name),
      Collections.emptyList()
  );

  private final IdDimension<Contact, User> importedBy = new IdDimension<>(
      "importedBy",
      Contact_.IMPORTED_BY,
      Contact_.importedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final TextDimension<Contact> designation = new TextDimension<>(
      "designation",
      contactRoot -> contactRoot.get(Contact_.designation),
      Collections.emptyList()
  );

  private final BooleanDimension<Contact> dnd = new BooleanDimension<>(
      "dnd",
      contactRoot -> contactRoot.get(Contact_.dnd)
  );

  private final TextDimension<Contact> timezone = new TextDimension<>(
      "timezone",
      contactRoot -> contactRoot.get(Contact_.timezone),
      Collections.emptyList()
  );

  private final TextDimension<Contact> city = new TextDimension<>(
      "city",
      contactRoot -> contactRoot.get(Contact_.city),
      Collections.emptyList()
  );

  private final TextDimension<Contact> state = new TextDimension<>(
      "state",
      contactRoot -> contactRoot.get(Contact_.state),
      Collections.emptyList()
  );

  private final TextDimension<Contact> zipcode = new TextDimension<>(
      "zipcode",
      contactRoot -> contactRoot.get(Contact_.zipcode),
      Collections.emptyList()
  );

  private final TextDimension<Contact> country = new TextDimension<>(
      "country",
      contactRoot -> contactRoot.get(Contact_.country),
      Collections.emptyList()
  );

  private final TextDimension<Contact> department = new TextDimension<>(
      "department",
      contactRoot -> contactRoot.get(Contact_.department),
      Collections.emptyList()
  );

  private final DateDimension<Contact> createdAt = new DateDimension<>(
      "createdAt",
      root -> root.get(Contact_.createdAt)
  );

  private final DateDimension<Contact> updatedAt = new DateDimension<>(
      "updatedAt",
      root -> root.get(Contact_.updatedAt)
  );

  private final IdDimension<Contact, User> createdBy = new IdDimension<>(
      "createdBy",
      Contact_.CREATED_BY,
      Contact_.createdBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdDimension<Contact, User> updatedBy = new IdDimension<>(
      "updatedBy",
      Contact_.UPDATED_BY,
      Contact_.updatedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final TextDimension<Contact> createdViaType = new TextDimension<>(
      "createdViaType",
      contactRoot -> contactRoot.get(Contact_.createdViaType),
      Collections.emptyList()
  );

  private final TextDimension<Contact> updatedViaType = new TextDimension<>(
      "updatedViaType",
      contactRoot -> contactRoot.get(Contact_.updatedViaType),
      Collections.emptyList()
  );

  private final ManyIdDimension<Contact, ContactDeal> associatedDeals = new ManyIdDimension<>(
      "associatedDeals",
      Contact_.ASSOCIATED_DEALS,
      Contact_.associatedDeals,
      contactDealJoin -> contactDealJoin.get(ContactDeal_.dealId),
      contactDealJoin -> contactDealJoin.get(ContactDeal_.name),
      emptyList()
  );

  private final IdDimension<Contact, ContactSource> source = new IdDimension<>(
      "source",
      Contact_.SOURCE,
      Contact_.source,
      join -> join.get(ContactSource_.id),
      join -> join.get(ContactSource_.name),
      emptyList());

  private final IdDimension<Contact, ContactCampaign> campaign = new IdDimension<>(
      "campaign",
      Contact_.CAMPAIGN,
      Contact_.campaign,
      join -> join.get(ContactCampaign_.id),
      join -> join.get(ContactCampaign_.name),
      emptyList());


  private final TextDimension<Contact> subSource = new TextDimension<>(
      "subSource",
      root -> getOrCreateLeftJoin(root, ContactUtm_.SUB_SOURCE).get(ContactUtm_.subSource),
      emptyList());

  private final TextDimension<Contact> utmSource = new TextDimension<>(
      "utmSource",
      root -> getOrCreateLeftJoin(root, ContactUtm_.UTM_SOURCE).get(ContactUtm_.utmSource),
      emptyList());

  private final TextDimension<Contact> utmMedium = new TextDimension<>(
      "utmMedium",
      root -> getOrCreateLeftJoin(root, ContactUtm_.UTM_MEDIUM).get(ContactUtm_.utmMedium),
      emptyList());

  private final TextDimension<Contact> utmCampaign = new TextDimension<>(
      "utmCampaign",
      root -> getOrCreateLeftJoin(root, ContactUtm_.UTM_CAMPAIGN).get(ContactUtm_.utmCampaign),
      emptyList());

  private final TextDimension<Contact> utmTerm = new TextDimension<>(
      "utmTerm",
      root -> getOrCreateLeftJoin(root, ContactUtm_.UTM_TERM).get(ContactUtm_.utmTerm),
      emptyList());

  private final TextDimension<Contact> utmContent = new TextDimension<>(
      "utmContent",
      root -> getOrCreateLeftJoin(root, ContactUtm_.UTM_CONTENT).get(ContactUtm_.utmContent),
      emptyList());

  private final NumberFilterDimension<Contact, Double> score = new NumberFilterDimension<>(
      "score",
      root -> root.get(Contact_.score),
      Double::valueOf
  );

  private final TextDimension<Contact> contactEmails = new TextDimension<>(
      "emails",
      root -> getOrCreateLeftJoinEmail(root, ContactEmail_.VALUE).get(ContactEmail_.value),
      emptyList());

  private final TextDimension<Contact> contactPhoneNumbers = new TextDimension<>(
      "phoneNumbers",
      root -> getOrCreateLeftJoinPhone(root, ContactPhoneNumber_.VALUE).get(ContactPhoneNumber_.value),
      emptyList());

  private final ManyIdDimension<Contact, ContactCampaignActivity> campaignActivities = new ManyIdDimension<>(
      "campaignActivities",
      Contact_.CONTACT_CAMPAIGN_ACTIVITIES,
      Contact_.contactCampaignActivities,
      join -> join.get(ContactCampaignActivity_.campaignId),
      join -> join.get(ContactCampaignActivity_.campaignName),
      emptyList()
  );

  private final ManyIdDimension<Contact, ContactCampaignActivity> activities = new ManyIdDimension<>(
      "activities",
      Contact_.CONTACT_CAMPAIGN_ACTIVITIES,
      Contact_.contactCampaignActivities,
      join -> join.get(ContactCampaignActivity_.activityId),
      join -> join.get(ContactCampaignActivity_.activityName),
      singletonList(campaignActivities)
  );

  private final Map<String, GroupByDimension<Contact>> groupByDimensions = new HashMap<>() {{
    put(owner.getName(), owner);
    put(company.getName(), company);
    put(designation.getName(), designation);
    put(timezone.getName(), timezone);
    put(city.getName(), city);
    put(state.getName(), state);
    put(zipcode.getName(), zipcode);
    put(country.getName(), country);
    put(department.getName(), department);
    put(createdAt.getName(), createdAt);
    put(updatedAt.getName(), updatedAt);
    put(createdBy.getName(), createdBy);
    put(updatedBy.getName(), updatedBy);
    put(importedBy.getName(), importedBy);
    put(createdViaType.getName(), createdViaType);
    put(updatedViaType.getName(), updatedViaType);
    put(associatedDeals.getName(), associatedDeals);
    put(source.getName(), source);
    put(campaign.getName(), campaign);
    put(subSource.getName(), subSource);
    put(utmSource.getName(), utmSource);
    put(utmCampaign.getName(), utmCampaign);
    put(utmMedium.getName(), utmMedium);
    put(utmTerm.getName(), utmTerm);
    put(utmContent.getName(), utmContent);
    put(campaignActivities.getName(), campaignActivities);
    put(activities.getName(), activities);
  }};

  private final Map<String, FilterDimension<Contact>> filterDimensions = new HashMap<>() {{
    put(owner.getName(), owner);
    put(firstName.getName(), firstName);
    put(lastName.getName(), lastName);
    put(company.getName(), company);
    put(designation.getName(), designation);
    put(dnd.getName(), dnd);
    put(timezone.getName(), timezone);
    put(city.getName(), city);
    put(state.getName(), state);
    put(zipcode.getName(), zipcode);
    put(country.getName(), country);
    put(department.getName(), department);
    put(createdAt.getName(), createdAt);
    put(updatedAt.getName(), updatedAt);
    put(createdBy.getName(), createdBy);
    put(updatedBy.getName(), updatedBy);
    put(importedBy.getName(), importedBy);
    put(createdViaType.getName(), createdViaType);
    put(updatedViaType.getName(), updatedViaType);
    put(associatedDeals.getName(), associatedDeals);
    put(source.getName(), source);
    put(campaign.getName(), campaign);
    put(subSource.getName(), subSource);
    put(utmSource.getName(), utmSource);
    put(utmCampaign.getName(), utmCampaign);
    put(utmMedium.getName(), utmMedium);
    put(utmTerm.getName(), utmTerm);
    put(utmContent.getName(), utmContent);
    put(score.getName(), score);
    put(contactPhoneNumbers.getName(), contactPhoneNumbers);
    put(contactEmails.getName(), contactEmails);
    put(campaignActivities.getName(), campaignActivities);
    put(activities.getName(), activities);
  }};

  private final List<Metric<Contact>> metrics = new ArrayList<>() {{
    add(new Metric<>(
        "id",
        MetricType.COUNT,
        (root, builder) -> builder.countDistinct(root),
        null));
    add(new Metric<>(
        "score",
        MetricType.SUM,
        (root, builder) -> builder.sum(root.get(Contact_.score)),
        null));
    add(new Metric<>(
        "score",
        MetricType.AVERAGE,
        (root, builder) -> builder.avg(root.get(Contact_.score)),
        null));
  }};

  @Override
  public DimensionDetail<Contact> getDimensionDetail() {
    return new DimensionDetail<>(groupByDimensions, filterDimensions, metrics);
  }

  private Join<Contact, ContactUtm> getOrCreateLeftJoin(Root<Contact> root, String dimensionName) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<Contact, ContactUtm>) j))
        .findFirst()
        .orElseGet(() -> root.join(Contact_.contactUtm, JoinType.LEFT));
  }

  @Override
  public GroupByDimension<Contact> createUserPropertyGroupByIdDimension(GroupByField groupByField) {
    ContactUserMapping contactUserMapping = MAPPED_PRIMARY_PROPERTIES.get(groupByField.getPrimaryField());
    if ("teams".equals(groupByField.getProperty())) {
      return new UserPropertyIdDimension<>(
          groupByField.getName(),
          contactUserMapping.getMappedColumnFieldName(),
          contactUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateLeftJoin(join, groupByField.getProperty()).get(Team_.id),
          join -> getOrCreateLeftJoin(join, groupByField.getProperty()).get(Team_.name),
          emptyList(),
          groupByField.getPrimaryField(),
          groupByField.getProperty()
      );
    }
    return null;
  }

  @Override
  public FilterDimension<Contact> createUserPropertyFilterIdDimension(Filter filter) {
    ContactUserMapping contactUserMapping = MAPPED_PRIMARY_PROPERTIES.get(filter.getPrimaryField());
    if ("teams".equals(filter.getProperty())) {
      return new UserPropertyIdDimension<>(
          filter.getFieldName(),
          contactUserMapping.getMappedColumnFieldName(),
          contactUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateLeftJoin(join, filter.getProperty()).get(Team_.id),
          join -> getOrCreateLeftJoin(join, filter.getProperty()).get(Team_.name),
          emptyList(),
          filter.getPrimaryField(),
          filter.getProperty()
      );
    }
    return null;
  }

  private Join<User, Team> getOrCreateLeftJoin(Join<Contact, User> contactUserJoin, String dimensionName) {
    return contactUserJoin.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<User, Team>) j))
        .findFirst()
        .orElseGet(() -> contactUserJoin.join(User_.teams, JoinType.LEFT));
  }

  private Join<Contact, ContactPhoneNumber> getOrCreateLeftJoinPhone(Root<Contact> root, String dimensionName) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<Contact, ContactPhoneNumber>) j))
        .findFirst()
        .orElseGet(() -> root.join(Contact_.contactPhoneNumbers, JoinType.LEFT));
  }
  private Join<Contact, ContactEmail> getOrCreateLeftJoinEmail(Root<Contact> root, String dimensionName) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<Contact, ContactEmail>) j))
        .findFirst()
        .orElseGet(() -> root.join(Contact_.contactEmails, JoinType.LEFT));
  }
}
