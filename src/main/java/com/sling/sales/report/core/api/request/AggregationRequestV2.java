package com.sling.sales.report.core.api.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class AggregationRequestV2 {

  @JsonProperty("rules")
  private final ArrayList<Filter> filters = new ArrayList<>();
  @JsonProperty("groupBy")
  private final List<String> groupBy;
  @JsonProperty("dateRange")
  private final Filter dateRange;
  @JsonProperty("metrics")
  private final List<Metric> metrics;

  @JsonCreator
  public AggregationRequestV2(
      @JsonProperty("rules") List<Filter> filters, @JsonProperty("groupBy") List<String> groupBy,
      @JsonProperty("dateRange") Filter dateRange, @JsonProperty("metrics") List<Metric> metrics) {
    this.groupBy = groupBy;
    this.dateRange = dateRange;
    this.metrics = metrics;
    this.filters.addAll(filters);
  }
}
