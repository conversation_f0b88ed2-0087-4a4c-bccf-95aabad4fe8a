package com.sling.sales.report.core.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.api.request.AggregationRequestV3;
import com.sling.sales.report.core.api.request.ReportCategory;
import com.sling.sales.report.mq.event.IdName;
import com.sling.sales.report.security.domain.User;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReportResponse {

  private final Long id;
  private final String name;
  private final String description;
  private final String reportType;
  private final String chartType;
  private final AggregationRequestV3 config;
  private Action recordActions;
  private final User createdBy;
  private final ReportCategory category;
  private final ReportGoalResponse goal;
  private final boolean systemDefault;
  private final boolean prorated;
  private final IdName currency;

  @JsonCreator
  public ReportResponse(
      @JsonProperty("id") Long id,
      @JsonProperty("name") String name,
      @JsonProperty("description") String description,
      @JsonProperty("reportType") String reportType,
      @JsonProperty("chartType") String chartType,
      @JsonProperty("config") AggregationRequestV3 config,
      @JsonProperty("recordActions") Action recordActions,
      @JsonProperty("createdBy") User createdBy,
      @JsonProperty("category") ReportCategory category,
      @JsonProperty("goal") ReportGoalResponse goal,
      @JsonProperty("systemDefault") boolean systemDefault,
      @JsonProperty("prorated") boolean prorated,
      @JsonProperty("currency") IdName currency) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.reportType = reportType;
    this.chartType = chartType;
    this.config = config;
    this.recordActions = recordActions;
    this.createdBy = createdBy;
    this.category = category;
    this.goal = goal;
    this.systemDefault = systemDefault;
    this.prorated = prorated;
    this.currency = currency;
  }

}
