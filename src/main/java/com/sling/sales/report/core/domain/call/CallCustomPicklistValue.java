package com.sling.sales.report.core.domain.call;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor
public class CallCustomPicklistValue implements EntityDimension {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private long id;
  @Column(name = "field_id")
  private long fieldId;
  @Column(name = "picklist_value_id")
  private Long picklistValueId;
  @Column(name = "display_name")
  private String displayName;

  @Column(name = "call_id")
  private Long callId;

  public CallCustomPicklistValue(long fieldId, Long picklistValueId, String displayName, Long callId) {
    this.fieldId = fieldId;
    this.picklistValueId = picklistValueId;
    this.displayName = displayName;
    this.callId = callId;
  }
}