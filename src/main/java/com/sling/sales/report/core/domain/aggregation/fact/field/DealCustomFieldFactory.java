package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.aggregation.dimension.DateCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyBooleanCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyTextCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberCustomFieldFilterDimension;
import com.sling.sales.report.core.domain.deal.Deal;
import com.sling.sales.report.core.domain.deal.DealCustomCheckboxValue;
import com.sling.sales.report.core.domain.deal.DealCustomCheckboxValue_;
import com.sling.sales.report.core.domain.deal.DealCustomDatePickerValue;
import com.sling.sales.report.core.domain.deal.DealCustomDatePickerValue_;
import com.sling.sales.report.core.domain.deal.DealCustomDatetimePickerValue;
import com.sling.sales.report.core.domain.deal.DealCustomDatetimePickerValue_;
import com.sling.sales.report.core.domain.deal.DealCustomMultiPicklistValue;
import com.sling.sales.report.core.domain.deal.DealCustomMultiPicklistValue_;
import com.sling.sales.report.core.domain.deal.DealCustomNumberValue;
import com.sling.sales.report.core.domain.deal.DealCustomNumberValue_;
import com.sling.sales.report.core.domain.deal.DealCustomTextValue;
import com.sling.sales.report.core.domain.deal.DealCustomTextValue_;
import com.sling.sales.report.core.domain.deal.DealPicklistValue;
import com.sling.sales.report.core.domain.deal.DealPicklistValue_;
import com.sling.sales.report.core.domain.deal.Deal_;
import com.sling.sales.report.dto.DimensionDetail;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class DealCustomFieldFactory {

  public static DimensionDetail<Deal> createManyIdCustomFieldDimension(String dimensionName, long fieldId) {
    ManyIdCustomFieldDimension<Deal, DealPicklistValue> manyIdCustomFieldDimension = new ManyIdCustomFieldDimension<>(
        dimensionName,
        Deal_.DEAL_PICKLIST_VALUES,
        Deal_.dealPicklistValues,
        join -> join.get(DealPicklistValue_.picklistValueId),
        join -> join.get(DealPicklistValue_.displayName),
        join -> join.get(DealPicklistValue_.fieldId),
        fieldId, DealPicklistValue_.DISPLAY_NAME);

    List<Metric<Deal>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Deal_.dealPicklistValues, join -> join.get(DealPicklistValue_.fieldId),
              DealPicklistValue_.PICKLIST_VALUE_ID, fieldId),
          null));
    }};

    return new DimensionDetail<>(manyIdCustomFieldDimension, manyIdCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Deal> createManyTextCustomFieldDimension(String dimensionName, long fieldId) {
    ManyTextCustomFieldDimension<Deal, DealCustomTextValue> manyTextCustomFieldDimension = new ManyTextCustomFieldDimension<>(
        dimensionName,
        Deal_.DEAL_CUSTOM_TEXT_VALUES,
        Deal_.dealCustomTextValues,
        join -> join.get(DealCustomTextValue_.value),
        join -> join.get(DealCustomTextValue_.fieldId),
        fieldId
    );

    List<Metric<Deal>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Deal_.dealCustomTextValues, join -> join.get(DealCustomTextValue_.fieldId),
              DealCustomTextValue_.VALUE, fieldId),
          null));
    }};

    return new DimensionDetail<>(manyTextCustomFieldDimension, manyTextCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Deal> createNumberCustomFieldFilterDimension(String dimensionName, long fieldId) {
    NumberCustomFieldFilterDimension<Deal, DealCustomNumberValue> numberCustomFieldFilterDimension = new NumberCustomFieldFilterDimension<>(
        dimensionName,
        Deal_.DEAL_CUSTOM_NUMBER_VALUES,
        Deal_.dealCustomNumberValues,
        join -> join.get(DealCustomNumberValue_.value),
        join -> join.get(DealCustomNumberValue_.fieldId), fieldId);

    List<Metric<Deal>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Deal_.dealCustomNumberValues, join -> join.get(DealCustomNumberValue_.fieldId),
              DealCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.SUM,
          getBiFunction(MetricType.SUM, Deal_.dealCustomNumberValues, join -> join.get(DealCustomNumberValue_.fieldId),
              DealCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.AVERAGE,
          getBiFunction(MetricType.AVERAGE, Deal_.dealCustomNumberValues, join -> join.get(DealCustomNumberValue_.fieldId),
              DealCustomNumberValue_.VALUE, fieldId),
          null));
    }};

    return new DimensionDetail<>(null, numberCustomFieldFilterDimension, metrics);
  }

  public static DimensionDetail<Deal> createDatePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Deal, DealCustomDatePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Deal_.DEAL_CUSTOM_DATE_PICKER_VALUES,
        Deal_.dealCustomDatePickerValues,
        join -> join.get(DealCustomDatePickerValue_.value),
        join -> join.get(DealCustomDatePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Deal> createDatetimePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Deal, DealCustomDatetimePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Deal_.DEAL_CUSTOM_DATETIME_PICKER_VALUES,
        Deal_.dealCustomDatetimePickerValues,
        join -> join.get(DealCustomDatetimePickerValue_.value),
        join -> join.get(DealCustomDatetimePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Deal> createBooleanCustomFieldDimension(String dimensionName, long fieldId) {
    ManyBooleanCustomFieldDimension<Deal, DealCustomCheckboxValue> manyBooleanCustomFieldDimension = new ManyBooleanCustomFieldDimension<>(
        dimensionName,
        Deal_.DEAL_CUSTOM_CHECKBOX_VALUES,
        Deal_.dealCustomCheckboxValues,
        join -> join.get(DealCustomCheckboxValue_.value),
        join -> join.get(DealCustomCheckboxValue_.fieldId),
        fieldId
    );
    return new DimensionDetail<>(manyBooleanCustomFieldDimension, manyBooleanCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Deal> createCustomMultiFieldDimension(String dimensionName, long fieldId) {
    ManyIdCustomFieldDimension<Deal, DealCustomMultiPicklistValue> customMultiPicklistDimension = new ManyIdCustomFieldDimension<>(
        dimensionName,
        Deal_.DEAL_CUSTOM_MULTI_PICKLIST_VALUES,
        Deal_.dealCustomMultiPicklistValues,
        join -> join.get(DealCustomMultiPicklistValue_.picklistValueId),
        join -> join.get(DealCustomMultiPicklistValue_.displayName),
        join -> join.get(DealCustomMultiPicklistValue_.fieldId),
        fieldId, DealCustomMultiPicklistValue_.DISPLAY_NAME);

    List<Metric<Deal>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Deal_.dealCustomMultiPicklistValues, join -> join.get(DealCustomMultiPicklistValue_.fieldId),
              DealCustomMultiPicklistValue_.PICKLIST_VALUE_ID, fieldId),
          null));
    }};

    return new DimensionDetail<>(customMultiPicklistDimension, customMultiPicklistDimension, metrics);
  }

  private static <T, V> BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>> getBiFunction(MetricType metricType,
      SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> getFieldId, String attributeName, long fieldId) {

    return (root, builder) -> CustomFieldMetricBuilder
        .buildExpressionByMetricType(metricType, root, builder, dimensionField, getFieldId,
            attributeName, fieldId);
  }
}
