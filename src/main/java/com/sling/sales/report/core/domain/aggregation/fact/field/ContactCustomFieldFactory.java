package com.sling.sales.report.core.domain.aggregation.fact.field;

import com.sling.sales.report.core.domain.aggregation.dimension.DateCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyBooleanCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyTextCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberCustomFieldFilterDimension;
import com.sling.sales.report.core.domain.contact.Contact;
import com.sling.sales.report.core.domain.contact.ContactCustomCheckboxValue;
import com.sling.sales.report.core.domain.contact.ContactCustomCheckboxValue_;
import com.sling.sales.report.core.domain.contact.ContactCustomDatePickerValue;
import com.sling.sales.report.core.domain.contact.ContactCustomDatePickerValue_;
import com.sling.sales.report.core.domain.contact.ContactCustomDatetimePickerValue;
import com.sling.sales.report.core.domain.contact.ContactCustomDatetimePickerValue_;
import com.sling.sales.report.core.domain.contact.ContactCustomMultiPicklistValue;
import com.sling.sales.report.core.domain.contact.ContactCustomMultiPicklistValue_;
import com.sling.sales.report.core.domain.contact.ContactCustomNumberValue;
import com.sling.sales.report.core.domain.contact.ContactCustomNumberValue_;
import com.sling.sales.report.core.domain.contact.ContactCustomPicklistValue;
import com.sling.sales.report.core.domain.contact.ContactCustomPicklistValue_;
import com.sling.sales.report.core.domain.contact.ContactCustomTextValue;
import com.sling.sales.report.core.domain.contact.ContactCustomTextValue_;
import com.sling.sales.report.core.domain.contact.Contact_;
import com.sling.sales.report.dto.DimensionDetail;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class ContactCustomFieldFactory {

  public static DimensionDetail<Contact> createManyIdCustomFieldDimension(String dimensionName, long fieldId) {
    ManyIdCustomFieldDimension<Contact, ContactCustomPicklistValue> manyIdCustomFieldDimension = new ManyIdCustomFieldDimension<>(
        dimensionName,
        Contact_.CONTACT_PICKLIST_VALUES,
        Contact_.contactPicklistValues,
        join -> join.get(ContactCustomPicklistValue_.picklistValueId),
        join -> join.get(ContactCustomPicklistValue_.displayName),
        join -> join.get(ContactCustomPicklistValue_.fieldId),
        fieldId, ContactCustomPicklistValue_.DISPLAY_NAME);

    List<Metric<Contact>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Contact_.contactPicklistValues, join -> join.get(ContactCustomPicklistValue_.fieldId),
              ContactCustomPicklistValue_.PICKLIST_VALUE_ID, fieldId),
          null));
    }};

    return new DimensionDetail<>(manyIdCustomFieldDimension, manyIdCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Contact> createManyTextCustomFieldDimension(String dimensionName, long fieldId) {
    ManyTextCustomFieldDimension<Contact, ContactCustomTextValue> manyTextCustomFieldDimension = new ManyTextCustomFieldDimension<>(
        dimensionName,
        Contact_.CONTACT_CUSTOM_TEXT_VALUES,
        Contact_.contactCustomTextValues,
        join -> join.get(ContactCustomTextValue_.value),
        join -> join.get(ContactCustomTextValue_.fieldId),
        fieldId
    );

    List<Metric<Contact>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Contact_.contactCustomTextValues, join -> join.get(ContactCustomTextValue_.fieldId),
              ContactCustomTextValue_.VALUE, fieldId),
          null));
    }};

    return new DimensionDetail<>(manyTextCustomFieldDimension, manyTextCustomFieldDimension, metrics);
  }

  public static DimensionDetail<Contact> createNumberCustomFieldFilterDimension(String dimensionName, long fieldId) {
    NumberCustomFieldFilterDimension<Contact, ContactCustomNumberValue> numberCustomFieldFilterDimension = new NumberCustomFieldFilterDimension<>(
        dimensionName,
        Contact_.CONTACT_CUSTOM_NUMBER_VALUES,
        Contact_.contactCustomNumberValues,
        join -> join.get(ContactCustomNumberValue_.value),
        join -> join.get(ContactCustomNumberValue_.fieldId), fieldId);

    List<Metric<Contact>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Contact_.contactCustomNumberValues, join -> join.get(ContactCustomNumberValue_.fieldId),
              ContactCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.SUM,
          getBiFunction(MetricType.SUM, Contact_.contactCustomNumberValues, join -> join.get(ContactCustomNumberValue_.fieldId),
              ContactCustomNumberValue_.VALUE, fieldId),
          null));
      add(new Metric<>(
          dimensionName,
          MetricType.AVERAGE,
          getBiFunction(MetricType.AVERAGE, Contact_.contactCustomNumberValues, join -> join.get(ContactCustomNumberValue_.fieldId),
              ContactCustomNumberValue_.VALUE, fieldId),
          null));
    }};

    return new DimensionDetail<>(null, numberCustomFieldFilterDimension, metrics);
  }

  public static DimensionDetail<Contact> createDatePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Contact, ContactCustomDatePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Contact_.CONTACT_CUSTOM_DATE_PICKER_VALUES,
        Contact_.contactCustomDatePickerValues,
        join -> join.get(ContactCustomDatePickerValue_.value),
        join -> join.get(ContactCustomDatePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Contact> createDatetimePickerCustomFieldDimension(String dimensionName, long fieldId) {
    DateCustomFieldDimension<Contact, ContactCustomDatetimePickerValue> dateCustomFieldDimension = new DateCustomFieldDimension<>(
        dimensionName,
        Contact_.CONTACT_CUSTOM_DATETIME_PICKER_VALUES,
        Contact_.contactCustomDatetimePickerValues,
        join -> join.get(ContactCustomDatetimePickerValue_.value),
        join -> join.get(ContactCustomDatetimePickerValue_.fieldId),
        fieldId);
    return new DimensionDetail<>(dateCustomFieldDimension, dateCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Contact> createBooleanCustomFieldDimension(String dimensionName, long fieldId) {
    ManyBooleanCustomFieldDimension<Contact, ContactCustomCheckboxValue> manyBooleanCustomFieldDimension = new ManyBooleanCustomFieldDimension<>(
        dimensionName,
        Contact_.CONTACT_CUSTOM_CHECKBOX_VALUES,
        Contact_.contactCustomCheckboxValues,
        join -> join.get(ContactCustomCheckboxValue_.value),
        join -> join.get(ContactCustomCheckboxValue_.fieldId),
        fieldId
    );
    return new DimensionDetail<>(manyBooleanCustomFieldDimension, manyBooleanCustomFieldDimension, Collections.emptyList());
  }

  public static DimensionDetail<Contact> createCustomMultiFieldDimension(String dimensionName, long fieldId) {
    ManyIdCustomFieldDimension<Contact, ContactCustomMultiPicklistValue> customMultiPicklistDimension = new ManyIdCustomFieldDimension<>(
        dimensionName,
        Contact_.CONTACT_CUSTOM_MULTI_PICKLIST_VALUES,
        Contact_.contactCustomMultiPicklistValues,
        join -> join.get(ContactCustomMultiPicklistValue_.picklistValueId),
        join -> join.get(ContactCustomMultiPicklistValue_.displayName),
        join -> join.get(ContactCustomMultiPicklistValue_.fieldId),
        fieldId, ContactCustomPicklistValue_.DISPLAY_NAME);

    List<Metric<Contact>> metrics = new ArrayList<>() {{
      add(new Metric<>(
          dimensionName,
          MetricType.COUNT,
          getBiFunction(MetricType.COUNT, Contact_.contactCustomMultiPicklistValues, join -> join.get(ContactCustomMultiPicklistValue_.fieldId),
              ContactCustomMultiPicklistValue_.PICKLIST_VALUE_ID, fieldId),
          null));
    }};

    return new DimensionDetail<>(customMultiPicklistDimension, customMultiPicklistDimension, metrics);
  }

  private static <T, V> BiFunction<Root<T>, CriteriaBuilder, Expression<? extends Number>> getBiFunction(MetricType metricType,
      SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Long>> getFieldId, String attributeName, long fieldId) {

    return (root, builder) -> CustomFieldMetricBuilder
        .buildExpressionByMetricType(metricType, root, builder, dimensionField, getFieldId,
            attributeName, fieldId);
  }
}
