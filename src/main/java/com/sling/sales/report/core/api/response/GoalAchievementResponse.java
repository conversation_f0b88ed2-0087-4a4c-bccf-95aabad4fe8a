package com.sling.sales.report.core.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.api.request.AggregationRequestV3;
import com.sling.sales.report.core.api.request.AggregationRequestV3.ColorCode;
import com.sling.sales.report.core.domain.goal.Filter;
import com.sling.sales.report.dto.GoalDimension;
import com.sling.sales.report.dto.GoalFieldValue;
import com.sling.sales.report.dto.GoalFilter;
import com.sling.sales.report.dto.GoalFrequency;
import com.sling.sales.report.dto.GoalMetric;
import com.sling.sales.report.security.domain.User;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoalAchievementResponse {
  private final long id;
  private final String name;
  private final String description;
  private final String entityType;
  private final double value;
  private final GoalDimension dimension;
  private final GoalMetric metric;
  private final GoalFrequency frequency;
  private final List<GoalFilter> filters;
  private final List<GoalFieldValue> fieldValues;
  private final User createdBy;
  private final User updatedBy;
  private final boolean active;
  private final Long defaultReportId;
  private final Achievement achievement;
  private final Achievement totalGoalAchievement;
  private final Filter dateRange;
  private final List<AggregationRequestV3.ColorCode> colorCodes;
  private final String dashletType;
  private List<IdName> owners;


  @JsonCreator
  public GoalAchievementResponse(@JsonProperty("id") long id, @JsonProperty("name") String name, @JsonProperty("description") String description,
      @JsonProperty("entityType") String entityType,
      @JsonProperty("value") double value,
      @JsonProperty("dimension") GoalDimension dimension, @JsonProperty("metric") GoalMetric metric,
      @JsonProperty("frequency") GoalFrequency frequency,
      @JsonProperty("filters") List<GoalFilter> filters, @JsonProperty("fieldValues") List<GoalFieldValue> fieldValues,
      @JsonProperty("createdBy") User createdBy,
      @JsonProperty("updatedBy") User updatedBy, @JsonProperty("active") boolean active,
      @JsonProperty("defaultReportId") Long defaultReportId,
      @JsonProperty("achievement") Achievement achievement,
      @JsonProperty("dateRange") Filter dateRange,
      @JsonProperty("colorCodes") List<ColorCode> colorCodes,
      @JsonProperty("dashletType") String dashletType,
      @JsonProperty("totalGoalAchievement") Achievement totalGoalAchievement,
      @JsonProperty("owners") List<IdName> owners) {

    this.id = id;
    this.name = name;
    this.description = description;
    this.entityType = entityType;
    this.value = value;
    this.dimension = dimension;
    this.metric = metric;
    this.frequency = frequency;
    this.filters = filters;
    this.fieldValues = fieldValues;
    this.createdBy = createdBy;
    this.updatedBy = updatedBy;
    this.active = active;
    this.defaultReportId = defaultReportId;
    this.achievement = achievement;
    this.dateRange = dateRange;
    this.colorCodes = colorCodes;
    this.dashletType = dashletType;
    this.totalGoalAchievement = totalGoalAchievement;
    this.owners = owners;
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Achievement{
      private final Number goal;
      private final Number achieved;
      private final double percentage;

    @JsonCreator
    public Achievement(
        @JsonProperty("goal") Number goal,
        @JsonProperty("achieved") Number achieved,
        @JsonProperty("percentage") double percentage) {
      this.goal = goal;
      this.achieved = achieved;
      this.percentage = percentage;
    }

    public static Achievement from(GoalCount goalCount) {
      return new Achievement(
          goalCount.getGoal(),
          goalCount.getAchieved(),
          goalCount.getPercentage()
      );
    }

    public static Achievement from(GoalCountSummary goalCountSummary) {
      return new Achievement(
          goalCountSummary.getGoal(),
          goalCountSummary.getAchieved(),
          goalCountSummary.getPercentage()
      );
    }
  }
}
