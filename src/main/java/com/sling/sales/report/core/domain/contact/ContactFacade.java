package com.sling.sales.report.core.domain.contact;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.domain.entity.ContactCampaignRepository;
import com.sling.sales.report.core.domain.entity.ContactCompanyRepository;
import com.sling.sales.report.core.domain.entity.ContactSourceRepository;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.core.domain.field.Field;
import com.sling.sales.report.core.domain.field.FieldFacade;
import com.sling.sales.report.core.domain.field.FieldType;
import com.sling.sales.report.core.domain.field.PicklistValue;
import com.sling.sales.report.core.domain.field.PicklistValueFacade;
import com.sling.sales.report.core.domain.lead.CampaignActivity;
import com.sling.sales.report.core.domain.service.SearchService;
import com.sling.sales.report.dto.FieldDetail;
import com.sling.sales.report.error.JsonParseException;
import com.sling.sales.report.mq.event.ContactDeletedEvent;
import com.sling.sales.report.mq.event.ContactEvent;
import com.sling.sales.report.mq.event.DeleteRecordsEvent;
import com.sling.sales.report.mq.event.IdName;
import com.sling.sales.report.mq.event.PicklistValueDetail;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.UserFacade;
import com.sling.sales.report.security.jwt.InternalAuthProvider;
import java.lang.reflect.Type;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class ContactFacade {

  private final ContactRepository contactRepository;
  private final FieldFacade fieldFacade;
  private final SearchService searchService;
  private final InternalAuthProvider internalAuthProvider;
  private final UserFacade userFacade;
  private final PicklistValueFacade picklistValueFacade;
  private final ContactCustomPicklistValueRepository contactCustomPicklistValueRepository;
  private final ObjectMapper objectMapper;
  private final ContactCompanyRepository contactCompanyRepository;
  private final ContactSourceRepository contactSourceRepository;
  private final ContactCampaignRepository contactCampaignRepository;
  private final ContactCustomMultiPicklistValueRepository contactCustomMultiPicklistValueRepository;

  private static final Set<Long> EXCLUDE_TENANT_ID_FOR_DEAL_SUMMARY = Set.of(1756L, 1L, 9280L, 6903L, 8838L, 4870L);

  @Autowired
  public ContactFacade(ContactRepository contactRepository, FieldFacade fieldFacade,
      SearchService searchService,
      InternalAuthProvider internalAuthProvider, UserFacade userFacade,
      PicklistValueFacade picklistValueFacade,
      ContactCustomPicklistValueRepository contactCustomPicklistValueRepository, ObjectMapper objectMapper,
      ContactCompanyRepository contactCompanyRepository, ContactSourceRepository contactSourceRepository,
      ContactCampaignRepository contactCampaignRepository, ContactCustomMultiPicklistValueRepository contactCustomMultiPicklistValueRepository) {
    this.contactRepository = contactRepository;
    this.fieldFacade = fieldFacade;
    this.searchService = searchService;
    this.internalAuthProvider = internalAuthProvider;
    this.userFacade = userFacade;
    this.picklistValueFacade = picklistValueFacade;
    this.contactCustomPicklistValueRepository = contactCustomPicklistValueRepository;
    this.objectMapper = objectMapper;
    this.contactCompanyRepository = contactCompanyRepository;
    this.contactSourceRepository = contactSourceRepository;
    this.contactCampaignRepository = contactCampaignRepository;
    this.contactCustomMultiPicklistValueRepository = contactCustomMultiPicklistValueRepository;
  }

  public Optional<Contact> getByTenantIdAndContactId(long tenantId, long contactId) {
    return contactRepository.findByTenantIdAndId(tenantId, contactId);
  }

  public void createOrUpdateContact(ContactEvent contactEvent, Map<String, Object> eventPayload, boolean deleted, boolean isCreateEvent) {
    fromEventToContactToBeCreatedOrUpdated(contactEvent, eventPayload, deleted, isCreateEvent)
        .map(contactRepository::save)
        .subscribe();
  }

  private Mono<Contact> fromEventToContactToBeCreatedOrUpdated(ContactEvent contactEvent, Map<String, Object> eventPayload, boolean deleted,
      boolean isCreateEvent) {

    Optional<Contact> optionalContact = getByTenantIdAndContactId(contactEvent.getTenantId(), contactEvent.getId());

    if (optionalContact.isPresent() && (optionalContact.get().isDeleted() || isCreateEvent ||
        contactEvent.getUpdatedAt().before(optionalContact.get().getUpdatedAt()))) {
      return Mono.just(optionalContact.get());
    }

    String authToken = internalAuthProvider.create(contactEvent.getOwnerId(), contactEvent.getTenantId());
    Set<Long> userIds = new HashSet<>() {{
      add(contactEvent.getOwnerId());
      add(contactEvent.getCreatedBy());
      add(contactEvent.getUpdatedBy());
      add(contactEvent.getImportedBy());
    }};

    var filteredUserIds = userIds.stream()
        .filter(ObjectUtils::isNotEmpty)
        .collect(Collectors.toSet());

    Map<String, Map<Long, String>> idNameStore = contactEvent.getIdNameStore();

    var userSummariesMono = getUserNames(filteredUserIds, contactEvent, authToken);
    var contactCompanyMono = getContactCompany(idNameStore, contactEvent.getCompany(), contactEvent.getTenantId(), authToken);

    return Mono.zip(userSummariesMono, contactCompanyMono, getDealsSummary(contactEvent, authToken))
        .map(tuple -> {
          Map<String, Object> customFieldValues =
              contactEvent.getCustomFieldValues() == null ? Collections.emptyMap() : contactEvent.getCustomFieldValues();
          Map<FieldType, List<FieldDetail>> fieldDetails = getFieldDetailsByTenantId(contactEvent.getTenantId(), customFieldValues);
          var userNames = tuple.getT1();
          User ownedBy = userFacade
              .getExistingOrCreateNewUser(new User(contactEvent.getOwnerId(), contactEvent.getTenantId(), userNames.get(contactEvent.getOwnerId())));
          User createdBy = userFacade
              .getExistingOrCreateNewUser(
                  new User(contactEvent.getCreatedBy(), contactEvent.getTenantId(), userNames.get(contactEvent.getCreatedBy())));
          User updatedBy = userFacade
              .getExistingOrCreateNewUser(
                  new User(contactEvent.getUpdatedBy(), contactEvent.getTenantId(), userNames.get(contactEvent.getUpdatedBy())));
          User importedBy = contactEvent.getImportedBy() != null && userNames.containsKey(contactEvent.getImportedBy()) ? userFacade
              .getExistingOrCreateNewUser(
                  (new User(contactEvent.getImportedBy(), contactEvent.getTenantId(), userNames.get(contactEvent.getImportedBy())))) : null;
          ContactSource contactSource = getSource(idNameStore, contactEvent.getSource(), contactEvent.getTenantId());
          ContactCampaign contactCampaign = getCampaign(idNameStore, contactEvent.getCampaign(), contactEvent.getTenantId());
          ContactEventData contactEventData = new ContactEventData();
          contactEventData.putAll(eventPayload);
          ContactCompany contactCompany = tuple.getT2().isPresent() ? tuple.getT2().get() : null;
          if (optionalContact.isPresent()) {
            return getUpdatedContact(contactEvent, tuple.getT3(), fieldDetails, contactCompany, ownedBy,
                updatedBy, contactEventData, contactSource, contactCampaign, optionalContact.get(), deleted);
          }
          return getCreatedContact(contactEvent, tuple.getT3(), fieldDetails, contactCompany, ownedBy, createdBy, updatedBy, importedBy,
              contactEventData,
              contactSource, contactCampaign, deleted);
        });
  }

  private Mono<Map<Long, String>> getUserNames(Set<Long> userIds, ContactEvent contactEvent, String authToken) {
    Map<Long, String> userNames = new HashMap<>();
    IdName ownerId = getIdName(contactEvent.getIdNameStore(), contactEvent.getOwnerId(), "ownerId");
    IdName createdBy = getIdName(contactEvent.getIdNameStore(), contactEvent.getCreatedBy(), "createdBy");
    IdName updatedBy = getIdName(contactEvent.getIdNameStore(), contactEvent.getUpdatedBy(), "updatedBy");
    IdName importedBy = getIdName(contactEvent.getIdNameStore(), contactEvent.getImportedBy(), "importedBy");
    if (ObjectUtils.isNotEmpty(ownerId)) {
      userNames.put(ownerId.getId(), ownerId.getName());
    }
    if (ObjectUtils.isNotEmpty(createdBy)) {
      userNames.put(createdBy.getId(), createdBy.getName());
    }
    if (ObjectUtils.isNotEmpty(updatedBy)) {
      userNames.put(updatedBy.getId(), updatedBy.getName());
    }
    if (ObjectUtils.isNotEmpty(importedBy)) {
      userNames.put(importedBy.getId(), importedBy.getName());
    }
    if (!userNames.isEmpty()) {
      return Mono.just(userNames);
    }
    return searchService.getLookUpForUsers(userIds, authToken);
  }

  private ContactCampaign getCampaign(Map<String, Map<Long, String>> idNameStore, Long campaign, Long tenantId) {
    if (ObjectUtils.isEmpty(campaign)) {
      return null;
    }
    Optional<ContactCampaign> contactCampaign = contactCampaignRepository.findByIdAndTenantId(campaign, tenantId);
    if (contactCampaign.isPresent()) {
      return contactCampaign.get();
    }
    IdName campaignIdName = getIdName(idNameStore, campaign, "campaign");
    return contactCampaignRepository.saveAndFlush(new ContactCampaign(campaignIdName.getId(), campaignIdName.getName(), tenantId));
  }

  private ContactSource getSource(Map<String, Map<Long, String>> idNameStore, Long source, Long tenantId) {
    if (ObjectUtils.isEmpty(source)) {
      return null;
    }
    Optional<ContactSource> contactSource = contactSourceRepository.findByIdAndTenantId(source, tenantId);
    if (contactSource.isPresent()) {
      return contactSource.get();
    }
    IdName sourceIdName = getIdName(idNameStore, source, "source");
    return contactSourceRepository.saveAndFlush(new ContactSource(sourceIdName.getId(), sourceIdName.getName(), tenantId));
  }

  private Mono<Optional<ContactCompany>> getContactCompany(Map<String, Map<Long, String>> idNameStore,
      Long company, Long tenantId, String authToken) {
    if (ObjectUtils.isEmpty(company)) {
      return Mono.just(Optional.empty());
    }
    Optional<ContactCompany> optionalContactCompany = contactCompanyRepository.findByIdAndTenantId(company, tenantId);
    if (optionalContactCompany.isPresent()) {
      return Mono.just(optionalContactCompany);
    }
    IdName companyIdName = getIdName(idNameStore, company, "company");
    if (ObjectUtils.isNotEmpty(companyIdName) && ObjectUtils.isNotEmpty(companyIdName.getName())) {
      ContactCompany contactCompany = new ContactCompany(companyIdName.getId(), companyIdName.getName(), tenantId);
      return Mono.just(Optional.of(contactCompanyRepository.saveAndFlush(contactCompany)));
    }
    return searchService.getLookUpForCompanies(Set.of(company), authToken)
        .map(idName -> contactCompanyRepository.saveAndFlush(new ContactCompany(company, idName.get(company), tenantId)))
        .map(Optional::of);
  }

  private IdName getIdName(Map<String, Map<Long, String>> idNameStore, Long id, String fieldName) {
    if (id != null && ObjectUtils.isNotEmpty(idNameStore) && idNameStore.containsKey(fieldName)) {
      return new IdName(id, idNameStore.get(fieldName).get(id));
    }
    return null;
  }

  private Contact getCreatedContact(ContactEvent contactEvent, Map<Long, String> dealsSummary, Map<FieldType, List<FieldDetail>> fieldDetails,
      ContactCompany contactCompany, User ownedBy, User createdBy, User updatedBy, User importedBy, ContactEventData contactEventData,
      ContactSource contactSource, ContactCampaign contactCampaign, boolean deleted) {
    Contact createdContact = Contact
        .create(contactEvent.getId(), contactEvent.getTenantId(), contactEvent.getFirstName(), contactEvent.getLastName(),
            contactEvent.getSalutation() == null ? null : contactEvent.getSalutation(), contactEvent.getAddress(),
            contactEvent.getCity(),
            contactEvent.getState(), contactEvent.getZipcode(), contactEvent.getCountry(), contactEvent.getDnd() != null && contactEvent.getDnd(),
            contactEvent.getTimezone(),
            contactEvent.getDesignation(), contactEvent.getDepartment(), contactEvent.getCreatedAt(), contactEvent.getUpdatedAt(),
            contactEvent.getCreatedViaType(),
            contactEvent.getUpdatedViaType(),
            contactEventData,
            contactCompany,
            ownedBy,
            createdBy, updatedBy, importedBy, contactSource, contactCampaign, deleted, contactEvent.getScore());
    addAssociatedDeals(contactEvent.getTenantId(), createdContact, dealsSummary);
    addPhoneNumbers(contactEvent.getPhoneNumbers(),createdContact);
    addEmails(contactEvent.getEmails(),createdContact);
    addCampaignActivities(contactEvent.getCampaignActivities(), createdContact);
    addCustomFieldValues(fieldDetails, createdContact);
    ContactUtm contactUtm = ContactUtm
        .createContactUtm(contactEvent.getSubSource(), contactEvent.getUtmSource(), contactEvent.getUtmMedium(), contactEvent.getUtmCampaign(),
            contactEvent.getUtmTerm(),
            contactEvent.getUtmContent(), createdContact);
    return createdContact.withContactUtm(contactUtm);
  }

  private Contact getUpdatedContact(ContactEvent contactEvent, Map<Long, String> dealsSummary, Map<FieldType, List<FieldDetail>> fieldDetails,
      ContactCompany contactCompany, User ownedBy, User updatedBy, ContactEventData contactEventData, ContactSource contactSource,
      ContactCampaign contactCampaign, Contact contact, boolean deleted) {
    ContactUtm contactUtm = updateOrCreateContactUtm(contactEvent, contact);
    Contact updatedContact = contact
        .update(contactEvent.getFirstName(), contactEvent.getLastName(),
            contactEvent.getSalutation() == null ? null : contactEvent.getSalutation(),
            contactEvent.getAddress(), contactEvent.getCity(), contactEvent.getState(),
            contactEvent.getZipcode(), contactEvent.getCountry(), contactEvent.getDnd() != null && contactEvent.getDnd(), contactEvent.getTimezone(),
            contactEvent.getDesignation(),
            contactEvent.getDepartment(), contactEvent.getUpdatedAt(), contactEvent.getUpdatedViaType(), contactEventData, contactCompany, ownedBy,
            updatedBy, contactUtm, contactSource, contactCampaign, deleted, contactEvent.getScore());
    addAssociatedDeals(contactEvent.getTenantId(), updatedContact, dealsSummary);
    addPhoneNumbers(contactEvent.getPhoneNumbers(),updatedContact);
    addEmails(contactEvent.getEmails(),updatedContact);
    addCampaignActivities(contactEvent.getCampaignActivities(),updatedContact);
    addCustomFieldValues(fieldDetails, updatedContact);
    return updatedContact;
  }

  private void addEmails(ContactEmail[] emails, Contact contact) {
    contact.addContactEmails(Collections.emptySet());
    if(ObjectUtils.isEmpty(emails)){
      return;
    }
    Set<ContactEmail> collect = Arrays.stream(emails)
        .map(email -> {
          ContactEmail contactEmail = new ContactEmail(email.getType(),
              email.getValue(),email.isPrimary());
          contactEmail.setContact(contact);
          return contactEmail;
        })
        .collect(Collectors.toSet());
    contact.addContactEmails(collect);
  }

  private void addCampaignActivities(Set<CampaignActivity> campaignActivities, Contact contactToUpdate) {
    if (ObjectUtils.isEmpty(campaignActivities)) {
      return;
    }

    Set<ContactCampaignActivity> contactCampaignActivitiesToAdd = campaignActivities.stream()
        .filter(ca -> !ObjectUtils.isEmpty(ca.getActivities()))
        .flatMap(ca -> ca.getActivities().stream()
            .map(activity -> {
              ContactCampaignActivity contactCampaignActivity = new ContactCampaignActivity();
              contactCampaignActivity.setTenantId(contactToUpdate.getTenantId());
              contactCampaignActivity.setCampaignId(ca.getId());
              contactCampaignActivity.setCampaignName(ca.getName());
              contactCampaignActivity.setActivityId(activity.getId());
              contactCampaignActivity.setActivityName(activity.getName());
              contactCampaignActivity.setActivityType(activity.getType());
              contactCampaignActivity.setSentAt(activity.getSentAt());
              contactCampaignActivity.setContact(contactToUpdate);
              return contactCampaignActivity;
            }))
        .collect(Collectors.toSet());

    contactToUpdate.addContactCampaignActivities(contactCampaignActivitiesToAdd);
  }

  private ContactUtm updateOrCreateContactUtm(ContactEvent contactEvent, Contact contact) {
    if (ObjectUtils.isNotEmpty(contact.getContactUtm())) {
      return contact.getContactUtm()
          .updateContactUtm(contactEvent.getSubSource(), contactEvent.getUtmSource(), contactEvent.getUtmMedium(), contactEvent.getUtmCampaign(),
              contactEvent.getUtmTerm(),
              contactEvent.getUtmContent());
    }
    return ContactUtm
        .createContactUtm(contactEvent.getSubSource(), contactEvent.getUtmSource(), contactEvent.getUtmMedium(), contactEvent.getUtmCampaign(),
            contactEvent.getUtmTerm(),
            contactEvent.getUtmContent(), contact);
  }

  private Mono<Map<Long, String>> getDealsSummary(ContactEvent contactEvent, String authToken) {
    if (ObjectUtils.isEmpty(contactEvent.getAssociatedDeals())) {
      return Mono.just(Collections.emptyMap());
    }
    Map<String, Map<Long, String>> idNameStore = contactEvent.getIdNameStore();
    if (ObjectUtils.isNotEmpty(idNameStore) && idNameStore.containsKey("associatedDeals")) {
      return Mono.just(idNameStore.get("associatedDeals"));
    }


    if(EXCLUDE_TENANT_ID_FOR_DEAL_SUMMARY.contains(contactEvent.getTenantId())){
      List<Long> dealIds = contactEvent.getAssociatedDeals().stream().limit(2).collect(Collectors.toList());
      return searchService.getLookUpForDeals(new HashSet<>(dealIds), authToken);
    }
    return searchService.getLookUpForDeals(new HashSet<>(contactEvent.getAssociatedDeals()), authToken);
  }

  private void addAssociatedDeals(long tenantId, Contact contact, Map<Long, String> dealsSummary) {
    Set<ContactDeal> associatedDeals = dealsSummary.entrySet()
        .stream()
        .map(entry -> new ContactDeal(tenantId, entry.getKey(), entry.getValue(), contact))
        .collect(Collectors.toSet());
    contact.addAssociatedDeals(associatedDeals);
  }

  private void addCustomFieldValues(Map<FieldType, List<FieldDetail>> fieldDetails, Contact contact) {
    contact.addContactPicklistValues(getContactPicklistValues(fieldDetails, contact));
    contact.addContactCustomTextValues(getContactCustomTextValues(fieldDetails, contact));
    contact.addContactCustomNumberValues(getContactCustomNumberValues(fieldDetails, contact));
    contact.addContactCustomDatePickerValues(getContactCustomDatePickerValues(fieldDetails, contact));
    contact.addContactCustomDatetimePickerValues(getContactCustomDatetimePickerValues(fieldDetails, contact));
    contact.addContactCustomCheckboxValues(getContactCustomCheckboxValues(fieldDetails, contact));
    contact.addContactCustomMultiPicklistValues(getContactCustomMultiPicklistValues(fieldDetails, contact));
  }

  private Map<FieldType, List<FieldDetail>> getFieldDetailsByTenantId(long tenantId, Map<String, Object> customFieldValues) {
    List<Field> fields = fieldFacade.getFieldsByTenantIdAndEntityType(tenantId, EntityType.CONTACT);
    return fields
        .stream()
        .filter(field -> customFieldValues.containsKey(field.getName()))
        .map(field -> new FieldDetail(field, customFieldValues.get(field.getName())))
        .collect(Collectors.groupingBy(fieldDetail -> fieldDetail.getField().getType()));
  }

  private Set<ContactCustomPicklistValue> getContactPicklistValues(Map<FieldType, List<FieldDetail>> fieldDetails, Contact contact) {
    if (!fieldDetails.containsKey(FieldType.PICK_LIST)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.PICK_LIST)
        .stream()
        .map(fieldDetail -> {
          Long picklistValueId = new ObjectMapper().convertValue(fieldDetail.getValue(), Long.class);
          Optional<PicklistValue> picklistValueOptional = picklistValueFacade
              .getPicklistValueByFieldIdAndPicklistValueId(fieldDetail.getField().getId(), picklistValueId);
          return picklistValueOptional
              .map(picklistValue -> new ContactCustomPicklistValue(fieldDetail.getField().getId(), picklistValueId, picklistValue.getDisplayName(),
                  contact))
              .orElse(null);
        }).filter(Objects::nonNull)
        .collect(Collectors.toSet());
  }

  private Set<ContactCustomTextValue> getContactCustomTextValues(Map<FieldType, List<FieldDetail>> fieldDetails, Contact contact) {
    if (!fieldDetails.containsKey(FieldType.TEXT_FIELD)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.TEXT_FIELD)
        .stream()
        .map(fieldDetail -> {
          String valueToBeSaved = fieldDetail.getValue() == null ? null : String.valueOf(fieldDetail.getValue());
          return new ContactCustomTextValue(fieldDetail.getField().getId(), valueToBeSaved, contact);
        }).collect(Collectors.toSet());
  }

  private Set<ContactCustomNumberValue> getContactCustomNumberValues(Map<FieldType, List<FieldDetail>> fieldDetails, Contact contact) {
    if (!fieldDetails.containsKey(FieldType.NUMBER)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.NUMBER)
        .stream()
        .map(fieldDetail -> {
          Double valueToBeSaved = fieldDetail.getValue() == null ? null : Double.parseDouble(String.valueOf(fieldDetail.getValue()));
          return new ContactCustomNumberValue(fieldDetail.getField().getId(), valueToBeSaved, contact);
        }).collect(Collectors.toSet());
  }

  private Set<ContactCustomDatePickerValue> getContactCustomDatePickerValues(Map<FieldType, List<FieldDetail>> fieldDetails, Contact contact) {
    if (!fieldDetails.containsKey(FieldType.DATE_PICKER)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.DATE_PICKER)
        .stream()
        .map(fieldDetail -> getContactCustomDatePickerValue(contact, fieldDetail))
        .collect(Collectors.toSet());
  }

  private Set<ContactCustomDatetimePickerValue> getContactCustomDatetimePickerValues(Map<FieldType, List<FieldDetail>> fieldDetails,
      Contact contact) {
    if (!fieldDetails.containsKey(FieldType.DATETIME_PICKER)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.DATETIME_PICKER)
        .stream()
        .map(fieldDetail -> getContactCustomDatetimePickerValue(contact, fieldDetail))
        .collect(Collectors.toSet());
  }

  private ContactCustomDatePickerValue getContactCustomDatePickerValue(Contact contact, FieldDetail fieldDetail) {
    if (fieldDetail.getValue() == null) {
      return null;
    }
    try {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString()).toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new ContactCustomDatePickerValue(fieldDetail.getField().getId(), valueToBeSaved, contact);
    } catch (DateTimeParseException pe) {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString().substring(0, 23) + "Z").toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new ContactCustomDatePickerValue(fieldDetail.getField().getId(), valueToBeSaved, contact);
    }
  }

  private ContactCustomDatetimePickerValue getContactCustomDatetimePickerValue(Contact contact, FieldDetail fieldDetail) {
    if (fieldDetail.getValue() == null) {
      return null;
    }
    try {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString()).toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new ContactCustomDatetimePickerValue(fieldDetail.getField().getId(), valueToBeSaved, contact);
    } catch (DateTimeParseException pe) {
      Date valueToBeSaved = Date.from(
          OffsetDateTime.parse(fieldDetail.getValue().toString().substring(0, 23) + "Z").toLocalDateTime().toInstant(ZoneOffset.UTC));
      return new ContactCustomDatetimePickerValue(fieldDetail.getField().getId(), valueToBeSaved, contact);
    }
  }

  private Set<ContactCustomCheckboxValue> getContactCustomCheckboxValues(Map<FieldType, List<FieldDetail>> fieldDetails, Contact contact) {
    if (!fieldDetails.containsKey(FieldType.CHECKBOX)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.CHECKBOX)
        .stream()
        .map(fieldDetail -> {
          Boolean valueToBeSaved = fieldDetail.getValue() == null ? Boolean.FALSE : Boolean.parseBoolean(String.valueOf(fieldDetail.getValue()));
          return new ContactCustomCheckboxValue(fieldDetail.getField().getId(), valueToBeSaved, contact);
        }).collect(Collectors.toSet());
  }

  private Set<ContactCustomMultiPicklistValue> getContactCustomMultiPicklistValues(Map<FieldType, List<FieldDetail>> fieldDetails, Contact contact) {
    if (!fieldDetails.containsKey(FieldType.MULTI_PICKLIST)) {
      return Collections.emptySet();
    }
    return fieldDetails.get(FieldType.MULTI_PICKLIST)
        .stream()
        .map(fieldDetail -> {
          List<Long> picklistValues = objectMapper.convertValue(fieldDetail.getValue(), new TypeReference<>() {
            @Override
            public Type getType() {
              return super.getType();
            }
          });
          Map<Long, PicklistValue> picklistValuesByFieldId = picklistValueFacade.getPicklistValuesByFieldId(fieldDetail.getField().getId());

          List<PicklistValue> picklistValuesToBeCreated = picklistValues.stream()
              .map(picklistValuesByFieldId::get)
              .filter(Objects::nonNull)
              .collect(Collectors.toList());

          return picklistValuesToBeCreated.stream().map(picklistValue -> {
            return new ContactCustomMultiPicklistValue(fieldDetail.getField().getId(), contact.getId(),
                picklistValue.getPicklistValueId(), picklistValue.getDisplayName(), contact);
          }).collect(Collectors.toSet());

        }).flatMap(Set::stream).collect(Collectors.toSet());
  }

  public void delete(ContactDeletedEvent contactDeletedEvent) {
    Optional<Contact> optionalContact = contactRepository.findByTenantIdAndId(contactDeletedEvent.getTenantId(), contactDeletedEvent.getId());
    if (optionalContact.isPresent()) {
      log.info("Processing contact delete event with existing contact, marking soft deleted as true contactId: {}, tenantId: {}",
          optionalContact.get().getId(), optionalContact.get().getTenantId());
      Contact softDeletedContact = optionalContact.get().withDeleted(true);
      contactRepository.saveAndFlush(softDeletedContact);
      return;
    }
    createContactOnDeleteIfNotExists(contactDeletedEvent);
  }

  private void createContactOnDeleteIfNotExists(ContactDeletedEvent contactDeletedEvent) {
    ContactEvent contactEvent = new ContactEvent();
    contactEvent.setId(contactDeletedEvent.getId());
    contactEvent.setTenantId(contactDeletedEvent.getTenantId());
    contactEvent.setOwnerId(contactDeletedEvent.getOwnerId());
    contactEvent.setCreatedBy(contactDeletedEvent.getOwnerId());
    contactEvent.setUpdatedBy(contactDeletedEvent.getOwnerId());
    contactEvent.setCreatedAt(Date.from(Instant.now()));
    contactEvent.setUpdatedAt(Date.from(Instant.now()));
    try {
      HashMap<String, Object> payload =
          objectMapper.readValue(
              objectMapper.writeValueAsString(contactEvent), new TypeReference<>() {
                @Override
                public Type getType() {
                  return super.getType();
                }
              });
      createOrUpdateContact(contactEvent, payload, true, false);
    } catch (JsonProcessingException e) {
      throw new JsonParseException(e);
    }
  }

  public void updateDisplayNameByPicklistValueId(PicklistValueDetail picklistValueDetail) {
    contactCustomPicklistValueRepository.updateDisplayNameByPicklistValueId(picklistValueDetail.getId(), picklistValueDetail.getDisplayName());
  }

  public void updateMultiPicklistValueDisplayName(PicklistValueDetail picklistValueDetail) {
    contactCustomMultiPicklistValueRepository.updateDisplayNameByPicklistValueId(picklistValueDetail.getId(), picklistValueDetail.getDisplayName());
  }

  private void addPhoneNumbers(ContactPhoneNumber[] contactPhoneNumbers, Contact createdContact) {
    if(ObjectUtils.isEmpty(contactPhoneNumbers)){
      createdContact.addContactPhoneNumbers(Collections.emptySet());
      return;
    }
    Set<ContactPhoneNumber> collect = Arrays.stream(contactPhoneNumbers)
        .map(contactPhoneNumber -> {
          ContactPhoneNumber contactPhoneNumber1 = new ContactPhoneNumber(contactPhoneNumber.getId(), contactPhoneNumber.getType(),
              contactPhoneNumber.getCode(), contactPhoneNumber.getValue(), contactPhoneNumber.getDialCode(), contactPhoneNumber.isPrimary());
          contactPhoneNumber1.setContact(createdContact);
          return contactPhoneNumber1;
        })
        .collect(Collectors.toSet());
    createdContact.addContactPhoneNumbers(collect);
  }

  @EventListener
  public void deleteSoftDeletedRecords(DeleteRecordsEvent deleteRecordsEvent) {
    contactRepository.deleteRecordsOlderThan10Days();
  }
}
