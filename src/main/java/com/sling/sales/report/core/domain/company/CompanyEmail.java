package com.sling.sales.report.core.domain.company;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import com.sling.sales.report.core.domain.contact.Contact;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Entity
@Getter
@Setter
@Slf4j
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CompanyEmail implements Serializable, EntityDimension {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private  Long id;
  private  String type;
  private  String value;
  private  boolean isPrimary;
  @ManyToOne
  @JoinColumn(name = "company_id")
  @JsonIgnore
  private Company company;

  @JsonCreator
  public CompanyEmail(
      @JsonProperty("type") String type,
      @JsonProperty("value") String value,
      @JsonProperty("primary") boolean isPrimary) {
    this.type=type;
    this.value=value;
    this.isPrimary=isPrimary;
  }

}
