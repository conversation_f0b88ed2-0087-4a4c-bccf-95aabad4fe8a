package com.sling.sales.report.core.api.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.Setter;

@Setter
@EqualsAndHashCode
@JsonIgnoreProperties(ignoreUnknown = true)
public class Action implements Serializable {

  @JsonProperty("read")
  private boolean read;

  @JsonProperty("update")
  private boolean update;

  public boolean canRead() {
    return this.read;
  }

  public boolean canUpdate() {
    return this.update;
  }

}
