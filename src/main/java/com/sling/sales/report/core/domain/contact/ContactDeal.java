package com.sling.sales.report.core.domain.contact;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor
public class ContactDeal implements EntityDimension {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;
  private long dealId;
  private long tenantId;
  @NotNull
  private String name;

  @ManyToOne
  @JoinColumn(name = "contact_id")
  @JsonIgnore
  private Contact contact;

  public ContactDeal(long tenantId, long dealId, @NotNull String name, Contact contact) {
    this.tenantId = tenantId;
    this.dealId = dealId;
    this.name = name;
    this.contact = contact;
  }
}
