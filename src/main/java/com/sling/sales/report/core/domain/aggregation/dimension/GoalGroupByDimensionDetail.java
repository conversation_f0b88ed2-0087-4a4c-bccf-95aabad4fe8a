package com.sling.sales.report.core.domain.aggregation.dimension;

import java.util.List;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Predicate;
import lombok.Getter;
import org.springframework.data.jpa.domain.Specification;

@Getter
public class GoalGroupByDimensionDetail<T> {

  private final List<Expression<?>> groupByExpressions;
  private Predicate goalIdPredicate;
  private Predicate goalFieldNamePredicate;
  private Specification<T> inFieldValues;
  private boolean joinPresent;

  public GoalGroupByDimensionDetail(List<Expression<?>> groupByExpressions, Predicate goalIdPredicate, Predicate goalFieldNamePredicate,
      boolean joinPresent) {
    this.groupByExpressions = groupByExpressions;
    this.goalIdPredicate = goalIdPredicate;
    this.goalFieldNamePredicate = goalFieldNamePredicate;
    this.joinPresent = joinPresent;
  }

  public GoalGroupByDimensionDetail(List<Expression<?>> groupByExpressions, Specification<T> inFieldValues, boolean joinPresent) {
    this.groupByExpressions = groupByExpressions;
    this.inFieldValues = inFieldValues;
    this.joinPresent = joinPresent;
  }
}
