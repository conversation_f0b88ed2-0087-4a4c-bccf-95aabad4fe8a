package com.sling.sales.report.core.domain.call;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor
public class CallRelatedTo implements EntityDimension {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private long id;

  @Column(name = "call_id")
  private Long callId;

  private long entityId;
  @NotNull
  private String entity;

  private String entityName;

  private Long ownerId;

  public CallRelatedTo(Long callId, long entityId,
      @NotNull String entity, String entityName, Long ownerId) {
    this.callId = callId;
    this.entityId = entityId;
    this.entity = entity;
    this.entityName = entityName;
    this.ownerId = ownerId;
  }
}
