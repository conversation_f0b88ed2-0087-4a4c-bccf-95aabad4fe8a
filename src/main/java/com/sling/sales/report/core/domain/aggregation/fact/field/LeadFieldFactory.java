package com.sling.sales.report.core.domain.aggregation.fact.field;

import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.domain.aggregation.dimension.DateDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.GroupByDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.IdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.LongJsonBFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Metric;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NameDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.TextDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.TextJsonBDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.UserPropertyIdDimension;
import com.sling.sales.report.core.domain.entity.Pipeline;
import com.sling.sales.report.core.domain.entity.PipelineStage;
import com.sling.sales.report.core.domain.entity.PipelineStage_;
import com.sling.sales.report.core.domain.entity.Pipeline_;
import com.sling.sales.report.core.domain.entity.Product;
import com.sling.sales.report.core.domain.entity.Product_;
import com.sling.sales.report.core.domain.lead.Campaign;
import com.sling.sales.report.core.domain.lead.Campaign_;
import com.sling.sales.report.core.domain.lead.CompanyEmployees;
import com.sling.sales.report.core.domain.lead.CompanyEmployees_;
import com.sling.sales.report.core.domain.lead.CompanyIndustry;
import com.sling.sales.report.core.domain.lead.CompanyIndustry_;
import com.sling.sales.report.core.domain.lead.Lead;
import com.sling.sales.report.core.domain.lead.LeadCampaignActivity;
import com.sling.sales.report.core.domain.lead.LeadCampaignActivity_;
import com.sling.sales.report.core.domain.lead.LeadCompanyPhoneNumber;
import com.sling.sales.report.core.domain.lead.LeadCompanyPhoneNumber_;
import com.sling.sales.report.core.domain.lead.LeadEmail;
import com.sling.sales.report.core.domain.lead.LeadEmail_;
import com.sling.sales.report.core.domain.lead.LeadPhoneNumber;
import com.sling.sales.report.core.domain.lead.LeadPhoneNumber_;
import com.sling.sales.report.core.domain.lead.LeadUtm;
import com.sling.sales.report.core.domain.lead.LeadUtm_;
import com.sling.sales.report.core.domain.lead.Lead_;
import com.sling.sales.report.core.domain.lead.Source;
import com.sling.sales.report.core.domain.lead.Source_;
import com.sling.sales.report.dto.DimensionDetail;
import com.sling.sales.report.security.domain.Team;
import com.sling.sales.report.security.domain.Team_;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.User_;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
class LeadFieldFactory extends FactFieldFactory<Lead> {

  private final static Map<String, LeadUserMapping> MAPPED_PRIMARY_PROPERTIES = new HashMap<>() {{
    put("ownerId", new LeadUserMapping(Lead_.OWNED_BY, Lead_.ownedBy));
    put("createdBy", new LeadUserMapping(Lead_.CREATED_BY, Lead_.createdBy));
    put("updatedBy", new LeadUserMapping(Lead_.UPDATED_BY, Lead_.updatedBy));
    put("convertedBy", new LeadUserMapping(Lead_.CONVERTED_BY, Lead_.convertedBy));
    put("importedBy", new LeadUserMapping(Lead_.IMPORTED_BY, Lead_.importedBy));
  }};

  private final IdDimension<Lead, Pipeline> pipeline =
      new IdDimension<>(
          "pipeline",
          Lead_.ATTACHED_PIPELINE,
          Lead_.attachedPipeline,
          join -> join.get(Pipeline_.id),
          join -> join.get(Pipeline_.name),
          emptyList());

  private final IdDimension<Lead, PipelineStage> pipelineStage = new IdDimension<>(
      "pipelineStage",
      Lead_.CURRENT_STAGE,
      Lead_.currentStage,
      join -> join.get(PipelineStage_.id),
      join -> join.get(PipelineStage_.name),
      singletonList(pipeline));

  private final IdDimension<Lead, User> ownedBy = new IdDimension<>(
      "ownerId",
      Lead_.OWNED_BY,
      Lead_.ownedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdDimension<Lead, Source> source = new IdDimension<>(
      "source",
      Lead_.SOURCE,
      Lead_.source,
      join -> join.get(Source_.id),
      join -> join.get(Source_.name),
      emptyList());

  private final ManyIdDimension<Lead, Product> product = new ManyIdDimension<>(
      "products",
      Lead_.PRODUCTS,
      Lead_.products,
      join -> join.get(Product_.id),
      join -> join.get(Product_.name),
      emptyList());

  private final IdDimension<Lead, User> convertedBy = new IdDimension<>(
      "convertedBy",
      Lead_.CONVERTED_BY,
      Lead_.convertedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdDimension<Lead, User> createdBy = new IdDimension<>(
      "createdBy",
      Lead_.CREATED_BY,
      Lead_.createdBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdDimension<Lead, User> updatedBy = new IdDimension<>(
      "updatedBy",
      Lead_.UPDATED_BY,
      Lead_.updatedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final IdDimension<Lead, User> importedBy = new IdDimension<>(
      "importedBy",
      Lead_.IMPORTED_BY,
      Lead_.importedBy,
      join -> join.get(User_.id),
      join -> join.get(User_.name),
      emptyList());

  private final TextDimension<Lead> pipelineStageReason = new TextDimension<>(
      "pipelineStageReason",
      root -> root.get(Lead_.pipelineStageReason),
      singletonList(pipeline));

  private final NameDimension<Lead, CompanyIndustry> companyIndustry = new NameDimension<>(
      "companyIndustry",
      Lead_.COMPANY_INDUSTRY,
      Lead_.companyIndustry,
      join -> join.get(CompanyIndustry_.name),
      emptyList());

  private final IdDimension<Lead, CompanyEmployees> companyEmployees = new IdDimension<>(
      "companyEmployees",
      Lead_.COMPANY_EMPLOYEES,
      Lead_.companyEmployees,
      join -> join.get(CompanyEmployees_.id),
      join -> join.get(CompanyEmployees_.name),
      emptyList());

  private final IdDimension<Lead, Campaign> campaign = new IdDimension<>(
      "campaign",
      Lead_.CAMPAIGN,
      Lead_.campaign,
      join -> join.get(Campaign_.id),
      join -> join.get(Campaign_.name),
      emptyList());

  private final NumberFilterDimension<Lead, Double> companyAnnualRevenue = new NumberFilterDimension<>(
      "companyAnnualRevenue",
      root -> root.get(Lead_.companyAnnualRevenue),
      Double::valueOf
  );

  private final NumberFilterDimension<Lead, Double> requirementBudget = new NumberFilterDimension<>(
      "requirementBudget",
      root -> root.get(Lead_.requirementBudget),
      Double::valueOf
  );

  private final TextDimension<Lead> subSource = new TextDimension<>(
      "subSource",
      root -> getOrCreateLeftJoin(root, LeadUtm_.SUB_SOURCE).get(LeadUtm_.subSource),
      emptyList());

  private final TextDimension<Lead> utmSource = new TextDimension<>(
      "utmSource",
      root -> getOrCreateLeftJoin(root, LeadUtm_.UTM_SOURCE).get(LeadUtm_.utmSource),
      emptyList());

  private final TextDimension<Lead> utmMedium = new TextDimension<>(
      "utmMedium",
      root -> getOrCreateLeftJoin(root, LeadUtm_.UTM_MEDIUM).get(LeadUtm_.utmMedium),
      emptyList());

  private final TextDimension<Lead> utmCampaign = new TextDimension<>(
      "utmCampaign",
      root -> getOrCreateLeftJoin(root, LeadUtm_.UTM_CAMPAIGN).get(LeadUtm_.utmCampaign),
      emptyList());

  private final TextDimension<Lead> utmTerm = new TextDimension<>(
      "utmTerm",
      root -> getOrCreateLeftJoin(root, LeadUtm_.UTM_TERM).get(LeadUtm_.utmTerm),
      emptyList());

  private final TextDimension<Lead> utmContent = new TextDimension<>(
      "utmContent",
      root -> getOrCreateLeftJoin(root, LeadUtm_.UTM_CONTENT).get(LeadUtm_.utmContent),
      emptyList());

  private final NumberFilterDimension<Lead, Double> score = new NumberFilterDimension<>(
      "score",
      root -> root.get(Lead_.score),
      Double::valueOf
  );

  private final TextDimension<Lead> leadPhoneNumbers = new TextDimension<>(
      "phoneNumbers",
      root -> getOrCreateLeftJoinPhone(root, LeadPhoneNumber_.VALUE).get(LeadPhoneNumber_.value),
      emptyList());

  private final TextDimension<Lead> leadCompanyPhoneNumbers = new TextDimension<>(
      "companyPhones",
      root -> getOrCreateLeftJoinCompanyPhone(root, LeadCompanyPhoneNumber_.VALUE).get(LeadCompanyPhoneNumber_.value),
      emptyList());

  private final TextDimension<Lead> leadEmails = new TextDimension<>(
      "emails",
      root -> getOrCreateLeftJoinEmail(root, LeadEmail_.VALUE).get(LeadEmail_.value),
      emptyList());
  private final DateDimension<Lead> createdAt = new DateDimension<>("createdAt", root -> root.get(Lead_.createdAt));
  private final DateDimension<Lead> updatedAt = new DateDimension<>("updatedAt", root -> root.get(Lead_.updatedAt));
  private final DateDimension<Lead> convertedAt = new DateDimension<>("convertedAt", root -> root.get(Lead_.convertedAt));
  private final DateDimension<Lead> expectedClosureOn = new DateDimension<>("expectedClosureOn", root -> root.get(Lead_.expectedClosureOn));
  private final DateDimension<Lead> actualClosureDate = new DateDimension<>("actualClosureDate", root -> root.get(Lead_.actualClosureDate));
  private final DateDimension<Lead> latestActivityCreatedAt = new DateDimension<>("latestActivityCreatedAt", root -> root.get(Lead_.latestActivityCreatedAt));
  private final DateDimension<Lead> taskDueOn = new DateDimension<>("taskDueOn", root -> root.get(Lead_.taskDueOn));
  private final DateDimension<Lead> meetingScheduledOn = new DateDimension<>("meetingScheduledOn", root -> root.get(Lead_.meetingScheduledOn));
  NumberFilterDimension<Lead, Long> idFilterDimension = new NumberFilterDimension<>(
      "id",
      root -> root.get(Lead_.id),
      Long::valueOf
  );

  private final ManyIdDimension<Lead, LeadCampaignActivity> campaignActivities = new ManyIdDimension<>(
      "campaignActivities",
      Lead_.LEAD_CAMPAIGN_ACTIVITIES,
      Lead_.leadCampaignActivities,
      join -> join.get(LeadCampaignActivity_.campaignId),
      join -> join.get(LeadCampaignActivity_.campaignName),
      emptyList()
  );

  private final ManyIdDimension<Lead, LeadCampaignActivity> activities = new ManyIdDimension<>(
      "activities",
      Lead_.LEAD_CAMPAIGN_ACTIVITIES,
      Lead_.leadCampaignActivities,
      join -> join.get(LeadCampaignActivity_.activityId),
      join -> join.get(LeadCampaignActivity_.activityName),
      singletonList(campaignActivities)
  );

  private Map<String, GroupByDimension<Lead>> GROUP_BY_DIMENSIONS =
      new HashMap<>() {
        {
          put(pipeline.getName(), pipeline);
          put(pipelineStage.getName(), pipelineStage);
          put(ownedBy.getName(), ownedBy);
          put(source.getName(), source);
          put(product.getName(), product);
          put(convertedBy.getName(), convertedBy);
          put(pipelineStageReason.getName(), pipelineStageReason);
          put(companyIndustry.getName(), companyIndustry);
          put(campaign.getName(), campaign);
          put(createdAt.getName(), createdAt);
          put(updatedAt.getName(), updatedAt);
          put(expectedClosureOn.getName(), expectedClosureOn);
          put(actualClosureDate.getName(), actualClosureDate);
          put(convertedAt.getName(), convertedAt);
          put(latestActivityCreatedAt.getName(), latestActivityCreatedAt);
          put(taskDueOn.getName(), taskDueOn);
          put(meetingScheduledOn.getName(), meetingScheduledOn);
          put(createdBy.getName(), createdBy);
          put(importedBy.getName(), importedBy);
          put(updatedBy.getName(), updatedBy);
          put(convertedBy.getName(), convertedBy);
          put("city", new TextJsonBDimension<>("city", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("country", new TextJsonBDimension<>("country", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("state", new TextJsonBDimension<>("state", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("zipcode", new TextJsonBDimension<>("zipcode", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("department", new TextJsonBDimension<>("department", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("designation", new TextJsonBDimension<>("designation", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyName", new TextJsonBDimension<>("companyName", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyCity", new TextJsonBDimension<>("companyCity", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyState", new TextJsonBDimension<>(
              "companyState", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyZipcode", new TextJsonBDimension<>(
              "companyZipcode", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyCountry", new TextJsonBDimension<>(
              "companyCountry", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put(subSource.getName(), subSource);
          put(utmSource.getName(), utmSource);
          put(utmCampaign.getName(), utmCampaign);
          put(utmMedium.getName(), utmMedium);
          put(utmTerm.getName(), utmTerm);
          put(utmContent.getName(), utmContent);
          put("forecastingType", new TextJsonBDimension<>("forecastingType", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyBusinessType", new TextJsonBDimension<>("companyBusinessType", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put(companyEmployees.getName(), companyEmployees);
          put(campaignActivities.getName(), campaignActivities);
          put(activities.getName(), activities);
        }
      };

  private final Map<String, FilterDimension<Lead>> DIMENSIONS =
      new HashMap<>() {
        {
          put(pipeline.getName(), pipeline);
          put(pipelineStage.getName(), pipelineStage);
          put(ownedBy.getName(), ownedBy);
          put(source.getName(), source);
          put(product.getName(), product);
          put(convertedBy.getName(), convertedBy);
          put(pipelineStageReason.getName(), pipelineStageReason);
          put(companyIndustry.getName(), companyIndustry);
          put(campaign.getName(), campaign);
          put(createdAt.getName(), createdAt);
          put(updatedAt.getName(), updatedAt);
          put(expectedClosureOn.getName(), expectedClosureOn);
          put(actualClosureDate.getName(), actualClosureDate);
          put(convertedAt.getName(), convertedAt);
          put(latestActivityCreatedAt.getName(), latestActivityCreatedAt);
          put(taskDueOn.getName(), taskDueOn);
          put(meetingScheduledOn.getName(), meetingScheduledOn);
          put("salutation", new LongJsonBFilterDimension<>("salutation", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("country", new TextJsonBDimension<>("country", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyCountry", new TextJsonBDimension<>(
              "companyCountry", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("timezone", new TextJsonBDimension<>("timezone", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("firstName", new TextJsonBDimension<>("firstName", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("lastName", new TextJsonBDimension<>("lastName", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyName", new TextJsonBDimension<>("companyName", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("city", new TextJsonBDimension<>("city", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("state", new TextJsonBDimension<>("state", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("zipcode", new TextJsonBDimension<>("zipcode", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("department", new TextJsonBDimension<>("department", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyCity", new TextJsonBDimension<>("companyCity", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyState", new TextJsonBDimension<>(
              "companyState", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyZipcode", new TextJsonBDimension<>(
              "companyZipcode", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyBusinessType", new TextJsonBDimension<>(
              "companyBusinessType", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("requirementName", new TextJsonBDimension<>(
              "requirementName", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("designation", new TextJsonBDimension<>("designation", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put(companyAnnualRevenue.getName(), companyAnnualRevenue);
          put(requirementBudget.getName(), requirementBudget);
          put(createdBy.getName(), createdBy);
          put(importedBy.getName(), importedBy);
          put("isNew", new TextJsonBDimension<>("isNew", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("createdViaId", new TextJsonBDimension<>("createdViaId", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("createdViaName", new TextJsonBDimension<>("createdViaName", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("createdViaType", new TextJsonBDimension<>("createdViaType", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("updatedViaId", new TextJsonBDimension<>("updatedViaId", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("updatedViaName", new TextJsonBDimension<>("updatedViaName", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("updatedViaType", new TextJsonBDimension<>("updatedViaType", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put(updatedBy.getName(), updatedBy);
          put(convertedBy.getName(), convertedBy);
          put(subSource.getName(), subSource);
          put(utmSource.getName(), utmSource);
          put(utmCampaign.getName(), utmCampaign);
          put(utmMedium.getName(), utmMedium);
          put(utmTerm.getName(), utmTerm);
          put(utmContent.getName(), utmContent);
          put(score.getName(), score);
          put(idFilterDimension.getName(), idFilterDimension);
          put("forecastingType", new TextJsonBDimension<>("forecastingType", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("dnd", new TextJsonBDimension<>("dnd", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("facebook", new TextJsonBDimension<>("facebook", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("twitter", new TextJsonBDimension<>("twitter", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("linkedIn", new TextJsonBDimension<>("linkedIn", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("address", new TextJsonBDimension<>("address", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyAddress", new TextJsonBDimension<>("companyAddress", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("companyWebsite", new TextJsonBDimension<>("companyWebsite", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("requirementCurrency", new TextJsonBDimension<>("requirementCurrency", root -> root.get(Lead_.EVENT_PAYLOAD)));
          put("phoneNumbers", leadPhoneNumbers);
          put("companyPhones", leadCompanyPhoneNumbers);
          put("emails", leadEmails);
          put(companyEmployees.getName(), companyEmployees);
          put(campaignActivities.getName(), campaignActivities);
          put(activities.getName(), activities);
        }
      };

  private final List<Metric<Lead>> SUPPORTED_METRICS =
      new ArrayList<>() {{
        add(new Metric<>(
            "id",
            MetricType.COUNT,
            (root, builder) -> builder.countDistinct(root),
            null));
        add(new Metric<>(
            companyAnnualRevenue.getName(),
            MetricType.SUM,
            (root, builder) -> builder.sum(root.get(Lead_.companyAnnualRevenue)),
            null));
        add(new Metric<>(
            companyAnnualRevenue.getName(),
            MetricType.AVERAGE,
            (root, builder) -> builder.avg(root.get(Lead_.companyAnnualRevenue)),
            null));
        add(new Metric<>(
            requirementBudget.getName(),
            MetricType.SUM,
            (root, builder) -> builder.sum(root.get(Lead_.requirementBudget)),
            null));
        add(new Metric<>(
            requirementBudget.getName(),
            MetricType.AVERAGE,
            (root, builder) -> builder.avg(root.get(Lead_.requirementBudget)),
            null));
        add(new Metric<>(
            score.getName(),
            MetricType.SUM,
            (root, builder) -> builder.sum(root.get(Lead_.score)),
            null));
        add(new Metric<>(
            score.getName(),
            MetricType.AVERAGE,
            (root, builder) -> builder.avg(root.get(Lead_.score)),
            null));
      }};

  @Override
  public DimensionDetail<Lead> getDimensionDetail() {
    return new DimensionDetail<>(GROUP_BY_DIMENSIONS, DIMENSIONS, SUPPORTED_METRICS);
  }

  private Join<Lead, LeadUtm> getOrCreateLeftJoin(Root<Lead> root, String dimensionName) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<Lead, LeadUtm>) j))
        .findFirst()
        .orElseGet(() -> root.join(Lead_.leadUtm, JoinType.LEFT));
  }

  @Override
  public GroupByDimension<Lead> createUserPropertyGroupByIdDimension(GroupByField groupByField) {
    LeadUserMapping leadUserMapping = MAPPED_PRIMARY_PROPERTIES.get(groupByField.getPrimaryField());
    if ("teams".equals(groupByField.getProperty())) {
      return new UserPropertyIdDimension<>(
          groupByField.getName(),
          leadUserMapping.getMappedColumnFieldName(),
          leadUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateLeftJoin(join, groupByField.getProperty()).get(Team_.id),
          join -> getOrCreateLeftJoin(join, groupByField.getProperty()).get(Team_.name),
          emptyList(),
          groupByField.getPrimaryField(),
          groupByField.getProperty()
      );
    }
    return null;
  }

  @Override
  public FilterDimension<Lead> createUserPropertyFilterIdDimension(Filter filter) {
    LeadUserMapping leadUserMapping = MAPPED_PRIMARY_PROPERTIES.get(filter.getPrimaryField());
    if ("teams".equals(filter.getProperty())) {
      return new UserPropertyIdDimension<>(
          filter.getFieldName(),
          leadUserMapping.getMappedColumnFieldName(),
          leadUserMapping.getMappedColumnFieldPath(),
          join -> getOrCreateLeftJoin(join, filter.getProperty()).get(Team_.id),
          join -> getOrCreateLeftJoin(join, filter.getProperty()).get(Team_.name),
          emptyList(),
          filter.getPrimaryField(),
          filter.getProperty()
      );
    }
    return null;
  }

  private Join<User, Team> getOrCreateLeftJoin(Join<Lead, User> leadUserJoin, String dimensionName) {
    return leadUserJoin.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<User, Team>) j))
        .findFirst()
        .orElseGet(() -> leadUserJoin.join(User_.teams, JoinType.LEFT));
  }

  private Join<Lead, LeadPhoneNumber> getOrCreateLeftJoinPhone(Root<Lead> root, String dimensionName) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<Lead, LeadPhoneNumber>) j))
        .findFirst()
        .orElseGet(() -> root.join(Lead_.leadPhoneNumbers, JoinType.LEFT));
  }

  private Join<Lead, LeadCompanyPhoneNumber> getOrCreateLeftJoinCompanyPhone(Root<Lead> root, String dimensionName) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<Lead, LeadCompanyPhoneNumber>) j))
        .findFirst()
        .orElseGet(() -> root.join(Lead_.leadCompanyPhoneNumbers, JoinType.LEFT));
  }

  private Join<Lead, LeadEmail> getOrCreateLeftJoinEmail(Root<Lead> root, String dimensionName) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(dimensionName))
        .map(j -> ((Join<Lead, LeadEmail>) j))
        .findFirst()
        .orElseGet(() -> root.join(Lead_.leadEmails, JoinType.LEFT));
  }
}
