package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_equal;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.Format;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.query.criteria.internal.expression.LiteralExpression;
import org.hibernate.query.criteria.internal.predicate.ComparisonPredicate;
import org.springframework.data.jpa.domain.Specification;

public class ManyBooleanCustomFieldDimension<T extends Fact, V extends EntityDimension> implements FilterDimension<T>, GroupByDimension<T> {

  private final List<Operator> allowedOperators = asList(equal, not_equal);
  private final String name;
  private final String dimensionName;
  private final SetAttribute<T, V> dimensionField;
  private final Function<Join<T, V>, Path<Boolean>> pathToColumn;
  private final Function<Join<T, V>, Path<Long>> getFieldId;
  private final long fieldId;
  private Format format;

  public ManyBooleanCustomFieldDimension(String name, String dimensionName, SetAttribute<T, V> dimensionField,
      Function<Join<T, V>, Path<Boolean>> pathToColumn,
      Function<Join<T, V>, Path<Long>> getFieldId, long fieldId) {
    this.name = name;
    this.dimensionName = dimensionName;
    this.dimensionField = dimensionField;
    this.pathToColumn = pathToColumn;
    this.getFieldId = getFieldId;
    this.fieldId = fieldId;
  }

  @Override
  public ManyBooleanCustomFieldDimension<T, V> withFormat(Format format) {
    this.format = format;
    return this;
  }

  @Override
  public Format getFormat() {
    return this.format;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    switch (operator) {
      case equal:
        return equalTo(Boolean.parseBoolean(value.toString()));
      case not_equal:
        return notEqualTo(Boolean.parseBoolean(value.toString()));
      default:
        return null;
    }
  }

  private Specification<T> isFieldIdNull(Join<T, V> join) {
    return ((root, query, builder) -> builder.isNull(getFieldId.apply(join)));
  }

  private Specification<T> hasFieldId(Join<T, V> join) {
    return ((root, query, builder) -> builder.equal(getFieldId.apply(join), fieldId));
  }

  private Optional<Specification<T>> getSpecification(Join<T, V> join) {
    return Optional.ofNullable(hasFieldId(join).or(isFieldIdNull(join)));
  }

  private Specification<T> equalTo(boolean value) {
    return ((root, query, builder) ->
        builder.equal(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), value));
  }

  private Specification<T> notEqualTo(boolean value) {
    return ((root, query, builder) ->
        builder.notEqual(pathToColumn.apply(getOrCreateLeftJoin(root, query, builder)), value));
  }

  @Override
  public List<Expression<?>> getGroupByPath(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, String timezone) {
    return singletonList(pathToColumn.apply(createJoin(root, query, criteriaBuilder)));
  }

  @Override
  public Aggregate toResultKey(Tuple tuple, int columnNumber) {
    int nextColumnNumber = columnNumber + 1;
    if (ObjectUtils.isEmpty(tuple.get(columnNumber, Boolean.class))) {
      return new Aggregate(null, "No", nextColumnNumber, null);
    }
    String booleanAsString = tuple.get(columnNumber, Boolean.class).equals(Boolean.TRUE) ? "Yes" : "No";
    return new Aggregate(null, booleanAsString, nextColumnNumber, null);
  }

  protected Join<T, V> createJoin(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
    Join<T, V> join = root.join(dimensionField, JoinType.INNER);
    if (getSpecification(join).isPresent()) {
      join.on(getSpecification(join).get().toPredicate(root, query, builder));
    }
    return join;
  }

  private Join<T, V> getOrCreateLeftJoin(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
    return root.getJoins()
        .stream()
        .filter(this::isFieldIdAlreadyPresent)
        .map(j -> ((Join<T, V>) j))
        .findFirst()
        .orElseGet(() -> createJoin(root, query, builder));
  }

  private boolean isFieldIdAlreadyPresent(Join<T, ?> j) {
    if (ObjectUtils.isEmpty(j.getOn()) || ObjectUtils.isEmpty(j.getOn().getExpressions()) || j.getOn().getExpressions().size() < 2) {
      return false;
    }
    ComparisonPredicate comparisonPredicate = (ComparisonPredicate) j.getOn().getExpressions().get(1);
    LiteralExpression<Long> literalExpression = (LiteralExpression<Long>) comparisonPredicate.getRightHandOperand();
    return literalExpression.getLiteral().equals(this.fieldId);
  }
}
