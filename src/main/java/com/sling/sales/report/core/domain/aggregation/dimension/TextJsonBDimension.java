package com.sling.sales.report.core.domain.aggregation.dimension;

import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.begins_with;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.contains;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.in;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_empty;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_empty;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_not_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.is_null;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_contains;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_equal;
import static com.sling.sales.report.core.domain.aggregation.dimension.Operator.not_in;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.toList;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.Format;
import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

public class TextJsonBDimension<T extends Fact, V> implements FilterDimension<T>, GroupByDimension<T> {

  private final List<String> fieldsToBeExcludedFromCamelCase = Collections.singletonList("forecastingType");
  private final String name;
  private final List<Operator> allowedOperators = asList(equal, not_equal, contains, not_contains, in, not_in, is_empty, is_not_empty, begins_with,
      is_null, is_not_null);
  private final Function<Root<T>, Path<String>> getPathToJsonbColumn;
  private Format format;

  public TextJsonBDimension(String name,
      Function<Root<T>, Path<String>> getPathToJsonbColumn) {
    this.name = name;
    this.getPathToJsonbColumn = getPathToJsonbColumn;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return allowedOperators;
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return emptyList();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Filter filter = factFilter.getFilter();
    Object value = filter.getValue();
    switch (operator) {
      case equal:
        return hasText(String.valueOf(value));
      case not_equal:
        return doesNotHaveText(String.valueOf(value)).or(isDimensionEmpty());
      case contains:
        return containsText(String.valueOf(value));
      case not_contains:
        return doesNotContainText(String.valueOf(value)).or(isDimensionEmpty());
      case in:
        return in(getInParameters(value));
      case not_in:
        return notIn(getInParameters(value)).or(isDimensionEmpty());
      case is_empty:
      case is_null:
        return isDimensionEmpty();
      case is_not_empty:
      case is_not_null:
        return isDimensionPresent();
      case begins_with:
        return beginsWith(String.valueOf(value));
      default:
        return null;
    }
  }

  private Specification<T> beginsWith(String value) {
    return ((root, query, builder) ->
        builder.like(
            builder.lower(builder.function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name))),
            value.toLowerCase() + "%"
        )
    );
  }

  private Specification<T> isDimensionPresent() {
    return ((root, query, builder) ->
        builder.function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name))
            .isNotNull()
    );
  }

  private Specification<T> isDimensionEmpty() {
    return ((root, query, builder) ->
        builder.function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name))
            .isNull()
    );
  }

  private Specification<T> notIn(List<String> values) {
    return ((root, query, builder) -> {
      Expression<String> jsonbExpression = builder
          .function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name));
      return jsonbExpression.in(values).not();
    });
  }

  private List<String> getInParameters(Object value) {
    if (value instanceof List) {
      return (List<String>) value;
    }
    return Arrays.stream(String.valueOf(value).split(","))
        .map(String::trim)
        .collect(toList());
  }

  private Specification<T> in(List<String> values) {
    return ((root, query, builder) -> {
      Expression<String> jsonbExpression = builder
          .function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name));
      return jsonbExpression.in(values);
    });
  }

  private Specification<T> doesNotContainText(String value) {
    return ((root, query, builder) ->
        builder.notLike(
            builder.lower(builder.function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name))),
            "%" + value.toLowerCase() + "%"
        )
    );
  }

  private Specification<T> containsText(String value) {
    return ((root, query, builder) ->
        builder.like(
            builder.lower(builder.function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name))),
            "%" + value.toLowerCase() + "%"
        )
    );
  }

  private Specification<T> hasText(String value) {
    return ((root, query, builder) ->
        builder.equal(
            builder.lower(builder.function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name))),
            value.toLowerCase()
        )
    );
  }

  private Specification<T> doesNotHaveText(String value) {
    return ((root, query, builder) ->
        builder.notEqual(
            builder.lower(builder.function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), builder.literal(name))),
            value.toLowerCase()
        )
    );
  }

  @Override
  public List<Expression<?>> getGroupByPath(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, String timezone) {
    var jsonbExtractPathText = criteriaBuilder
        .function("jsonb_extract_path_text", String.class, getPathToJsonbColumn.apply(root), criteriaBuilder.literal(name));

    var expression = fieldsToBeExcludedFromCamelCase.contains(this.name) ? criteriaBuilder.upper(jsonbExtractPathText)
        : criteriaBuilder.lower(jsonbExtractPathText);

    var toString = criteriaBuilder.function("STR", String.class, expression);
    return singletonList(toString);
  }

  @Override
  public Aggregate toResultKey(Tuple tuple, int columnNumber) {
    int nextColumnNumber = columnNumber + 1;
    String nameWithOrWithoutCamelCase = fieldsToBeExcludedFromCamelCase.contains(this.name) ? tuple.get(columnNumber, String.class)
        : toCamelCase(tuple.get(columnNumber, String.class));
    return new Aggregate(null, nameWithOrWithoutCamelCase, nextColumnNumber, null);
  }

  @Override
  public GroupByDimension<T> withFormat(Format format) {
    this.format = format;
    return this;
  }

  @Override
  public Format getFormat() {
    return this.format;
  }

  private String toCamelCase(String name) {
    if (StringUtils.isNotEmpty(name)) {
      return Arrays.stream(StringUtils.splitPreserveAllTokens(name, " "))
          .map(this::capitalize)
          .collect(Collectors.joining(" "));
    }
    return name;
  }

  private String capitalize(String word) {
    return word.isEmpty() ? "" : StringUtils.capitalize(word);
  }
}
