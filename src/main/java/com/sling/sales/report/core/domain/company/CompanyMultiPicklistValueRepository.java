package com.sling.sales.report.core.domain.company;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
interface CompanyMultiPicklistValueRepository extends JpaRepository<CompanyCustomMultiPicklistValue, Long>, JpaSpecificationExecutor<CompanyCustomMultiPicklistValue> {
  @Transactional
  @Modifying
  @Query("UPDATE CompanyCustomMultiPicklistValue SET displayName = :DisplayName WHERE picklistValueId = :PicklistValueId")
  void updateDisplayNameByPicklistValueId(@Param("PicklistValueId") long picklistValueId, @Param("DisplayName") String displayName);
}
