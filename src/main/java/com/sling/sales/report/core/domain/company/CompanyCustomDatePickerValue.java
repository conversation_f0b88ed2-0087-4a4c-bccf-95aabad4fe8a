package com.sling.sales.report.core.domain.company;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor
public class CompanyCustomDatePickerValue implements EntityDimension {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private long id;
  @Column(name = "field_id")
  private long fieldId;
  @Column(name = "value")
  private Date value;

  @ManyToOne
  @JsonIgnore
  @JoinColumn(name = "company_id")
  private Company company;

  public CompanyCustomDatePickerValue(long fieldId, Date value, Company company) {
    this.fieldId = fieldId;
    this.value = value;
    this.company = company;
  }
}
