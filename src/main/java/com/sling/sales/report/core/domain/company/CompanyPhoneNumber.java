package com.sling.sales.report.core.domain.company;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import com.sling.sales.report.core.domain.lead.Lead;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Entity
@Getter
@Setter
@Slf4j
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CompanyPhoneNumber implements Serializable, EntityDimension {
  @Id
  private  Long id;
  private  String type;
  private  String code;
  private  String value;
  private  String dialCode;
  private  boolean isPrimary;
  @ManyToOne
  @JoinColumn(name = "company_id")
  @JsonIgnore
  private Company company;

  @JsonCreator
  public CompanyPhoneNumber(
      @JsonProperty("id") long id,
      @JsonProperty("type") String type,
      @JsonProperty("code") String code,
      @JsonProperty("value") String value,
      @JsonProperty("dialCode") String dialCode,
      @JsonProperty("primary") boolean isPrimary) {
    this.id = id;
    this.type=type;
    this.code=code;
    this.value=value;
    this.dialCode=dialCode;
    this.isPrimary=isPrimary;
  }

}
