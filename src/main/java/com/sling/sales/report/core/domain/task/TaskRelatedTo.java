package com.sling.sales.report.core.domain.task;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor
public class TaskRelatedTo implements EntityDimension {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private long id;

  @Column(name = "task_id")
  private Long taskId;

  private long entityId;
  @NotNull
  private String entityType;

  private String entityName;

  private Long ownerId;

  private Long tenantId;

  public TaskRelatedTo(Long taskId, long entityId,
      @NotNull String entityType, String entityName,Long ownerId, Long tenantId) {
    this.taskId = taskId;
    this.entityId = entityId;
    this.entityType = entityType;
    this.entityName = entityName;
    this.ownerId = ownerId;
    this.tenantId = tenantId;
  }
}

