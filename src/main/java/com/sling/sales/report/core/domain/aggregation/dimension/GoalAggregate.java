package com.sling.sales.report.core.domain.aggregation.dimension;

import lombok.Getter;

@Getter
public class GoalAggregate {

  private final Long id;
  private final String name;
  private final Number goal;
  private final long goalFieldValueId;
  private final int nextColumnNumber;

  public GoalAggregate(Long id, String name, Number goal, long goalFieldValueId, int nextColumnNumber) {
    this.id = id;
    this.name = name;
    this.goal = goal;
    this.goalFieldValueId = goalFieldValueId;
    this.nextColumnNumber = nextColumnNumber;
  }
}
