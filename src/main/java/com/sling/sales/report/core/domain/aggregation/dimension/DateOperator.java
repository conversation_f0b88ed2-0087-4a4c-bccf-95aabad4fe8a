package com.sling.sales.report.core.domain.aggregation.dimension;

public enum DateOperator {
  between(true),
  not_between(false),
  today(true),
  before_current_date_and_time(false),
  after_current_date_and_time(false),
  current_week(true),
  last_week(true),
  current_month(true),
  last_month(true),
  current_year(true),
  last_year(true),
  current_quarter(true),
  last_quarter(true),
  next_week(true),
  next_month(true),
  next_quarter(true),
  next_year(true),
  last_seven_days(true),
  next_seven_days(true),
  last_fifteen_days(true),
  next_fifteen_days(true),
  last_thirty_days(true),
  next_thirty_days(true),

  week_to_date(true),
  month_to_date(true),
  quarter_to_date(true),
  year_to_date(true),
  yesterday(true),
  tomorrow(true);

  private boolean allowedToProrate;

  DateOperator(boolean allowedToProrate) {
    this.allowedToProrate = allowedToProrate;
  }

  public boolean isAllowedToProrate() {
    return allowedToProrate;
  }
}
