package com.sling.sales.report.core.domain.contact;

import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import javax.persistence.Entity;
import javax.persistence.Id;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor
public class ContactCompany implements EntityDimension {

  @Id
  private long id;
  private String name;
  private long tenantId;

  public ContactCompany(long id, String name, long tenantId) {
    this.id = id;
    this.name = name;
    this.tenantId = tenantId;
  }
}
