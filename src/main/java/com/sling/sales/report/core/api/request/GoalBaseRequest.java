package com.sling.sales.report.core.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sling.sales.report.annotation.AllowedValue;
import com.sling.sales.report.core.api.response.IdName;
import com.sling.sales.report.core.domain.report.ReportType;
import com.sling.sales.report.dto.GoalDimension;
import com.sling.sales.report.dto.GoalFieldValue;
import com.sling.sales.report.dto.GoalFilter;
import com.sling.sales.report.dto.GoalFrequency;
import com.sling.sales.report.dto.GoalMetric;
import java.io.Serializable;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoalBaseRequest implements Serializable {

  @NotEmpty
  protected String name;
  protected String description;
  @NotNull
  @AllowedValue(enumClass = ReportType.class, inputValue = "${validatedValue}")
  protected String entityType;
  protected double value;
  @Valid
  @Size(min = 1, max = 1)
  protected List<GoalDimension> dimensions;
  @Valid
  @Size(min = 1, max = 1)
  protected List<GoalMetric> metrics;
  @Valid
  protected GoalFrequency frequency;
  protected GoalFilter dateRange;
  @Valid
  protected List<GoalFilter> filters;
  @NotEmpty
  @Valid
  protected List<GoalFieldValue> fieldValues;
  protected List<IdName> owners;
  protected boolean showOthersProgress;
}
