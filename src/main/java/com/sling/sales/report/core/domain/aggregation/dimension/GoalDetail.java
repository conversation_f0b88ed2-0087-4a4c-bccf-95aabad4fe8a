package com.sling.sales.report.core.domain.aggregation.dimension;

import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.domain.goal.FrequencyType;
import com.sling.sales.report.security.domain.User;
import java.util.Set;
import lombok.Getter;

@Getter
public class GoalDetail {

  private final long id;
  private final double value;
  private final boolean prorated;
  private final Filter dateRange;
  private final FrequencyType frequencyType;
  private final String timeZone;
  private final String fieldName;
  private Set<Long> fieldValues;
  private Set<User> owners;
  private User createdBy;
  private boolean showOthersProgress;


  public GoalDetail(long id, double value, boolean prorated, Filter dateRange, FrequencyType frequencyType, String timeZone,
      String fieldName, Set<User> owners, User createdBy, boolean showOthersProgress) {
    this.id = id;
    this.value = value;
    this.prorated = prorated;
    this.dateRange = dateRange;
    this.frequencyType = frequencyType;
    this.timeZone = timeZone;
    this.fieldName = fieldName;
    this.owners = owners;
    this.createdBy = createdBy;
    this.showOthersProgress = showOthersProgress;
  }

  public GoalDetail withFieldValues(Set<Long> fieldValues) {
    this.fieldValues = fieldValues;
    return this;
  }
}
