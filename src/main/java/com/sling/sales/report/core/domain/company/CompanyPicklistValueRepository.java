package com.sling.sales.report.core.domain.company;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
interface CompanyPicklistValueRepository extends JpaRepository<CompanyPicklistValue, Long>, JpaSpecificationExecutor<CompanyPicklistValue> {

  @Transactional
  @Modifying
  @Query("UPDATE CompanyPicklistValue SET displayName = :displayName WHERE picklistValueId = :picklistValueId")
  void updateDisplayNameByPicklistValueId(@Param("picklistValueId") long picklistValueId, @Param("displayName") String displayName);
}
