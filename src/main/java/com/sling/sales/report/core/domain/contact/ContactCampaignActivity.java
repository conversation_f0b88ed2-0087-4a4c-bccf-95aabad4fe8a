package com.sling.sales.report.core.domain.contact;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sling.sales.report.core.domain.aggregation.dimension.EntityDimension;
import com.sling.sales.report.core.domain.lead.ActivityType;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContactCampaignActivity implements Serializable, EntityDimension {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;
  private Long tenantId;
  private Long campaignId;
  private String campaignName;

  private Long activityId;
  private String activityName;
  @Enumerated(EnumType.STRING)
  private ActivityType activityType;
  private Date sentAt;

  @ManyToOne
  @JsonIgnore
  @JoinColumn(name = "contact_id", nullable = false)
  private Contact contact;

}
