package com.sling.sales.report.core.domain.aggregation.dimension;

import com.sling.sales.report.core.domain.aggregation.fact.Fact;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import javax.persistence.metamodel.SetAttribute;
import javax.persistence.metamodel.SingularAttribute;
import org.springframework.data.jpa.domain.Specification;

public class DealMultiFieldDimension<T extends Fact, V extends EntityDimension, D extends EntityDimension, P extends EntityDimension> implements
    FilterDimension<T> {

  private final String name;
  private final String company;
  private final String contacts;
  private final String stage;
  private final Function<Root<T>, Path<Long>> idColumnPath;
  private final Function<Root<T>, Path<String>> nameColumnPath;
  private final SingularAttribute<T, V> dealCompany;
  private final SetAttribute<T, D> associatedContacts;
  private final SingularAttribute<T, P> pipelineStage;
  private final Function<Join<T, V>, Path<String>> companyNamePath;
  private final Function<Join<T, D>, Path<String>> contactNamePath;
  private final Function<Join<T, P>, Path<String>> stageNamePath;

  public DealMultiFieldDimension(String name, String company, String contacts, String stage, Function<Root<T>, Path<Long>> idColumnPath,
      Function<Root<T>, Path<String>> nameColumnPath,
      SingularAttribute<T, V> dealCompany,
      SetAttribute<T, D> associatedContacts, SingularAttribute<T, P> pipelineStage, Function<Join<T, V>, Path<String>> companyNamePath,
      Function<Join<T, D>, Path<String>> contactNamePath, Function<Join<T, P>, Path<String>> stageNamePath) {
    this.name = name;
    this.company = company;
    this.contacts = contacts;
    this.stage = stage;
    this.idColumnPath = idColumnPath;
    this.nameColumnPath = nameColumnPath;
    this.dealCompany = dealCompany;
    this.associatedContacts = associatedContacts;
    this.pipelineStage = pipelineStage;
    this.companyNamePath = companyNamePath;
    this.contactNamePath = contactNamePath;
    this.stageNamePath = stageNamePath;
  }

  @Override
  public String getName() {
    return this.name;
  }

  @Override
  public List<Operator> getAllowedOperators() {
    return List.of(Operator.multi_field);
  }

  @Override
  public List<FilterDimension<T>> getRequiredFilters() {
    return Collections.emptyList();
  }

  @Override
  public Specification<T> toSpecification(Operator operator, FactFilter<T> factFilter) {
    Object value = factFilter.getFilter().getValue();
    return (root, query, builder) -> {
      var idInLower = builder.lower(idColumnPath.apply(root).as(String.class));
      var dealIdLike = builder.like(idInLower, "%" + value.toString().toLowerCase() + "%");
      var dealNameInLower = builder.lower(
          builder.function("jsonb_extract_path_text", String.class, nameColumnPath.apply(root), builder.literal("name")));
      var dealNameLike = builder.like(dealNameInLower, "%" + value.toString().toLowerCase() + "%");
      var companyNameInLower = builder.lower(companyNamePath.apply(getOrCreateLeftJoinWithDealCompany(root)));
      var companyNameLike = builder.like(companyNameInLower, "%" + value.toString().toLowerCase() + "%");
      var contactNameInLower = builder.lower(contactNamePath.apply(getOrCreateLeftJoinWithDealContacts(root)));
      var contactNameLike = builder.like(contactNameInLower, "%" + value.toString().toLowerCase() + "%");
      var stageNameInLower = builder.lower(stageNamePath.apply(getOrCreateLeftJoinWithPipelineStage(root)));
      var stageNameLike = builder.like(stageNameInLower, "%" + value.toString().toLowerCase() + "%");
      return builder.or(dealIdLike, dealNameLike, companyNameLike, contactNameLike, stageNameLike);
    };
  }

  private Join<T, V> getOrCreateLeftJoinWithDealCompany(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(company))
        .map(j -> ((Join<T, V>) j))
        .findFirst()
        .orElseGet(() -> root.join(dealCompany, JoinType.LEFT));
  }

  private Join<T, D> getOrCreateLeftJoinWithDealContacts(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(contacts))
        .map(j -> ((Join<T, D>) j))
        .findFirst()
        .orElseGet(() -> root.join(associatedContacts, JoinType.LEFT));
  }

  private Join<T, P> getOrCreateLeftJoinWithPipelineStage(Root<T> root) {
    return root.getJoins()
        .stream()
        .filter(j -> j.getAttribute().getName().equals(stage))
        .map(j -> ((Join<T, P>) j))
        .findFirst()
        .orElseGet(() -> root.join(pipelineStage, JoinType.LEFT));
  }
}
