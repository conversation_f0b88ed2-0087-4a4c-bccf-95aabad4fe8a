SET session_replication_role = replica;
DELETE FROM task_custom_picklist_value;
DELETE FROM task_type;
DELETE FROM task_status;
DELETE FROM task_priority;
DELETE FROM task;
DELETE FROM users;

INSERT INTO users(id, tenant_id, name)
VALUES (200, 11, '<PERSON> Stark');

INSERT INTO task_type(id, tenant_id, name)
VALUES (500, 11, 'Call');

INSERT INTO task_status(id, tenant_id, name)
VALUES (600, 11, 'Scheduled');

INSERT INTO task_priority(id, tenant_id, name)
VALUES (700, 11, 'High');

INSERT INTO task(id, tenant_id, name, due_date, assigned_to, owner, created_by, updated_by, created_at, updated_at, event_payload, task_type, task_status, task_priority, deleted)
VALUES (2401, 5, 'Task1', '2020-02-02 05:43:45.717000', 200, 200, 200, 200, '2020-02-02 05:43:45.717000','2020-02-02 05:43:45.717000', '{"id": 2400}', 500, 600, 700, false),
(2403, 5, 'Task3', '2020-02-02 05:43:45.717000', 200, 200, 200, 200, '2020-02-02 05:43:45.717000','2020-02-02 05:43:45.717000', '{"id": 2400}', 500, 600, 700, true);

INSERT INTO task_custom_picklist_value(id, task_id, picklist_value_id, display_name, field_id) OVERRIDING SYSTEM VALUE
VALUES (1000, 2401, 200, 'valueThree', 100),
(1001, 2401, 201, 'valueFour', 101),
(1002, 2401, 202, 'valueFive', 1001);