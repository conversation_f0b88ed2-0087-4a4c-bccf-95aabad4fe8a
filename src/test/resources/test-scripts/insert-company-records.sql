SET session_replication_role = replica;
DELETE FROM picklist_value;
DELETE FROM company_custom_text_value WHERE id IN (1000,1001);
DELETE FROM company_custom_number_value WHERE id IN (1000,1001);
DELETE FROM company_custom_date_picker_value WHERE id IN (1000,1001);
DELETE FROM company_custom_datetime_picker_value WHERE id IN (1000,1001,1002);
DELETE FROM company_custom_checkbox_value WHERE id IN (1000,1001);
DELETE FROM company_picklist_value WHERE id IN (1000,1001,1002,1003,1004,1005);
DELETE FROM field;
DELETE FROM task_related_to;
DELETE FROM task;
DELETE FROM call_related_to;
DELETE FROM call_associated_to;
DELETE FROM call;
DELETE FROM company_phone_number;
DELETE FROM company_email;
DELETE FROM company where tenant_id = 1234;
DELETE FROM team_user;
DELETE FROM team;
DELETE FROM users WHERE tenant_id=1234;
DELETE FROM company_custom_multi_picklist_value;

INSERT INTO users(id, tenant_id, name)
VALUES (12345, 1234, 'Tony Stark'),
(12346, 1234, 'Monty Stark');

INSERT INTO team(id, name, tenant_id)
VALUES
(1111, 'Team1', 999);

INSERT INTO team_user(user_id, team_id)
VALUES
(12345, 1111);

INSERT INTO field (id, field_id, name, tenant_id, type, entity_type, filterable, standard) OVERRIDING SYSTEM VALUE
VALUES (215, 215, 'customCompanyPicklist', 11, 'PICK_LIST', 'COMPANY', false, true),
(216, 216, 'customText', 11, 'TEXT_FIELD', 'COMPANY', false, true),
(217, 217, 'customTextOne', 11, 'TEXT_FIELD', 'COMPANY', false, true),
(218, 218, 'customNumber', 11, 'NUMBER', 'COMPANY', false, true),
(219, 219, 'customNumberOne', 11, 'NUMBER', 'COMPANY', false, true),
(220, 220, 'customDatePicker', 11, 'DATE_PICKER', 'COMPANY', false, true),
(221, 221, 'customDatePickerOne', 11, 'DATE_PICKER', 'COMPANY', false, true),
(222, 222, 'customDatetimePicker', 11, 'DATETIME_PICKER', 'COMPANY', false, true),
(223, 223, 'customDatetimePickerOne', 11, 'DATETIME_PICKER', 'COMPANY', false, true),
(224, 224, 'customCheckbox', 11, 'CHECKBOX', 'COMPANY', false, true),
(225, 225, 'customCompanyPicklistOne', 11, 'PICK_LIST', 'COMPANY', false, true),
(226, 226, 'customCompanyPicklistTwo', 11, 'PICK_LIST', 'COMPANY', false, true),
(227, 227, 'multiPicklist', 888, 'MULTI_PICKLIST', 'COMPANY', false, true);

INSERT INTO company (id, name, tenant_id, owned_by, created_at, updated_at, event_payload, created_by, updated_by)
VALUES (9000, 'new company', 1234, 12345, '2020-02-02 05:43:45.717000', '2020-02-02 05:43:45.717000', '{}', 12345, 12345);

INSERT INTO company (id, name, tenant_id, owned_by, industry_id, industry_value, number_of_employees_id, number_of_employees_value,
business_type_id, business_type_value, country_id, country_value, city, state, created_at, updated_at, event_payload, revenue_currency_id, revenue_value, created_by, updated_by, imported_by, deleted, unique_text1)
VALUES (9001, 'company one', 1234, 12345, 1, 'Software', 2, '1-50', 3, 'Accounting', 4, 'India', 'Pune', 'Maharashtra', '2020-02-02 05:43:45.717000', '2020-02-02 05:43:45.717000', '{}', 413, 130.0, 12345, 12345, 12345, false, 'some text'),
(9002, 'company two', 1234, 12345, 5, 'Computer', 6, '1-60', 7, 'Automobile', 8, 'US', 'California', 'North Carolina', '2020-02-02 05:43:45.717000', '2020-02-02 05:43:45.717000', '{}', 413, 120.0, 12345, 12345, 12345, false, 'some new text'),
(9003, 'company three', 1234, 12346, 9, 'Government Body', 10, '1-70', 11, 'Military', 12, 'Europe', 'Paris', 'Ukraine', '2020-02-02 05:43:45.717000', now() + interval '1 days' + interval '6 min', '{}', 413, 150.0, 12345, 12345, null, false, null),
(9004, 'company four', 1234, 12346, 13, 'Pharma', 14, '1-80', 15, 'Medical', 16, 'Uganda', 'Uganda city', 'Uganda state', '2020-02-02 05:43:45.717000', '2020-02-02 05:43:45.717000', '{}', 413, 150.0,12345, 12345, null, false, null),
(9006, 'company six', 1234, 12346, 13, 'Pharma', 14, '1-80', 15, 'Medical', 16, 'Uganda', 'Uganda city', 'Uganda state', '2020-02-02 05:43:45.717000', '2020-02-02 05:43:45.717000', '{}', 413, 150.0,12345, 12345, null, true, null);

INSERT INTO company_picklist_value(id, company_id, picklist_value_id, display_name, field_id) OVERRIDING SYSTEM VALUE
VALUES (1000, 9001, 200, 'valueThree', 100),
(1001, 9001, 201, 'valueFour', 101),
(1002, 9002, 202, 'valueFive', 215),
(1003, 9003, 203, 'valueSix', 215),
(1004, 9001, 204, 'valueSeven', 225),
(1005, 9002, 204, 'valueSeven', 226);

INSERT INTO company_custom_text_value(id, company_id, field_id, value) OVERRIDING SYSTEM VALUE
VALUES (1000, 9001, 216, 'test1'),
(1001, 9002, 216, 'report test');

INSERT INTO company_custom_number_value(id, company_id, field_id, value) OVERRIDING SYSTEM VALUE
VALUES (1000, 9001, 218, 20.0),
(1001, 9002, 218, 25.0);

INSERT INTO company_custom_date_picker_value(id, company_id, field_id, value) OVERRIDING SYSTEM VALUE
VALUES (1000, 9001, 220, '2020-02-02 05:43:45.717000'),
(1001, 9002, 220, '2020-02-02 05:43:45.717000');

INSERT INTO company_custom_datetime_picker_value(id, company_id, field_id, value) OVERRIDING SYSTEM VALUE
VALUES (1000, 9001, 222, '2020-02-02 05:43:45.717000'),
(1001, 9002, 222, '2020-02-02 05:43:45.717000'),
(1002, 9003, 222, now() - interval '1 days' + interval '6 min');

INSERT INTO company_custom_checkbox_value(id, company_id, field_id, value) OVERRIDING SYSTEM VALUE
VALUES (1000, 9001, 224, true),
(1001, 9002, 224, false);

INSERT INTO company_custom_multi_picklist_value(id, company_id, field_id, picklist_value_id, display_name) OVERRIDING SYSTEM VALUE
VALUES (1000, 9001, 227, 1, 'Value 1'),
 (1001, 9001, 227, 2, 'Value 2'),
 (1002, 9002, 227, 2, 'Value 2'),
 (1003, 9002, 227, 3, 'Value 3');

INSERT INTO company_phone_number(id,type, code, value, dial_code, is_primary,company_id)
VALUES (89001, 'MOBILE','IN','9090909090', '+91',true, 9001);

INSERT INTO company_email(id,type, value, is_primary, company_id)
OVERRIDING SYSTEM VALUE
VALUES (89001, 'MOBILE', '<EMAIL>', true, 9001);

INSERT INTO task(id, tenant_id, name, due_date, assigned_to, owner, created_by, updated_by, created_at, updated_at, event_payload)
VALUES (2400, 5, 'Task1', '2020-02-02 05:43:45.717000', 12345, 12345, 12345, 12345, '2020-02-02 05:43:45.717000','2020-02-02 05:43:45.717000', '{"id": 2400}'),
(2401, 5, 'Task2', '2020-02-02 05:43:45.717000', 12345, 12345, 12345, 12345, '2020-02-02 05:43:45.717000','2020-02-02 05:43:45.717000', '{"id": 2400}');

INSERT INTO task_related_to(id,task_id, entity_id, entity_type, entity_name, owner_id, tenant_id) OVERRIDING SYSTEM VALUE
VALUES (1001, 2400, 9001, 'COMPANY', 'company one', 12345, 1234),
(1002, 2401, 9001, 'COMPANY', 'company one', 12345, 1234);

INSERT INTO call(id, tenant_id, owner, call_type, outcome, duration_in_minutes, recording_available, created_at, updated_at, event_payload, phone_number)
VALUES
(2400, 1234, 12345, 'incoming','Successful',50, true, '2020-02-02 05:43:45.717000', '2020-02-02 05:43:45.717000', '{"id": 2400}', '*********'),
(2401, 1234, 12345, 'incoming','Successful',50, true, '2020-02-02 05:43:45.717000', '2020-02-02 05:43:45.717000', '{"id": 2400}', '*********');

INSERT INTO call_related_to(id, call_id, entity_id, entity, entity_name, owner_id) OVERRIDING SYSTEM VALUE
VALUES
(11, 2400, 9001, 'lead', 'lead one', 12345),
(12, 2400, 9001, 'company', 'company one', 12345);

INSERT INTO call_associated_to(id, call_id, entity_id, entity, entity_name, owner_id) OVERRIDING SYSTEM VALUE
VALUES
(11, 2400, 9001, 'lead', 'lead one', 12345),
(12, 2400, 9001, 'company', 'company one', 12345);