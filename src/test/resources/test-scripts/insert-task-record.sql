SET session_replication_role = replica;
DELETE FROM users;
DELETE FROM task_type;
DELETE FROM task_status;
DELETE FROM task_priority;
DELETE FROM task_related_to;
DELETE FROM task;

INSERT INTO users(id, tenant_id, name)
VALUES (200, 11, '<PERSON>'),
(201, 11, '<PERSON>');

INSERT INTO task_type(id, tenant_id, name)
VALUES (500, 11, 'Call');

INSERT INTO task_status(id, tenant_id, name)
VALUES (600, 11, 'Scheduled');

INSERT INTO task_priority(id, tenant_id, name)
VALUES (700, 11, 'High');

INSERT INTO task(id, tenant_id, name, due_date, assigned_to, owner, created_by, updated_by, created_at, updated_at, event_payload, task_type, task_status, task_priority)
VALUES (2400, 5, 'Task1', '2020-02-02 05:43:45.717000', 201, 200, 200, 200, '2020-02-02 05:43:45.717000','2020-02-02 05:43:45.717000', '{"id": 2400}', 500, 600, 700),
(2401, 5, 'Task2', '2020-02-02 05:43:45.717000', 201, 201, 201, 201, '2020-02-02 05:43:45.717000','2020-02-02 05:43:45.717000', '{"id": 2400}', 500, 600, 700);

INSERT INTO task_related_to(id,task_id, entity_id, entity_type, entity_name, owner_id, tenant_id) OVERRIDING SYSTEM VALUE
VALUES (1001, 2400, 1, 'LEAD', 'lead one', 200, 11),
(1002, 2400, 1, 'DEAL', 'deal one', 200, 11),
(1003, 2400, 1, 'CONTACT', 'contact one', 200, 11),
(1004, 2401, 1, 'LEAD', 'lead one', 201, 11),
(1005, 2401, 1, 'CONTACT', 'contact one', 201, 11),
(1006, 2401, 1, 'DEAL', 'deal one', 201, 11);