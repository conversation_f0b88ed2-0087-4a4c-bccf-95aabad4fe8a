{"entity": {"id": 9001, "name": "Test", "updatedBy": {"id": 10, "name": "<PERSON>"}, "ownedBy": {"id": 123456, "name": "<PERSON>"}}, "oldEntity": {"id": 9001, "name": "Test", "updatedBy": {"id": 20, "name": "Test User20"}, "ownedBy": {"id": 12345, "name": "Test User20"}}, "metadata": {"tenantId": 1234, "userId": 123456, "entityType": "COMPANY", "entityId": 9001, "entityAction": "REASSIGNED", "workflowId": null, "executedWorkflows": [], "executeWorkflow": true, "sendNotification": true, "publishUsage": true}}