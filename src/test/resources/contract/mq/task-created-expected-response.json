{"id": 2405, "tenantId": 5, "name": "Prepare <PERSON>", "dueDate": 1592850600000, "assignedTo": {"id": 114, "tenantId": 5, "permissions": null, "name": "<PERSON>", "currency": null}, "owner": {"id": 114, "tenantId": 5, "permissions": null, "name": "<PERSON>", "currency": null}, "createdBy": {"id": 114, "tenantId": 5, "permissions": null, "name": "<PERSON>", "currency": null}, "updatedBy": {"id": 114, "tenantId": 5, "permissions": null, "name": "<PERSON>", "currency": null}, "createdAt": 1592850600000, "updatedAt": 1592850600000, "eventPayload": {"entity": {"id": 2405, "name": "Prepare <PERSON>", "type": {"id": 1700, "name": "Reminder"}, "owner": {"id": 114, "name": "<PERSON>"}, "status": {"id": 1800, "name": "Scheduled"}, "dueDate": "2020-06-22T18:30:00.000+0000", "priority": {"id": 1900, "name": "High"}, "tenantId": 5, "createdAt": "2020-06-22T18:30:00.000+0000", "createdBy": {"id": 114, "name": "<PERSON>"}, "updatedAt": "2020-06-22T18:30:00.000+0000", "updatedBy": {"id": 114, "name": "<PERSON>"}, "assignedTo": {"id": 114, "name": "<PERSON>"}, "completedAt": "2020-06-29T18:30:00.000+0000", "entityDetails": [{"ownerId": 12, "entityId": 123, "entityName": "test lead", "entityType": "LEAD"}, {"ownerId": 12, "entityId": 13, "entityName": "test contact", "entityType": "CONTACT"}], "originalDueDate": "2020-06-22T18:30:00.000+0000", "customFieldValues": {"myTextOne": "TextValueOne", "myTextTwo": "TextValueTwo", "myNumberOne": 20.0, "myNumberTwo": 25.0, "myCheckboxOne": true, "myCheckboxTwo": true, "myPicklistOne": 1, "myPicklistTwo": 2, "myDatePickerOne": "2019-10-01T05:43:45.717Z", "myDatePickerTwo": "2019-10-02T05:43:45.717Z", "myDatetimePickerOne": "2019-10-01T05:43:45.717Z", "myDatetimePickerTwo": "2019-10-02T05:43:45.717Z"}}}, "taskType": {"id": 1700, "name": "Reminder", "tenantId": 5}, "taskStatus": {"id": 1800, "name": "Scheduled", "tenantId": 5}, "taskPriority": {"id": 1900, "name": "High", "tenantId": 5}, "originalDueDate": 1592850600000, "completedAt": 1593455400000, "deleted": false, "relatedTo": [{"id": 2, "taskId": 2405, "entityId": 123, "entityType": "LEAD", "entityName": "test lead", "ownerId": 12, "tenantId": 5}, {"id": 1, "taskId": 2405, "entityId": 13, "entityType": "CONTACT", "entityName": "test contact", "ownerId": 12, "tenantId": 5}], "taskPicklistValues": [], "taskCustomTextValues": [], "taskCustomNumberValues": [], "taskCustomDatePickerValues": [], "taskCustomDatetimePickerValues": [], "taskCustomCheckboxValues": []}