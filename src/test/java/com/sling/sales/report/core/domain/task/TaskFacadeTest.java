package com.sling.sales.report.core.domain.task;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.core.domain.field.Field;
import com.sling.sales.report.core.domain.field.FieldFacade;
import com.sling.sales.report.core.domain.field.FieldType;
import com.sling.sales.report.core.domain.field.PicklistValue;
import com.sling.sales.report.core.domain.field.PicklistValueFacade;
import com.sling.sales.report.mq.event.IdName;
import com.sling.sales.report.mq.event.PicklistValueDetail;
import com.sling.sales.report.mq.event.TaskDeletedEvent;
import com.sling.sales.report.mq.event.TaskEvent;
import com.sling.sales.report.mq.event.TaskEvent.EventEntity;
import com.sling.sales.report.setup.TestDatabaseInitializer;
import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.sql.Date;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ExtendWith(SpringExtension.class)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestDatabaseInitializer.class})
class TaskFacadeTest {

  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private TaskRepository taskRepository;
  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  private TaskFacade taskFacade;
  @MockBean
  private FieldFacade fieldFacade;
  @Autowired
  private TaskCustomPicklistValueRepository taskCustomPicklistValueRepository;
  @MockBean
  private PicklistValueFacade picklistValueFacade;

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenExistingTask_shouldUpdateIt() throws JSONException, IOException {
    verifyTaskUpsert();
  }

  @Test
  @Transactional
  public void givenNewTask_shouldCreateIt() throws JSONException, IOException {
    String taskEventStr = getResourceAsString("classpath:contract/mq/create-or-update-task-event.json");
    HashMap<String, Object> payload = objectMapper.readValue(taskEventStr, new TypeReference<>() {
      @Override
      public Type getType() {
        return super.getType();
      }
    });
    var taskEvent = objectMapper.readValue(taskEventStr, TaskEvent.class);
    var entity = taskEvent.getEntity();
    Field fieldOne = new Field(3L, 6L, entity.getTenantId(), "myPicklistOne", FieldType.PICK_LIST, EntityType.TASK, true, false, null);
    Field fieldTwo = new Field(4L, 7L, entity.getTenantId(), "myPicklistTwo", FieldType.PICK_LIST, EntityType.TASK, true, false, null);
    Field fieldThree = new Field(3L, 6L, entity.getTenantId(), "myTextOne", FieldType.TEXT_FIELD, EntityType.TASK, true, false, null);
    Field fieldFour = new Field(4L, 7L, entity.getTenantId(), "myTextTwo", FieldType.TEXT_FIELD, EntityType.TASK, true, false, null);
    Field fieldFive = new Field(3L, 6L, entity.getTenantId(), "myNumberOne", FieldType.NUMBER, EntityType.TASK, true, false, null);
    Field fieldSix = new Field(4L, 7L, entity.getTenantId(), "myNumberTwo", FieldType.NUMBER, EntityType.TASK, true, false, null);
    Field fieldSeven = new Field(3L, 6L, entity.getTenantId(), "myDatePickerOne", FieldType.DATE_PICKER, EntityType.TASK, true, false, null);
    Field fieldEight = new Field(4L, 7L, entity.getTenantId(), "myDatePickerTwo", FieldType.DATE_PICKER, EntityType.TASK, true, false, null);
    Field fieldNine = new Field(3L, 6L, entity.getTenantId(), "myDatetimePickerOne", FieldType.DATETIME_PICKER, EntityType.TASK, true, false,
        null);
    Field fieldTen = new Field(4L, 7L, entity.getTenantId(), "myDatetimePickerTwo", FieldType.DATETIME_PICKER, EntityType.TASK, true, false, null);
    Field fieldEleven = new Field(3L, 6L, entity.getTenantId(), "myCheckboxOne", FieldType.CHECKBOX, EntityType.TASK, true, false,
        null);
    Field fieldTwelve = new Field(4L, 7L, entity.getTenantId(), "myCheckboxTwo", FieldType.CHECKBOX, EntityType.TASK, true, false, null);
    given(fieldFacade.getFieldsByTenantIdAndEntityType(entity.getTenantId(), EntityType.TASK))
        .willReturn(List.of(fieldOne, fieldTwo, fieldThree, fieldFour, fieldFive, fieldSix, fieldSeven, fieldEight, fieldNine, fieldTen, fieldEleven,
            fieldTwelve));
    given(fieldFacade.getFieldsByTenantIdAndEntityType(entity.getTenantId(), EntityType.TASK))
        .willReturn(List.of(fieldOne, fieldTwo, fieldThree, fieldFour, fieldFive, fieldSix, fieldSeven, fieldEight, fieldNine, fieldTen, fieldEleven,
            fieldTwelve));
    given(picklistValueFacade.getPicklistValueByFieldIdAndPicklistValueId(3L, 1L)).willReturn(
        Optional.of(new PicklistValue(3L, 1L, "valueOne", fieldOne)));
    given(picklistValueFacade.getPicklistValueByFieldIdAndPicklistValueId(4L, 2L)).willReturn(
        Optional.of(new PicklistValue(4L, 3L, "valueTwo", fieldTwo)));

    //when
    taskEvent.getEntity().setPriority(null);
    taskFacade.create(taskEvent, payload, false);

    //then
    var task = taskRepository.findById(2400L).get();
    assertThat(task.getName()).isEqualTo("Prepare Jarvis");
    assertThat(task.getTaskType().getName()).isEqualTo("Reminder");
    assertThat(task.getTaskType().getId()).isEqualTo(1700);
    assertThat(task.getTaskStatus().getName()).isEqualTo("Scheduled");
    assertThat(task.getTaskStatus().getId()).isEqualTo(1800);
    assertThat(task.getCompletedAt()).isEqualTo("2020-06-29T18:30:00.000+0000");
    assertThat(task.getOriginalDueDate()).isEqualTo("2020-06-22T18:30:00.000+0000");
    assertThat(task.getTaskPriority()).isNull();
    assertThat(task.getRelatedTo())
        .isNotEmpty()
        .extracting(TaskRelatedTo::getEntityName)
        .containsExactlyInAnyOrder("test lead", "test contact");
    var actualPayload = objectMapper.writeValueAsString(task.getEventPayload());
    JSONAssert.assertEquals(actualPayload, taskEventStr, JSONCompareMode.LENIENT);
    assertThat(task.getTaskPicklistValues())
        .isNotEmpty()
        .extracting(TaskCustomPicklistValue::getDisplayName)
        .containsExactlyInAnyOrder("valueOne", "valueTwo");
    assertThat(task.getTaskCustomTextValues())
        .isNotEmpty()
        .extracting(TaskCustomTextValue::getValue)
        .containsExactlyInAnyOrder("TextValueOne", "TextValueTwo");
    assertThat(task.getTaskCustomNumberValues())
        .isNotEmpty()
        .extracting(TaskCustomNumberValue::getValue)
        .containsExactlyInAnyOrder(20.0, 25.0);
    assertThat(task.getTaskCustomDatePickerValues())
        .anyMatch(taskCustomDatePickerValue -> taskCustomDatePickerValue.getValue().equals(Date.from(Instant.parse("2019-10-01T05:43:45.717Z"))))
        .anyMatch(taskCustomDatePickerValue -> taskCustomDatePickerValue.getValue().equals(Date.from(Instant.parse("2019-10-02T05:43:45.717Z"))));
    assertThat(task.getTaskCustomDatetimePickerValues())
        .anyMatch(
            taskCustomDatetimePickerValue -> taskCustomDatetimePickerValue.getValue().equals(Date.from(Instant.parse("2019-10-01T05:43:45.717Z"))))
        .anyMatch(
            taskCustomDatetimePickerValue -> taskCustomDatetimePickerValue.getValue().equals(Date.from(Instant.parse("2019-10-02T05:43:45.717Z"))));
    assertThat(task.getTaskCustomCheckboxValues())
        .allMatch(TaskCustomCheckboxValue::getValue);
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record-to-delete.sql")
  public void givenTaskDeletedEvent_withNonExistingTask_shouldCreateIt() {
    //when
    taskFacade.delete(new TaskDeletedEvent(2402, 5));
    //then
    Optional<Task> optionalTask = taskRepository.findByTenantIdAndId(5L, 2402L);
    assertThat(optionalTask).isPresent();
    assertThat(optionalTask.get().isDeleted()).isTrue();
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record-to-delete.sql")
  public void givenTaskCreatedEvent_withAlreadyDeletedTask_shouldNotCreateIt() {
    //given
    long tenantId = 5L;
    long userId = 200L;
    TaskEvent event = new TaskEvent();
    EventEntity eventEntity = new EventEntity();
    eventEntity.setId(2403L);
    eventEntity.setTenantId(tenantId);
    eventEntity.setDueDate(Date.from(Instant.now()));
    eventEntity.setAssignedTo(new IdName(userId, "AssignedTo"));
    eventEntity.setOwner(new IdName(userId, "Owner"));
    eventEntity.setCreatedBy(new IdName(userId, "CreatedBy User"));
    eventEntity.setUpdatedBy(new IdName(userId, "UpdatedBy User"));
    eventEntity.setCreatedAt(Date.from(Instant.now()));
    eventEntity.setUpdatedAt(Date.from(Instant.now()));
    event.setEntity(eventEntity);
    //when
    taskFacade.create(event, new HashMap<>(), false);
    //then
    Optional<Task> optionalTask = taskRepository.findByTenantIdAndId(5L, 2403L);
    assertThat(optionalTask).isPresent();
    assertThat(optionalTask.get().isDeleted()).isTrue();
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record-to-delete.sql")
  public void givenTaskCreatedEvent_withExistingTask_shouldNotUpdateIt() {
    //given
    long tenantId = 5L;
    long userId = 200L;
    TaskEvent event = new TaskEvent();
    EventEntity eventEntity = new EventEntity();
    eventEntity.setId(2401L);
    eventEntity.setName("updated name");
    eventEntity.setTenantId(tenantId);
    eventEntity.setDueDate(Date.from(Instant.now()));
    eventEntity.setAssignedTo(new IdName(userId, "AssignedTo"));
    eventEntity.setOwner(new IdName(userId, "Owner"));
    eventEntity.setCreatedBy(new IdName(userId, "CreatedBy User"));
    eventEntity.setUpdatedBy(new IdName(userId, "UpdatedBy User"));
    eventEntity.setCreatedAt(Date.from(Instant.now()));
    eventEntity.setUpdatedAt(Date.from(Instant.now()));
    event.setEntity(eventEntity);
    //when
    taskFacade.create(event, new HashMap<>(), false);
    //then
    Optional<Task> optionalTask = taskRepository.findByTenantIdAndId(5L, 2401L);
    assertThat(optionalTask).isPresent();
    assertThat(optionalTask.get().getName()).isNotEqualTo("updated name");
    assertThat(optionalTask.get().isDeleted()).isFalse();
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record-to-delete.sql")
  public void givenTaskUpdatedEvent_withAlreadyDeletedTask_shouldNotUpdateIt() {
    //given
    long tenantId = 5L;
    long userId = 200L;
    TaskEvent event = new TaskEvent();
    EventEntity eventEntity = new EventEntity();
    eventEntity.setId(2403L);
    eventEntity.setTenantId(tenantId);
    eventEntity.setName("updated name");
    eventEntity.setDueDate(Date.from(Instant.now()));
    eventEntity.setAssignedTo(new IdName(userId, "AssignedTo"));
    eventEntity.setOwner(new IdName(userId, "Owner"));
    eventEntity.setCreatedBy(new IdName(userId, "CreatedBy User"));
    eventEntity.setUpdatedBy(new IdName(userId, "UpdatedBy User"));
    eventEntity.setCreatedAt(Date.from(Instant.now()));
    eventEntity.setUpdatedAt(Date.from(Instant.now()));
    event.setEntity(eventEntity);
    //when
    taskFacade.update(event, new HashMap<>(), false);
    //then
    Optional<Task> optionalTask = taskRepository.findByTenantIdAndId(5L, 2403L);
    assertThat(optionalTask).isPresent();
    assertThat(optionalTask.get().getName()).isEqualTo("Task3");
    assertThat(optionalTask.get().isDeleted()).isTrue();
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record-to-delete.sql")
  public void givenTaskToDelete_shouldDelete() {
    //when
    taskFacade.delete(new TaskDeletedEvent(2401, 5));
    //then
    assertThat(taskRepository.findByTenantIdAndId(5L, 2401L)).isNotPresent();
  }

  private void  verifyTaskUpsert() throws IOException, JSONException {
    //given
    String taskEventStr = getResourceAsString("classpath:contract/mq/create-or-update-task-event.json");
    HashMap<String, Object> payload = objectMapper.readValue(taskEventStr, new TypeReference<>() {
      @Override
      public Type getType() {
        return super.getType();
      }
    });
    var taskEvent = objectMapper.readValue(taskEventStr, TaskEvent.class);
    var entity = taskEvent.getEntity();
    Field fieldOne = new Field(3L, 6L, entity.getTenantId(), "myPicklistOne", FieldType.PICK_LIST, EntityType.TASK, true, false, null);
    Field fieldTwo = new Field(4L, 7L, entity.getTenantId(), "myPicklistTwo", FieldType.PICK_LIST, EntityType.TASK, true, false, null);
    Field fieldThree = new Field(3L, 6L, entity.getTenantId(), "myTextOne", FieldType.TEXT_FIELD, EntityType.TASK, true, false, null);
    Field fieldFour = new Field(4L, 7L, entity.getTenantId(), "myTextTwo", FieldType.TEXT_FIELD, EntityType.TASK, true, false, null);
    Field fieldFive = new Field(3L, 6L, entity.getTenantId(), "myNumberOne", FieldType.NUMBER, EntityType.TASK, true, false, null);
    Field fieldSix = new Field(4L, 7L, entity.getTenantId(), "myNumberTwo", FieldType.NUMBER, EntityType.TASK, true, false, null);
    Field fieldSeven = new Field(3L, 6L, entity.getTenantId(), "myDatePickerOne", FieldType.DATE_PICKER, EntityType.TASK, true, false, null);
    Field fieldEight = new Field(4L, 7L, entity.getTenantId(), "myDatePickerTwo", FieldType.DATE_PICKER, EntityType.TASK, true, false, null);
    Field fieldNine = new Field(3L, 6L, entity.getTenantId(), "myDatetimePickerOne", FieldType.DATETIME_PICKER, EntityType.TASK, true, false,
        null);
    Field fieldTen = new Field(4L, 7L, entity.getTenantId(), "myDatetimePickerTwo", FieldType.DATETIME_PICKER, EntityType.TASK, true, false, null);
    Field fieldEleven = new Field(3L, 6L, entity.getTenantId(), "myCheckboxOne", FieldType.CHECKBOX, EntityType.TASK, true, false,
        null);
    Field fieldTwelve = new Field(4L, 7L, entity.getTenantId(), "myCheckboxTwo", FieldType.CHECKBOX, EntityType.TASK, true, false, null);
    given(fieldFacade.getFieldsByTenantIdAndEntityType(entity.getTenantId(), EntityType.TASK))
        .willReturn(List.of(fieldOne, fieldTwo, fieldThree, fieldFour, fieldFive, fieldSix, fieldSeven, fieldEight, fieldNine, fieldTen, fieldEleven,
            fieldTwelve));
    given(fieldFacade.getFieldsByTenantIdAndEntityType(entity.getTenantId(), EntityType.TASK))
        .willReturn(List.of(fieldOne, fieldTwo, fieldThree, fieldFour, fieldFive, fieldSix, fieldSeven, fieldEight, fieldNine, fieldTen, fieldEleven,
            fieldTwelve));
    given(picklistValueFacade.getPicklistValueByFieldIdAndPicklistValueId(3L, 1L)).willReturn(
        Optional.of(new PicklistValue(3L, 1L, "valueOne", fieldOne)));
    given(picklistValueFacade.getPicklistValueByFieldIdAndPicklistValueId(4L, 2L)).willReturn(
        Optional.of(new PicklistValue(4L, 3L, "valueTwo", fieldTwo)));

    //when
    taskEvent.getEntity().setPriority(null);
    taskFacade.update(taskEvent, payload, false);

    //then
    var task = taskRepository.findById(2400L).get();
    assertThat(task.getName()).isEqualTo("Prepare Jarvis");
    assertThat(task.getTaskType().getName()).isEqualTo("Reminder");
    assertThat(task.getTaskType().getId()).isEqualTo(1700);
    assertThat(task.getTaskStatus().getName()).isEqualTo("Scheduled");
    assertThat(task.getTaskStatus().getId()).isEqualTo(1800);
    assertThat(task.getCompletedAt()).isEqualTo("2020-06-29T18:30:00.000+0000");
    assertThat(task.getOriginalDueDate()).isEqualTo("2020-06-22T18:30:00.000+0000");
    assertThat(task.getTaskPriority()).isNull();
    assertThat(task.getRelatedTo())
        .isNotEmpty()
        .extracting(TaskRelatedTo::getEntityName)
        .containsExactlyInAnyOrder("test lead", "test contact");
    var actualPayload = objectMapper.writeValueAsString(task.getEventPayload());
    JSONAssert.assertEquals(actualPayload, taskEventStr, JSONCompareMode.LENIENT);
    assertThat(task.getTaskPicklistValues())
        .isNotEmpty()
        .extracting(TaskCustomPicklistValue::getDisplayName)
        .containsExactlyInAnyOrder("valueOne", "valueTwo");
    assertThat(task.getTaskCustomTextValues())
        .isNotEmpty()
        .extracting(TaskCustomTextValue::getValue)
        .containsExactlyInAnyOrder("TextValueOne", "TextValueTwo");
    assertThat(task.getTaskCustomNumberValues())
        .isNotEmpty()
        .extracting(TaskCustomNumberValue::getValue)
        .containsExactlyInAnyOrder(20.0, 25.0);
    assertThat(task.getTaskCustomDatePickerValues())
        .anyMatch(taskCustomDatePickerValue -> taskCustomDatePickerValue.getValue().equals(Date.from(Instant.parse("2019-10-01T05:43:45.717Z"))))
        .anyMatch(taskCustomDatePickerValue -> taskCustomDatePickerValue.getValue().equals(Date.from(Instant.parse("2019-10-02T05:43:45.717Z"))));
    assertThat(task.getTaskCustomDatetimePickerValues())
        .anyMatch(
            taskCustomDatetimePickerValue -> taskCustomDatetimePickerValue.getValue().equals(Date.from(Instant.parse("2019-10-01T05:43:45.717Z"))))
        .anyMatch(
            taskCustomDatetimePickerValue -> taskCustomDatetimePickerValue.getValue().equals(Date.from(Instant.parse("2019-10-02T05:43:45.717Z"))));
    assertThat(task.getTaskCustomCheckboxValues())
        .allMatch(TaskCustomCheckboxValue::getValue);
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record-to-delete.sql")
  public void givenPicklistValueId_withDisplayName_shouldUpdateDisplayName() {
    //given
    PicklistValueDetail picklistValueDetail = new PicklistValueDetail(202L, "updateValue");
    //when
    taskFacade.updateDisplayNameByPicklistValueId(picklistValueDetail);
    //then
    Assertions.assertThat(taskCustomPicklistValueRepository.findAll())
        .filteredOn(taskCustomPicklistValue -> taskCustomPicklistValue.getPicklistValueId() == 202L)
        .allMatch(taskCustomPicklistValue -> taskCustomPicklistValue.getDisplayName()
            .equals("updateValue"));
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenTaskUpdateEvent_withOldUpdatedAt_shouldNotUpdateIt() throws ParseException {
    // given
    DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
    String oldDate = "2001-07-04T12:08:56.235-0700";
    TaskEvent event = new TaskEvent();
    EventEntity eventEntity = new EventEntity();
    eventEntity.setId(2400L);
    eventEntity.setName("updated name");
    eventEntity.setUpdatedAt(dateFormat.parse(oldDate));
    event.setEntity(eventEntity);

    // when
    taskFacade.update(event, new HashMap<>(), false);

    // then
    Optional<Task> optionalTask = taskRepository.findByTenantIdAndId(5L, 2400L);
    assertThat(optionalTask.get().getName()).isNotEqualTo("updated name");
  }

  @Test
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenOwnerId_toUpdateTaskRelatedToByEntityIdAndEntity_shouldUpdateIt() throws ParseException {
    // given
    // when
    taskFacade.updateTaskRelatedToOwnerIdByEntityIdAndEntity(11L, 202L, 1L, "LEAD");

    // then
    Task task = taskRepository.findOne((root, criteriaQuery, criteriaBuilder) -> {
      root.fetch("relatedTo");
      return criteriaBuilder.equal(root.get("id"),2400L);
    }).get();
    assertThat(task.getRelatedTo())
        .isNotEmpty()
        .filteredOn(relatedTo -> relatedTo.getOwnerId().equals(202L) && relatedTo.getEntityType().equals("LEAD")
            && relatedTo.getEntityId() == 1)
        .hasSize(1);
  }

  @Test
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenName_toUpdateTaskRelatedToNameByEntityIdAndEntity_shouldUpdateIt() {
    // give
    // when
    taskFacade.updateTaskRelatedToNameByEntityIdAndEntity(11L, 1L, "LEAD", "Walter White");

    // then
    Task task = taskRepository
        .findOne((root, criteriaQuery, criteriaBuilder) -> {
          root.fetch("relatedTo");
          return criteriaBuilder.equal(root.get("id"),2400L);
        }).get();
    assertThat(task.getRelatedTo())
        .isNotEmpty()
        .filteredOn(relatedTo -> relatedTo.getEntityName().equals("Walter White") && relatedTo.getEntityType().equals("LEAD")
            && relatedTo.getEntityId() == 1)
        .hasSize(1);
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = resourceLoader.getResource(resourcePath);
    return FileUtils.readFileToString(resource.getFile(), StandardCharsets.UTF_8);
  }
}