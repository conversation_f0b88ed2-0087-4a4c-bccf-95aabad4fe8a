package com.sling.sales.report.core.domain.entity;


import static java.util.Arrays.asList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import com.sling.sales.report.core.domain.contact.ContactCompany;
import com.sling.sales.report.core.domain.deal.DealProduct;
import com.sling.sales.report.mq.event.CompanyNameUpdatedEvent;
import com.sling.sales.report.mq.event.ContactNameUpdatedEvent;
import com.sling.sales.report.mq.event.DealNameUpdatedEvent;
import com.sling.sales.report.mq.event.PipelineUpdatedEvent;
import com.sling.sales.report.mq.event.PipelineUpdatedEvent.Stage;
import com.sling.sales.report.mq.event.PipelineUpdatedEvent.StageTypes;
import com.sling.sales.report.mq.event.ProductNameUpdatedEvent;
import com.sling.sales.report.mq.event.UserNameUpdatedEvent;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.UserRepository;
import com.sling.sales.report.setup.TestDatabaseInitializer;
import java.util.List;
import java.util.Optional;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ExtendWith(SpringExtension.class)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestDatabaseInitializer.class})
class EntityDimensionNameChangeFacadeTest {

  @Autowired
  private UserRepository userRepository;
  @Autowired
  private ProductRepository productRepository;
  @Autowired
  private DealProductRepository dealProductRepository;

  @Autowired
  private PipelineRepository pipelineRepository;
  @Autowired
  private PipelineStageRepository pipelineStageRepository;
  @Autowired
  private DealCompanyRepository dealCompanyRepository;
  @Autowired
  private DealContactRepository dealContactRepository;
  @Autowired
  private ContactCompanyRepository contactCompanyRepository;
  @Autowired
  private ContactDealRepository contactDealRepository;

  @Test
  @Transactional
  @Sql("/test-scripts/insert_users.sql")
  public void givenUserNameUpdatedEventForAnExistingUser_shouldUpdateUserName() {
    var unit = new EntityDimensionNameChangeFacade(userRepository, productRepository, pipelineRepository, pipelineStageRepository,
        dealCompanyRepository, null, dealProductRepository, null, null);
    var event = new UserNameUpdatedEvent(
        888,
        915,
        "Tom",
        "Hanks"
    );

    unit.updateUserName(event);

    User user = userRepository.findByIdAndTenantId(915, 888).get();
    assertThat(user.getName()).isEqualTo("Tom Hanks");
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert_users.sql")
  public void givenUserNameUpdatedEventForAnNonExistingUser_shouldNotUpdateUserName() {
    UserRepository mockRepository = mock(UserRepository.class);
    EntityDimensionNameChangeFacade newUnit = new EntityDimensionNameChangeFacade(mockRepository, productRepository, pipelineRepository,
        pipelineStageRepository, dealCompanyRepository, null, dealProductRepository, null, null);
    var event = new UserNameUpdatedEvent(
        888,
        986,
        "Tom",
        "Hanks"
    );

    newUnit.updateUserName(event);

    verify(mockRepository, never()).saveAndFlush(any(User.class));
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert_product.sql")
  public void givenProductNameUpdatedEventForAnExistingProduct_shouldUpdate() {
    var unit = new EntityDimensionNameChangeFacade(userRepository, productRepository, pipelineRepository, pipelineStageRepository,
        dealCompanyRepository, null, dealProductRepository, null, null);
    int productId = 912;
    int tenantId = 888;
    var event = new ProductNameUpdatedEvent(
        tenantId,
        productId,
        "New Product"
    );

    unit.updateProductName(event);

    Product product = productRepository.findByIdAndTenantId(productId, tenantId).get();
    assertThat(product.getName()).isEqualTo("New Product");

    DealProduct dealProduct = dealProductRepository.findByProductIdAndTenantId(productId, tenantId).get();
    assertThat(dealProduct.getProductName()).isEqualTo("New Product");

  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert_product.sql")
  public void givenProductNameUpdatedEventForAnNonExistingProduct_shouldNotUpdate() {
    ProductRepository mockProductRepo = mock(ProductRepository.class);
    DealProductRepository mockDealProductRepo = mock(DealProductRepository.class);
    var unit = new EntityDimensionNameChangeFacade(userRepository, mockProductRepo, pipelineRepository, pipelineStageRepository,
        dealCompanyRepository,
        null, mockDealProductRepo, null, null);
    var event = new ProductNameUpdatedEvent(
        888,
        986,
        "New Product"
    );

    unit.updateProductName(event);

    verify(mockProductRepo, never()).saveAndFlush(any(Product.class));
    verify(mockDealProductRepo, never()).saveAndFlush(any(DealProduct.class));
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert_pipeline.sql")
  public void givenPipelineUpdatedEventForAnExistingPipeline_shouldUpdate() {
    var unit = new EntityDimensionNameChangeFacade(userRepository, productRepository, pipelineRepository, pipelineStageRepository,
        dealCompanyRepository, null, dealProductRepository, null, null);
    var event =
        new PipelineUpdatedEvent(
            "DEAL",
            888L,
            913,
            "Stark Expo Pipeline",
            null,
            new String[]{"lost reason"},
            new String[]{"unqualified reason"});

    unit.updatePipeline(event);

    Pipeline pipeline = pipelineRepository.findByIdAndTenantId(913, 888).get();
    assertThat(pipeline.getName()).isEqualTo("Stark Expo Pipeline");
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert_pipeline.sql")
  public void givenPipelineUpdatedEventForAnNonExistingPipeline_shouldNotUpdate() {
    PipelineRepository mockRepo = mock(PipelineRepository.class);
    var unit = new EntityDimensionNameChangeFacade(userRepository, productRepository, mockRepo, pipelineStageRepository, dealCompanyRepository,
        null, dealProductRepository, null, null);
    var event =
        new PipelineUpdatedEvent(
            "DEAL",
            888L,
            986,
            "Stark Expo Pipeline",
            null,
            new String[]{"lost reason"},
            new String[]{"unqualified reason"});

    unit.updatePipeline(event);

    verify(mockRepo, never()).saveAndFlush(any(Pipeline.class));
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert_pipeline.sql")
  public void givenPipelineUpdatedEventForAnExistingPipeline_shouldUpdatePipelineStage() {
    var unit = new EntityDimensionNameChangeFacade(userRepository, productRepository, pipelineRepository, pipelineStageRepository,
        dealCompanyRepository, null, dealProductRepository, null, null);
    List<Stage> stages = asList(
        new Stage(914L, "Intro", 1, "Introduction", StageTypes.OPEN),
        new Stage(921L, "End", 2, "End", StageTypes.CLOSED)
    );
    var event =
        new PipelineUpdatedEvent(
            "DEAL",
            888L,
            913L,
            "Stark Expo Pipeline",
            stages,
            new String[] {"lost reason"},
            new String[] {"unqualified reason"});

    unit.updatePipeline(event);

    PipelineStage pipelineStage1 = pipelineStageRepository.findByIdAndTenantId(914, 888).get();
    assertThat(pipelineStage1.getName()).isEqualTo("Intro");
    assertThat(pipelineStage1.getPipeline().getName()).isEqualTo("Stark Expo Pipeline");
    Optional<PipelineStage> pipelineStage2 = pipelineStageRepository.findByIdAndTenantId(921, 888);
    assertThat(pipelineStage2.isEmpty()).isTrue();
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert_company.sql")
  public void givenCompanyNameUpdatedEventForAnExistingCompany_shouldUpdate() {
    var unit = new EntityDimensionNameChangeFacade(userRepository, productRepository, pipelineRepository, pipelineStageRepository,
        dealCompanyRepository, null, dealProductRepository, null, null);
    var event = new CompanyNameUpdatedEvent(
        333L,
        123L,
        "New Company"
    );

    unit.updateAssociatedCompanyName(event);

    DealCompany company = dealCompanyRepository.findByIdAndTenantId(123, 333).get();
    assertThat(company.getName()).isEqualTo("New Company");
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert_company.sql")
  public void givenCompanyNameUpdatedEventForAnNonExistingCompany_shouldNotUpdate() {
    DealCompanyRepository mockRepository = mock(DealCompanyRepository.class);
    EntityDimensionNameChangeFacade newUnit = new EntityDimensionNameChangeFacade(userRepository, productRepository, pipelineRepository,
        pipelineStageRepository, mockRepository, null, dealProductRepository, null, null);
    var event = new CompanyNameUpdatedEvent(
        334L,
        123L,
        "New Company"
    );

    newUnit.updateAssociatedCompanyName(event);

    verify(mockRepository, never()).saveAndFlush(any(DealCompany.class));
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-deals-to-update.sql")
  public void givenContactNameUpdatedEvent_shouldUpdateDealContactName() {
    //given
    EntityDimensionNameChangeFacade newUnit = new EntityDimensionNameChangeFacade(userRepository, productRepository, pipelineRepository,
        pipelineStageRepository, dealCompanyRepository, dealContactRepository, dealProductRepository, null, null);
    ContactNameUpdatedEvent contactNameUpdatedEvent = new ContactNameUpdatedEvent(103L, "firstName", "lastName", 11L);
    //when
    newUnit.updateDealContactName(contactNameUpdatedEvent);
    //then
    Assertions.assertThat(dealContactRepository.findAll())
        .anyMatch(contact -> contact.getContactId() == 103L && contact.getName().equals("firstName lastName"));
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-deals-to-update.sql")
  public void givenContactNameUpdatedEvent_withoutFirstName_shouldUpdateDealContactName() {
    //given
    EntityDimensionNameChangeFacade newUnit = new EntityDimensionNameChangeFacade(userRepository, productRepository, pipelineRepository,
        pipelineStageRepository, dealCompanyRepository, dealContactRepository, dealProductRepository, null, null);
    ContactNameUpdatedEvent contactNameUpdatedEvent = new ContactNameUpdatedEvent(103L, null, "lastName", 11L);
    //when
    newUnit.updateDealContactName(contactNameUpdatedEvent);
    //then
    Assertions.assertThat(dealContactRepository.findAll())
        .anyMatch(contact -> contact.getContactId() == 103L && contact.getName().equals("lastName"));
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-deals-to-update.sql")
  public void givenContactNameUpdatedEvent_withoutFirstNameAndLastName_shouldUpdateDealContactName() {
    //given
    EntityDimensionNameChangeFacade newUnit = new EntityDimensionNameChangeFacade(userRepository, productRepository, pipelineRepository,
        pipelineStageRepository, dealCompanyRepository, dealContactRepository, dealProductRepository, null, null);
    ContactNameUpdatedEvent contactNameUpdatedEvent = new ContactNameUpdatedEvent(103L, null, null, 11L);
    //when
    newUnit.updateDealContactName(contactNameUpdatedEvent);
    //then
    Assertions.assertThat(dealContactRepository.findAll())
        .anyMatch(contact -> contact.getContactId() == 103L && contact.getName().isEmpty());
  }

  @Test
  @Sql("/test-scripts/insert-contact-company.sql")
  public void givenCompanyNameUpdatedEvent_shouldUpdateContactCompanyName() {
    //given
    EntityDimensionNameChangeFacade newUnit = new EntityDimensionNameChangeFacade(userRepository, productRepository, pipelineRepository,
        pipelineStageRepository, dealCompanyRepository, dealContactRepository, dealProductRepository, contactCompanyRepository, null);
    CompanyNameUpdatedEvent companyNameUpdatedEvent = new CompanyNameUpdatedEvent(2000L, 1111L, "updated name");
    //when
    newUnit.updateContactCompanyName(companyNameUpdatedEvent);
    //then
    Assertions.assertThat(contactCompanyRepository.findById(1111L))
        .isPresent()
        .get()
        .extracting(ContactCompany::getName)
        .isEqualTo("updated name");
  }

  @Test
  @Sql("/test-scripts/insert-contact-deal.sql")
  public void givenDealNameUpdatedEvent_shouldUpdateContactDealName() {
    //given
    EntityDimensionNameChangeFacade newUnit = new EntityDimensionNameChangeFacade(userRepository, productRepository, pipelineRepository,
        pipelineStageRepository, dealCompanyRepository, dealContactRepository, dealProductRepository, contactCompanyRepository,
        contactDealRepository);
    DealNameUpdatedEvent dealNameUpdatedEvent = new DealNameUpdatedEvent(1L, "updated name", 55555);
    //when
    newUnit.updateContactDealName(dealNameUpdatedEvent);
    //then
    Assertions.assertThat(contactDealRepository.findAll())
        .filteredOn(contactDeal -> contactDeal.getDealId() == 1L)
        .allMatch(contactDeal -> contactDeal.getName().equals("updated name"));
  }
}