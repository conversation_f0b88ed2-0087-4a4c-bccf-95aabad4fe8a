package com.sling.sales.report.core.domain.task;

import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Collections.singletonList;
import static java.util.Collections.singletonMap;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Filter;
import com.sling.sales.report.core.api.request.AggregationRequestV3.Metric;
import com.sling.sales.report.core.api.request.GroupByField;
import com.sling.sales.report.core.domain.aggregation.dimension.DateCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.FilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.IdDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyBooleanCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdAssociatedEntityFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyIdCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.ManyTextCustomFieldDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.MetricType;
import com.sling.sales.report.core.domain.aggregation.dimension.NumberCustomFieldFilterDimension;
import com.sling.sales.report.core.domain.aggregation.dimension.Operator;
import com.sling.sales.report.core.domain.aggregation.fact.field.AbstractFactFieldFactory;
import com.sling.sales.report.core.domain.aggregation.fact.field.FactFilter;
import com.sling.sales.report.core.domain.aggregation.fact.field.TaskCustomFieldFactory;
import com.sling.sales.report.security.domain.Action;
import com.sling.sales.report.security.domain.Permission;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.UserService;
import com.sling.sales.report.security.domain.User_;
import com.sling.sales.report.setup.TestDatabaseInitializer;
import com.sling.sales.report.stubs.UserStub;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.BDDMockito;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ExtendWith(SpringExtension.class)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestDatabaseInitializer.class})
class TaskAggregationFacadeTest {

  @Autowired
  TaskAggregationFacade taskAggregationFacade;
  @MockBean
  UserService userService;
  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private AbstractFactFieldFactory abstractFactFieldFactory;

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record.sql")
  public void aggregationOnCountMetric_shouldReturn() {
    //given
    var metrics = singletonList(new Metric(MetricType.COUNT, "id", null));
    Action action = new Action();
    action.setReadAll(true);
    BDDMockito.given(userService.getLoggedInUser())
        .willReturn(new User(201L, 5L, Set.of(new Permission(4L, "task", null, action))));
    //when
    var records = taskAggregationFacade.getAggregatedResults(emptyMap(), emptyList(), Collections.singletonList(new GroupByField("assignedTo", null,
            null, null)),
        metrics, emptyList(),
        "Asia/Calcutta", 20L);

    //then
    assertThat(records)
        .hasSize(1)
        .anyMatch(record -> record.getId().equals(201L) && record.getValues().get(0).equals(2L));
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenMultipleGroupBy_withCountMetrics_shouldReturnTaskRecords() throws Exception {
    //given
    var metrics = singletonList(new Metric(MetricType.COUNT, "id", null));
    Action action = new Action();
    action.setReadAll(true);
    BDDMockito.given(userService.getLoggedInUser())
        .willReturn(new User(201L, 5L, Set.of(new Permission(4L, "task", null, action))));
    //when
    var records = taskAggregationFacade
        .getAggregatedResults(emptyMap(), emptyList(),
            List.of(new GroupByField("type", null, null, null), new GroupByField("assignedTo", null, null, null), new GroupByField("status", null,
                null, null)), metrics,
            emptyList(),
            "Asia/Calcutta", 20L);

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contract/report/result/task-multiple-group-by-count-metrics.json"),
        new ObjectMapper().writeValueAsString(records),
        JSONCompareMode.NON_EXTENSIBLE
    );
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenGroupBy_withCountMetrics_shouldReturnTableRows() throws Exception {
    //given
    IdDimension<Task, User> owner = new IdDimension<>(
        "ownerId",
        Task_.OWNER,
        Task_.owner,
        join -> join.get(User_.id),
        join -> join.get(User_.name),
        emptyList());
    var metrics = singletonList(new Metric(MetricType.COUNT, "id", null));
    Action action = new Action();
    action.setReadAll(true);
    BDDMockito.given(userService.getLoggedInUser())
        .willReturn(new User(200L, 5L, Set.of(new Permission(4L, "task", null, action))));
    //when
    var records = taskAggregationFacade.getAggregationResult(emptyList(), List.of(owner), metrics, emptyList(), "Asia/Calcutta", 55L);
    //then
    assertThat(records)
        .isNotEmpty()
        .hasSize(2)
        .extracting(r -> r.getValues().get(0))
        .containsExactlyInAnyOrder(1L, 1L);
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenGroupByAndFilterWithInOperator_withCountMetrics_shouldReturnTableRows() throws Exception {
    //given
    IdDimension<Task, User> owner = new IdDimension<>(
        "ownerId",
        Task_.OWNER,
        Task_.owner,
        join -> join.get(User_.id),
        join -> join.get(User_.name),
        emptyList());
    Filter filter = new Filter("in", "ownerId", "ownerId", "long", List.of(200, 300), null, null, "LOOK_UP", null, null);
    FactFilter<Task> factFilter = new FactFilter<>(Operator.in, owner, filter, "Asia/Calcutta");
    var metrics = singletonList(new Metric(MetricType.COUNT, "id", null));
    Action action = new Action();
    action.setReadAll(true);
    BDDMockito.given(userService.getLoggedInUser())
        .willReturn(new User(200L, 5L, Set.of(new Permission(4L, "task", null, action))));
    //when
    var records = taskAggregationFacade.getAggregationResult(singletonList(factFilter), List.of(owner), metrics, emptyList(), "Asia/Calcutta", 55L);
    //then
    assertThat(records)
        .isNotEmpty()
        .hasSize(1)
        .anyMatch(taskRecord -> taskRecord.getValues().get(0).equals(1L));
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenGroupByAndFilterWithNotInOperator_withCountMetrics_shouldReturnTableRows() throws Exception {
    //given
    IdDimension<Task, User> owner = new IdDimension<>(
        "ownerId",
        Task_.OWNER,
        Task_.owner,
        join -> join.get(User_.id),
        join -> join.get(User_.name),
        emptyList());
    Filter filter = new Filter("not_in", "ownerId", "ownerId", "long", List.of(200, 300, 201), null, null, "LOOK_UP", null, null);
    FactFilter<Task> factFilter = new FactFilter<>(Operator.not_in, owner, filter, "Asia/Calcutta");
    var metrics = singletonList(new Metric(MetricType.COUNT, "id", null));
    Action action = new Action();
    action.setReadAll(true);
    BDDMockito.given(userService.getLoggedInUser())
        .willReturn(new User(200L, 5L, Set.of(new Permission(4L, "task", null, action))));
    //when
    var records = taskAggregationFacade.getAggregationResult(singletonList(factFilter), List.of(owner), metrics, emptyList(), "Asia/Calcutta", 55L);
    //then
    assertThat(records)
        .isEmpty();
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenUpdatedByAsGroupByDimension_withCountMetric_shouldReturnTaskRecords() {
    //given
    var metrics = singletonList(new Metric(MetricType.COUNT, "id", null));
    Action action = new Action();
    action.setReadAll(true);
    BDDMockito.given(userService.getLoggedInUser())
        .willReturn(new User(200L, 5L, Set.of(new Permission(4L, "task", null, action))));
    //when
    var records = taskAggregationFacade.getAggregatedResults(emptyMap(), emptyList(), Collections.singletonList(new GroupByField("updatedBy", null,
            null, null)),
        metrics,
        emptyList(),
        "Asia/Calcutta", 20L);

    //then
    assertThat(records)
        .isNotEmpty()
        .hasSize(2)
        .extracting(r -> r.getValues().get(0))
        .containsExactlyInAnyOrder(1L, 1L);
  }

  @Test
  @Sql("/test-scripts/tasks-with-custom-fields.sql")
  public void givenCustomPicklistFieldAsGroupByAndFilterDimension_withMetricCount_shouldReturnAggregatedRecords() {
    //given
    ManyIdCustomFieldDimension<Task, TaskCustomPicklistValue> manyIdCustomFieldDimension = new ManyIdCustomFieldDimension<>(
        "customTaskPicklist",
        Task_.TASK_PICKLIST_VALUES,
        Task_.taskPicklistValues,
        join -> join.get(TaskCustomPicklistValue_.picklistValueId),
        join -> join.get(TaskCustomPicklistValue_.displayName),
        join -> join.get(TaskCustomPicklistValue_.fieldId),
        2015L, TaskCustomPicklistValue_.DISPLAY_NAME);
    given(userService.getLoggedInUser())
        .willReturn(UserStub.aUser(1201L, 1200L, true, true, true, true, true, "task"));
    Operator operator = Operator.equal;
    Filter filter = new Filter("equal", "customTaskPicklist", "customTaskPicklist", "long", 267L, null, null, "LOOK_UP", null, null);
    var taskFilters = singletonList(
        new FactFilter<>(operator, getDimension("customTaskPicklist", operator, singletonMap("customTaskPicklist", manyIdCustomFieldDimension)),
            filter, "Asia/Kolkata"));
    var groupBy = new GroupByField("customTaskPicklist", null, null, null);
    var metric = new Metric(MetricType.COUNT, "id", null);

    //when
    var taskRecords = taskAggregationFacade
        .getAggregatedResults(singletonMap("customTaskPicklist", manyIdCustomFieldDimension), taskFilters, singletonList(groupBy),
            singletonList(metric), emptyList(), "Asia/Calcutta", 20L);

    //then
    Assertions.assertThat(taskRecords).
        isEmpty();
  }

  @Test
  @Sql("/test-scripts/tasks-with-custom-fields.sql")
  public void givenCustomPicklistFieldAsGroupByWithoutFilters_withMetricCount_shouldReturnAggregatedRecords() {
    //given
    ManyIdCustomFieldDimension<Task, TaskCustomPicklistValue> manyIdCustomFieldDimension = new ManyIdCustomFieldDimension<>(
        "customTaskPicklist",
        Task_.TASK_PICKLIST_VALUES,
        Task_.taskPicklistValues,
        join -> join.get(TaskCustomPicklistValue_.picklistValueId),
        join -> join.get(TaskCustomPicklistValue_.displayName),
        join -> join.get(TaskCustomPicklistValue_.fieldId),
        2015L, TaskCustomPicklistValue_.DISPLAY_NAME);
    given(userService.getLoggedInUser())
        .willReturn(UserStub.aUser(1201L, 1200L, true, true, true, true, true, "task"));
    var groupBy = new GroupByField("customTaskPicklist", null, null, null);
    var metric = new Metric(MetricType.COUNT, "id", null);

    //when
    var taskRecords = taskAggregationFacade
        .getAggregatedResults(singletonMap("customTaskPicklist", manyIdCustomFieldDimension), emptyList(), singletonList(groupBy),
            singletonList(metric), emptyList(), "Asia/Calcutta", 20L);

    //then
    Assertions.assertThat(taskRecords).
        filteredOn(taskRecord -> Objects.nonNull(taskRecord.getId()) && Objects.nonNull(taskRecord.getName()))
        .hasSize(3)
        .anyMatch(
            taskRecord -> taskRecord.getName().equals("valueThree") && taskRecord.getId().equals(200L) && taskRecord.getValues().get(0)
                .equals(1L))
        .anyMatch(
            taskRecord -> taskRecord.getName().equals("valueFour") && taskRecord.getId().equals(201L) && taskRecord.getValues().get(0)
                .equals(1L))
        .anyMatch(
            taskRecord -> taskRecord.getName().equals("valueFive") && taskRecord.getId().equals(202L) && taskRecord.getValues().get(0)
                .equals(1L));
  }

  @Test
  @Sql("/test-scripts/tasks-with-custom-fields.sql")
  public void givenCustomTextFieldAsGroupByAndFilterDimension_withMetricCount_shouldReturnAggregatedRecords() {
    //given
    ManyTextCustomFieldDimension<Task, TaskCustomTextValue> manyTextCustomFieldDimension = new ManyTextCustomFieldDimension<>(
        "customText",
        Task_.TASK_CUSTOM_TEXT_VALUES,
        Task_.taskCustomTextValues,
        join -> join.get(TaskCustomTextValue_.value),
        join -> join.get(TaskCustomTextValue_.fieldId),
        2016L
    );
    given(userService.getLoggedInUser())
        .willReturn(UserStub.aUser(1201L, 1200L, true, true, true, true, true, "task"));
    Operator operator = Operator.equal;
    Filter filter = new Filter("equal", "customText", "customText", "long", 267L, null, null, "LOOK_UP", null, null);
    var taskFilters = singletonList(
        new FactFilter<>(operator, getDimension("customText", operator, singletonMap("customText", manyTextCustomFieldDimension)), filter,
            "Asia/Kolkata"));
    var groupBy = new GroupByField("customText", null, null, null);
    var metric = new Metric(MetricType.COUNT, "id", null);

    //when
    var taskRecords = taskAggregationFacade
        .getAggregatedResults(singletonMap("customText", manyTextCustomFieldDimension), taskFilters, singletonList(groupBy),
            singletonList(metric), emptyList(), "Asia/Calcutta", 20L);

    //then
    Assertions.assertThat(taskRecords).
        isEmpty();
  }

  @Test
  @Sql("/test-scripts/tasks-with-custom-fields.sql")
  public void givenCustomTextFieldAsGroupByWithoutFilters_withMetricCount_shouldReturnAggregatedRecords() {
    //given
    ManyTextCustomFieldDimension<Task, TaskCustomTextValue> manyTextCustomFieldDimension = new ManyTextCustomFieldDimension<>(
        "customText",
        Task_.TASK_CUSTOM_TEXT_VALUES,
        Task_.taskCustomTextValues,
        join -> join.get(TaskCustomTextValue_.value),
        join -> join.get(TaskCustomTextValue_.fieldId),
        2016L
    );
    given(userService.getLoggedInUser())
        .willReturn(UserStub.aUser(1201L, 1200L, true, true, true, true, true, "task"));
    var groupBy = new GroupByField("customText", null, null, null);
    var metric = new Metric(MetricType.COUNT, "id", null);

    //when
    var taskRecords = taskAggregationFacade
        .getAggregatedResults(singletonMap("customText", manyTextCustomFieldDimension), emptyList(), singletonList(groupBy),
            singletonList(metric), emptyList(), "Asia/Calcutta", 20L);

    //then
    Assertions.assertThat(taskRecords).
        filteredOn(taskRecord -> Objects.nonNull(taskRecord.getName()))
        .hasSize(1)
        .anyMatch(
            taskRecord -> taskRecord.getName().equals("Test1") && Objects.isNull(taskRecord.getId()) && taskRecord.getValues().get(0)
                .equals(1L));
  }

  @Test
  @Sql("/test-scripts/tasks-with-custom-fields.sql")
  public void givenCustomNumberFieldAsFilterDimension_withMetricCount_shouldReturnAggregatedRecords() {
    //given
    NumberCustomFieldFilterDimension<Task, TaskCustomNumberValue> numberCustomFieldFilterDimension = new NumberCustomFieldFilterDimension<>(
        "customNumber",
        Task_.TASK_CUSTOM_NUMBER_VALUES,
        Task_.taskCustomNumberValues,
        join -> join.get(TaskCustomNumberValue_.value),
        join -> join.get(TaskCustomNumberValue_.fieldId), 2018L);
    given(userService.getLoggedInUser())
        .willReturn(UserStub.aUser(1201L, 1200L, true, true, true, true, true, "task"));
    Operator operator = Operator.equal;
    Filter filter = new Filter("equal", "customNumber", "customNumber", "double", 20.0d, null, null, "LOOK_UP", null, null);
    var taskFilters = singletonList(
        new FactFilter<>(operator, getDimension("customNumber", operator, singletonMap("customNumber", numberCustomFieldFilterDimension)), filter,
            "Asia/Kolkata"));
    var groupBy = new GroupByField("createdBy", null, null, null);
    var metric = new Metric(MetricType.COUNT, "id", null);

    //when
    var taskRecords = taskAggregationFacade
        .getAggregatedResults(emptyMap(), taskFilters, singletonList(groupBy),
            singletonList(metric), emptyList(), "Asia/Calcutta", 20L);

    //then
    Assertions.assertThat(taskRecords).
        hasSize(1)
        .allMatch(
            taskRecord -> taskRecord.getName().equals("Rob Stark") && taskRecord.getId().equals(1201L) && taskRecord.getValues().get(0)
                .equals(1L));
  }

  @Test
  @Sql("/test-scripts/tasks-with-custom-fields.sql")
  public void givenCustomNumberField_withMetricSumAndAverage_shouldReturnAggregatedRecords() {
    //given
    given(userService.getLoggedInUser())
        .willReturn(UserStub.aUser(1201L, 1200L, true, true, true, true, true, "task"));
    var groupBy = new GroupByField("createdBy", null, null, null);
    var dimensionDetail = TaskCustomFieldFactory.createNumberCustomFieldFilterDimension("customNumber", 2018L);
    var metrics = List
        .of(new Metric(MetricType.SUM, "customNumber", "Custom Number"), new Metric(MetricType.AVERAGE, "customNumber", "Custom Number"));

    //when
    var taskRecords = taskAggregationFacade
        .getAggregatedResults(emptyMap(), emptyList(), singletonList(groupBy),
            metrics, dimensionDetail.getMetrics(), "Asia/Calcutta", 20L);

    //then
    Assertions.assertThat(taskRecords).
        hasSize(1)
        .anyMatch(
            taskRecord -> taskRecord.getName().equals("Rob Stark") && taskRecord.getId().equals(1201L) && taskRecord.getValues().get(0)
                .equals(20.0d) && taskRecord.getValues().get(1)
                .equals(20.0d));
  }

  @Test
  @Sql("/test-scripts/tasks-with-custom-fields.sql")
  public void givenCustomDatePickerFieldAsFilterDimension_withMetricCount_shouldReturnAggregatedRecords() {
    //given
    DateCustomFieldDimension<Task, TaskCustomDatePickerValue> dateCustomFieldFilterDimension = new DateCustomFieldDimension<>(
        "customDatePicker",
        Task_.TASK_CUSTOM_DATE_PICKER_VALUES,
        Task_.taskCustomDatePickerValues,
        join -> join.get(TaskCustomDatePickerValue_.value),
        join -> join.get(TaskCustomDatePickerValue_.fieldId),
        2020L);
    given(userService.getLoggedInUser())
        .willReturn(UserStub.aUser(1201L, 1200L, true, true, true, true, true, "task"));
    Operator operator = Operator.between;
    Filter filter = new Filter("equal", "customNumber", "customNumber", "double",
        new ArrayList<>(List.of("2020-02-01T05:43:45.717Z", "2020-02-05T05:43:45.717Z")), null, null, "LOOK_UP", null, null);
    var taskFilters = singletonList(
        new FactFilter<>(operator, getDimension("customDatePicker", operator, singletonMap("customDatePicker", dateCustomFieldFilterDimension)),
            filter,
            "Asia/Kolkata"));
    var groupBy = new GroupByField("createdBy", null, null, null);
    var metric = new Metric(MetricType.COUNT, "id", null);

    //when
    var taskRecords = taskAggregationFacade
        .getAggregatedResults(emptyMap(), taskFilters, singletonList(groupBy),
            singletonList(metric), emptyList(), "Asia/Calcutta", 20L);

    //then
    Assertions.assertThat(taskRecords).
        hasSize(1)
        .allMatch(
            taskRecord -> taskRecord.getName().equals("Rob Stark") && taskRecord.getId().equals(1201L) && taskRecord.getValues().get(0)
                .equals(1L));
  }

  @Test
  @Sql("/test-scripts/tasks-with-custom-fields.sql")
  public void givenCustomDateTimePickerFieldAsFilterDimension_withMetricCount_shouldReturnAggregatedRecords() {
    //given
    DateCustomFieldDimension<Task, TaskCustomDatetimePickerValue> dateCustomFieldFilterDimension = new DateCustomFieldDimension<>(
        "customDatetimePicker",
        Task_.TASK_CUSTOM_DATETIME_PICKER_VALUES,
        Task_.taskCustomDatetimePickerValues,
        join -> join.get(TaskCustomDatetimePickerValue_.value),
        join -> join.get(TaskCustomDatetimePickerValue_.fieldId),
        2022L);
    given(userService.getLoggedInUser())
        .willReturn(UserStub.aUser(1201L, 1200L, true, true, true, true, true, "task"));
    Operator operator = Operator.between;
    Filter filter = new Filter("between", "customDatetimePicker", "customDatetimePicker", "date",
        new ArrayList<>(List.of("2020-02-01T05:43:45.717Z", "2020-02-05T05:43:45.717Z")), null, null, "DATETIME_PICKER", null, null);
    var taskFilters = singletonList(
        new FactFilter<>(operator,
            getDimension("customDatetimePicker", operator, singletonMap("customDatetimePicker", dateCustomFieldFilterDimension)),
            filter,
            "Asia/Kolkata"));
    var groupBy = new GroupByField("createdBy", null, null, null);
    var metric = new Metric(MetricType.COUNT, "id", null);

    //when
    var taskRecords = taskAggregationFacade
        .getAggregatedResults(emptyMap(), taskFilters, singletonList(groupBy),
            singletonList(metric), emptyList(), "Asia/Calcutta", 20L);

    //then
    Assertions.assertThat(taskRecords).
        hasSize(1)
        .allMatch(
            taskRecord -> taskRecord.getName().equals("Rob Stark") && taskRecord.getId().equals(1201L) && taskRecord.getValues().get(0)
                .equals(1L));
  }

  @Test
  @Sql("/test-scripts/tasks-with-custom-fields.sql")
  public void givenCustomCheckboxFieldAsGroupByDimension_withMetricCount_shouldReturnAggregatedRecords() {
    //given
    ManyBooleanCustomFieldDimension<Task, TaskCustomCheckboxValue> manyBooleanCustomFieldDimension = new ManyBooleanCustomFieldDimension<>(
        "customCheckbox",
        Task_.TASK_CUSTOM_CHECKBOX_VALUES,
        Task_.taskCustomCheckboxValues,
        join -> join.get(TaskCustomCheckboxValue_.value),
        join -> join.get(TaskCustomCheckboxValue_.fieldId),
        2024L
    );
    given(userService.getLoggedInUser())
        .willReturn(UserStub.aUser(1201L, 1200L, true, true, true, true, true, "task"));
    var groupBy = new GroupByField("customCheckbox", null, null, null);
    var metric = new Metric(MetricType.COUNT, "id", null);

    //when
    var taskRecords = taskAggregationFacade
        .getAggregatedResults(singletonMap("customCheckbox", manyBooleanCustomFieldDimension), emptyList(), singletonList(groupBy),
            singletonList(metric), emptyList(), "Asia/Calcutta", 20L);

    //then
    Assertions.assertThat(taskRecords).
        isNotEmpty()
        .anyMatch(taskRecord -> taskRecord.getName().equals("No") && taskRecord.getValues().get(0).equals(1L));
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenUserWithoutReadPermission_andTaskAssignedTo_shouldReturnRecords() throws Exception {
    //given
    IdDimension<Task, TaskStatus> taskStatus = new IdDimension<>(
        "status",
        Task_.TASK_STATUS,
        Task_.taskStatus,
        join -> join.get(TaskStatus_.id),
        join -> join.get(TaskStatus_.name),
        emptyList());
    var metrics = singletonList(new Metric(MetricType.COUNT, "id", null));
    Action action = new Action();
    action.setRead(true);
    BDDMockito.given(userService.getLoggedInUser())
        .willReturn(new User(201L, 5L, Set.of(new Permission(4L, "task", null, action))));
    //when
    var records = taskAggregationFacade.getAggregationResult(emptyList(), List.of(taskStatus), metrics, emptyList(), "Asia/Calcutta", 20L);
    //then
    assertThat(records)
        .isNotEmpty()
        .hasSize(1)
        .anyMatch(taskRecord -> taskRecord.getValues().get(0).equals(2L));
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenAssociatedLeadsAsFilterDimension_withMetricCount_shouldReturnRecords() {
    //given
    var metrics = singletonList(new Metric(MetricType.COUNT, "id", null));
    ManyIdAssociatedEntityFilterDimension<Task, TaskRelatedTo> associatedLeads = new ManyIdAssociatedEntityFilterDimension<>(
        "associatedLeads",
        Task_.RELATED_TO,
        Task_.relatedTo,
        join -> join.get(TaskRelatedTo_.entityId),
        join -> join.get(TaskRelatedTo_.entityType),
        join -> join.get(TaskRelatedTo_.entityName),
        "LEAD"
    );
    Filter filter = new Filter("equal", "associatedDeals", "associatedDeals", "long",
        1, null, null, "LOOK_UP", null, null);
    FactFilter<Task> factFilter = new FactFilter<>(Operator.equal, associatedLeads, filter, "Asia/Calcutta");
    Action action = new Action();
    action.setReadAll(true);
    BDDMockito.given(userService.getLoggedInUser())
        .willReturn(new User(200L, 5L, Set.of(new Permission(4L, "task", null, action))));
    //when
    var records = taskAggregationFacade.getAggregatedResults(emptyMap(), List.of(factFilter),
        List.of(new GroupByField("ownerId", null, null, null)), metrics, emptyList(),
        "Asia/Calcutta", 50L);
    //then
    assertThat(records)
        .isNotEmpty()
        .hasSize(2)
        .extracting(r -> r.getValues().get(0))
        .containsExactlyInAnyOrder(1L, 1L);
  }

  @Test
  @Transactional
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenAssociatedDealsAsGroupByDimension_withMetricCount_shouldReturnRecords() {
    //given
    var metrics = singletonList(new Metric(MetricType.COUNT, "id", null));
    Action action = new Action();
    action.setReadAll(true);
    BDDMockito.given(userService.getLoggedInUser())
        .willReturn(new User(200L, 5L, Set.of(new Permission(4L, "task", null, action))));
    //when
    var records = taskAggregationFacade.getAggregatedResults(emptyMap(), emptyList(),
        List.of(new GroupByField("associatedLeads", null, null, null)), metrics, emptyList(),
        "Asia/Calcutta", 50L);
    //then
    assertThat(records)
        .isNotEmpty()
        .hasSize(1)
        .extracting(r -> r.getValues().get(0))
        .containsExactlyInAnyOrder(2L);
  }

  private FilterDimension<Task> getDimension(String dimensionName, Operator operator, Map<String, FilterDimension<Task>> filterDimensions) {
    return abstractFactFieldFactory.getFieldFactory(Task.class).getFilterField(dimensionName, operator, filterDimensions);
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = resourceLoader.getResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }
}