package com.sling.sales.report.mq;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.domain.call.CallFacade;
import com.sling.sales.report.core.domain.contact.Contact;
import com.sling.sales.report.core.domain.contact.ContactFacade;
import com.sling.sales.report.core.domain.meeting.MeetingInviteeFacade;
import com.sling.sales.report.core.domain.meeting.MeetingRelatedToFacade;
import com.sling.sales.report.core.domain.meeting.OrganizerFacade;
import com.sling.sales.report.core.domain.service.PicklistService;
import com.sling.sales.report.core.domain.service.SearchService;
import com.sling.sales.report.core.domain.task.TaskFacade;
import com.sling.sales.report.mq.ContactDomainEventListenerTest.TestMqSetup;
import com.sling.sales.report.mq.event.ContactDeletedEvent;
import com.sling.sales.report.mq.event.ContactEvent;
import com.sling.sales.report.mq.event.ContactOwnerUpdateEvent;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.UserFacade;
import com.sling.sales.report.setup.TestDatabaseInitializer;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.RabbitMQContainer;
import reactor.core.publisher.Mono;

@SpringBootTest
@ExtendWith(SpringExtension.class)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
@Sql("/test-scripts/cleanUp-tables.sql")
class ContactDomainEventListenerTest {

  private static final String SALES_EXCHANGE = "ex.sales";

  private SimpleMessageListenerContainer container;
  private MockMqListener mockMqListener = new MockMqListener();

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  private ContactFacade contactFacade;
  @MockBean
  private UserFacade userFacade;
  @MockBean
  private SearchService searchService;
  @MockBean
  private PicklistService picklistService;
  @MockBean
  private CallFacade callFacade;
  @MockBean
  private OrganizerFacade organizerFacade;
  @MockBean
  private MeetingRelatedToFacade meetingRelatedToFacade;
  @MockBean
  private MeetingInviteeFacade meetingInviteeFacade;
  @MockBean
  private TaskFacade taskFacade;
  @BeforeAll
  public static void initialise() {
    rabbitMQContainer.start();
  }

  @AfterAll
  public static void tearDown() {
    rabbitMQContainer.stop();
  }

  @Test
  @Sql(value = {"/test-scripts/cleanUp-tables.sql", "/test-scripts/insert_users.sql", "/test-scripts/insert-fields.sql"})
  public void givenContactCreatedEvent_shouldCaptureAndCreateContact() throws InterruptedException, IOException, JSONException {
    // given
    String contactEventAsString = getResourceAsString("classpath:contract/mq/contact/contact-created-event.json");
    ContactEvent contactEvent = objectMapper.readValue(contactEventAsString, ContactEvent.class);
    Map<Long, String> dealsSummary = Map.of(1L, "one", 2L, "two", 3L, "three");
    given(searchService.getLookUpForUsers(any(), any())).willReturn(Mono.just(Map.of(915L, "Tony Stark")));
    given(userFacade.getExistingOrCreateNewUser(any())).willReturn(new User(915L, 888L, "Tony Stark"));
    given(searchService.getLookUpForDeals(any(), any()))
        .willReturn(Mono.just(dealsSummary));

    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactCreatedEventName(), contactEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    Contact persistedContact = contactFacade.getByTenantIdAndContactId(888L, 1L).get();
    String persistedContactAsString = objectMapper.writeValueAsString(persistedContact);
    String expectedContactAsString = getResourceAsString("classpath:contract/mq/contact/expected-contact-created.json");
    JSONAssert.assertEquals(expectedContactAsString, persistedContactAsString,
        new CustomComparator(
            JSONCompareMode.NON_EXTENSIBLE,
            new Customization("contactCustomDatetimePickerValues[*].id", (o1, o2) -> true),
            new Customization("contactCustomDatePickerValues[*].id", (o1, o2) -> true),
            new Customization("contactCustomNumberValues[*].id", (o1, o2) -> true),
            new Customization("contactCustomTextValues[*].id", (o1, o2) -> true),
            new Customization("contactCustomCheckboxValues[*].id", (o1, o2) -> true),
            new Customization("contactPicklistValues[*].id", (o1, o2) -> true),
            new Customization("contactCustomMultiPicklistValues[*].id", (o1, o2) -> true),
            new Customization("associatedDeals[*].id", (o1, o2) -> true),
            new Customization("contactEmails[*].id", (o1, o2) -> true),
            new Customization("contactUtm.id", (o1, o2) -> true),
            new Customization("version", (o1, o2) -> true)
        )
    );
  }

  @Test
  @Sql("/test-scripts/insert-contact-records.sql")
  public void givenContactUpdatedEvent_shouldCaptureAndUpdateContact() throws InterruptedException, IOException, JSONException {
    // given

    String contactEventAsString = getResourceAsString("classpath:contract/mq/contact/contact-updated-event.json");
    ContactEvent contactEvent = objectMapper.readValue(contactEventAsString, ContactEvent.class);
    Map<Long, String> dealsSummary = Map.of(1L, "one", 2L, "two", 3L, "three");
    given(searchService.getLookUpForUsers(any(), any())).willReturn(Mono.just(Map.of(11112L, "Monty Stark")));
    given(userFacade.getExistingOrCreateNewUser(argThat(user -> user.getId().equals(11112L) && user.getTenantId() == 1111L)))
        .willReturn(new User(11112L, 1111L, "Monty Stark"));
    given(searchService.getLookUpForDeals(any(), any()))
        .willReturn(Mono.just(dealsSummary));

    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactUpdatedEventName(), contactEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    Contact updatedContact = contactFacade.getByTenantIdAndContactId(1111L, 9000L).get();
    String updatedContactAsString = objectMapper.writeValueAsString(updatedContact);
    String expectedContactAsString = getResourceAsString("classpath:contract/mq/contact/expected-contact-updated.json");
    JSONAssert.assertEquals(expectedContactAsString, updatedContactAsString,
        new CustomComparator(
            JSONCompareMode.NON_EXTENSIBLE,
            new Customization("contactCustomDatetimePickerValues[*].id", (o1, o2) -> true),
            new Customization("contactCustomDatePickerValues[*].id", (o1, o2) -> true),
            new Customization("contactCustomNumberValues[*].id", (o1, o2) -> true),
            new Customization("contactCustomTextValues[*].id", (o1, o2) -> true),
            new Customization("contactCustomCheckboxValues[*].id", (o1, o2) -> true),
            new Customization("contactPicklistValues[*].id", (o1, o2) -> true),
            new Customization("contactCustomMultiPicklistValues[*].id", (o1, o2) -> true),
            new Customization("associatedDeals[*].id", (o1, o2) -> true),
            new Customization("contactEmails[*].id", (o1, o2) -> true),
            new Customization("contactCampaignActivities[*].id", (o1, o2) -> true),
            new Customization("contactUtm.id", (o1, o2) -> true),
            new Customization("version", (o1, o2) -> true)

        )
    );
  }

  @Test
  @Sql("/test-scripts/insert-contact-records.sql")
  public void givenContactDeletedEvent_shouldCaptureAndUpdateContact() throws InterruptedException, IOException, JSONException {
    // given

    String contactEventAsString = getResourceAsString("classpath:contract/mq/contact/contact-deleted-event.json");
    ContactDeletedEvent contactDeletedEvent = objectMapper.readValue(contactEventAsString, ContactDeletedEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactDeletedEvent.getContactDeletedEventName(), contactDeletedEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    Optional<Contact> contact = contactFacade.getByTenantIdAndContactId(1111L, 9000L);
    Assertions.assertThat(contact).isPresent();
    Assertions.assertThat(contact.get().isDeleted()).isTrue();
  }

  @Test
  public void givenContactOwnerUpdateEvent_shouldUpdateOwnerInAssociatedEntities() throws InterruptedException {
    //given
    ContactOwnerUpdateEvent contactOwnerUpdateEvent = new ContactOwnerUpdateEvent(1L, 100L, 99L , 101L);
    //when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactOwnerUpdateEvent.getEventName(), contactOwnerUpdateEvent);
    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    verify(organizerFacade, times(1)).updateOrganizerOwnerByEntityIdAndEntity(100L, 1L,"contact");
    verify(meetingRelatedToFacade, times(1)).updateMeetingRelatedToOwnerByEntityIdAndEntity(100L, 1L,"contact");
    verify(meetingInviteeFacade, times(1)).updateMeetingInviteeOwnerByEntityIdAndEntity(100L, 1L,"contact");
    verify(callFacade, times(1)).updateCallAssociatedToOwnersByEntityIdAndEntity(100L, 1L,"contact");
    verify(callFacade, times(1)).updateCallRelatedToOwnersByEntityIdAndEntity(100L, 1L,"contact");
    verify(taskFacade, times(1)).updateTaskRelatedToOwnerIdByEntityIdAndEntity(101L, 100L, 1L,"CONTACT");
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
  }

  @TestConfiguration
  public static class TestMqSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.rabbitmq.virtual-host=" + "/");
    }
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = resourceLoader.getResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

}