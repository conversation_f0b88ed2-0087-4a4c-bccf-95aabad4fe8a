package com.sling.sales.report.mq;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.domain.call.Call;
import com.sling.sales.report.core.domain.call.CallFacade;
import com.sling.sales.report.core.domain.call.CallRepository;
import com.sling.sales.report.core.domain.company.Company;
import com.sling.sales.report.core.domain.company.CompanyFacade;
import com.sling.sales.report.core.domain.task.Task;
import com.sling.sales.report.core.domain.task.TaskFacade;
import com.sling.sales.report.core.domain.task.TaskRepository;
import com.sling.sales.report.mq.CompanyDomainEventListenerTest.TestMqSetup;
import com.sling.sales.report.mq.event.CompanyDeletedEvent;
import com.sling.sales.report.mq.event.CompanyEvent;
import com.sling.sales.report.mq.event.CompanyOwnerUpdateEventV2;
import com.sling.sales.report.setup.TestDatabaseInitializer;
import java.io.IOException;
import java.util.HashMap;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.json.JSONException;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.RabbitMQContainer;

@SpringBootTest
@ExtendWith(SpringExtension.class)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
@Sql("/test-scripts/cleanUp-tables.sql")
class CompanyDomainEventListenerTest {

  private static final String COMPANY_EXCHANGE = "ex.company";

  private SimpleMessageListenerContainer container;
  private MockMqListener mockMqListener = new MockMqListener();

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private CompanyFacade companyFacade;
  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  private CallRepository callRepository;
  @Autowired
  private TaskRepository taskRepository;

  @BeforeAll
  public static void initialise() {
    rabbitMQContainer.start();
  }

  @AfterAll
  public static void tearDown() {
    rabbitMQContainer.stop();
  }

  @Test
  @Sql(value = {"/test-scripts/insert_users.sql", "/test-scripts/insert-fields.sql"})
  public void givenCompanyCreatedEvent_shouldCaptureAndCreateCompany() throws InterruptedException, IOException, JSONException {
    // given

    String companyEventAsString = getResourceAsString("classpath:contract/mq/company-created-event.json");
    HashMap companyEvent = objectMapper.readValue(companyEventAsString, HashMap.class);

    // when
    rabbitTemplate.convertAndSend(COMPANY_EXCHANGE, CompanyEvent.getCompanyCreatedEventName(), companyEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    Company persistedCompany = companyFacade.getByTenantIdAndCompanyId(888L, 1L).get();
    String persistedCompanyAsString = objectMapper.writeValueAsString(persistedCompany);
    String expectedCompanyAsString = getResourceAsString("classpath:contract/mq/expected-company-created.json");
    JSONAssert.assertEquals(expectedCompanyAsString, persistedCompanyAsString,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("id", (o1, o2) -> true),
            new Customization("companyCustomDatetimePickerValues[*].id", (o1, o2) -> true),
            new Customization("companyCustomDatePickerValues[*].id", (o1, o2) -> true),
            new Customization("companyCustomNumberValues[*].id", (o1, o2) -> true),
            new Customization("companyCustomTextValues[*].id", (o1, o2) -> true),
            new Customization("companyCustomCheckboxValues[*].id", (o1, o2) -> true),
            new Customization("companyPicklistValues[*].id", (o1, o2) -> true),
            new Customization("companyPhoneNumbers[*]", (o1, o2) -> true),
            new Customization("companyCustomMultiPicklistValues[*]", (o1, o2) -> true)
        )
    );
  }

  @Test
  @Sql("/test-scripts/insert-company-records.sql")
  public void givenCompanyUpdatedEvent_shouldCaptureAndUpdateCompany() throws InterruptedException, IOException, JSONException {
    // given

    String companyEventAsString = getResourceAsString("classpath:contract/mq/company-updated-event.json");
    HashMap companyEvent = objectMapper.readValue(companyEventAsString, HashMap.class);

    // when
    rabbitTemplate.convertAndSend(COMPANY_EXCHANGE, CompanyEvent.getCompanyUpdatedEventName(), companyEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    Company updatedCompany = companyFacade.getByTenantIdAndCompanyId(1234L, 9000L).get();
    String updatedCompanyAsString = objectMapper.writeValueAsString(updatedCompany);
    String expectedCompanyAsString = getResourceAsString("classpath:contract/mq/expected-company-updated.json");
    JSONAssert.assertEquals(expectedCompanyAsString, updatedCompanyAsString, JSONCompareMode.STRICT);
  }

  @Test
  @Sql("/test-scripts/insert-company-records.sql")
  public void givenCompanyDeletedEvent_shouldCaptureIt() throws InterruptedException {
    // given

    CompanyDeletedEvent companyDeletedEvent = new CompanyDeletedEvent(9000L, 1234, 12345);

    // when
    rabbitTemplate.convertAndSend(COMPANY_EXCHANGE, CompanyDeletedEvent.getEventName(), companyDeletedEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    Optional<Company> persistedCompanyOptional = companyFacade.getByTenantIdAndCompanyId(1234L, 9000L);
    assertThat(persistedCompanyOptional).isNotPresent();
  }

  @Test
  @Sql("/test-scripts/insert-company-records.sql")
  public void givenCompanyOwnerUpdateEvent_shouldCaptureAndUpdateOwnerInRelatedTo() throws InterruptedException, IOException {
    // given

    String companyEventAsString = getResourceAsString("classpath:contract/mq/company-reassigned-event-v2.json");
    HashMap companyOwnerUpdatedEvent = objectMapper.readValue(companyEventAsString, HashMap.class);

    // when
    rabbitTemplate.convertAndSend(COMPANY_EXCHANGE, CompanyOwnerUpdateEventV2.getEventName(), companyOwnerUpdatedEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);

    Call call1 = callRepository.findOne((root, criteriaQuery, criteriaBuilder) -> {
      root.fetch("callRelatedTos");
      return criteriaBuilder.equal(root.get("id"),2400L);
    }).get();
    assertThat(call1.getCallRelatedTos())
        .isNotEmpty()
        .filteredOn(callRelatedTo -> callRelatedTo.getOwnerId().equals(123456L) && callRelatedTo.getEntity().equals("company")
            && callRelatedTo.getEntityId() == 9001)
        .hasSize(1);

    Call call2 = callRepository.findOne((root, criteriaQuery, criteriaBuilder) -> {
      root.fetch("callAssociatedTos");
      return criteriaBuilder.equal(root.get("id"),2400L);
    }).get();

    assertThat(call2.getCallAssociatedTos())
        .isNotEmpty()
        .filteredOn(callAssociatedTo -> callAssociatedTo.getOwnerId().equals(123456L) && callAssociatedTo.getEntity().equals("company")
            && callAssociatedTo.getEntityId() == 9001)
        .hasSize(1);

    Task task = taskRepository.findOne((root, criteriaQuery, criteriaBuilder) -> {
      root.fetch("relatedTo");
      return criteriaBuilder.equal(root.get("id"),2400L);
    }).get();
    assertThat(task.getRelatedTo())
        .isNotEmpty()
        .filteredOn(relatedTo -> relatedTo.getOwnerId().equals(123456L) && relatedTo.getEntityType().equals("COMPANY")
            && relatedTo.getEntityId() == 9001)
        .hasSize(1);
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
  }

  @TestConfiguration
  public static class TestMqSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.rabbitmq.virtual-host=" + "/");
    }
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = resourceLoader.getResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }
}