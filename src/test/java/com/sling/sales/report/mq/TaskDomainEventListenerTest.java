package com.sling.sales.report.mq;

import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.domain.task.Task;
import com.sling.sales.report.core.domain.task.TaskFacade;
import com.sling.sales.report.core.domain.task.TaskRepository;
import com.sling.sales.report.mq.TaskDomainEventListenerTest.TestMqSetup;
import com.sling.sales.report.setup.TestDatabaseInitializer;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import javax.persistence.EntityManager;
import javax.persistence.criteria.JoinType;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.RabbitMQContainer;

@SpringBootTest
@ExtendWith(SpringExtension.class)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
@Sql("/test-scripts/cleanUp-tables.sql")
public class TaskDomainEventListenerTest {
  private static final String PRODUCTIVITY_EXCHANGE = "ex.productivity";
  private static final String TASK_CREATED_EVENT = "task.created";
  private static final String TASK_UPDATED_EVENT = "task.updated";

  private SimpleMessageListenerContainer container;
  private MockMqListener mockMqListener = new MockMqListener();

  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private EntityManager entityManager;

  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private TaskFacade taskFacade;

  @Autowired
  private TaskRepository taskRepository;

  @Autowired
  private ApplicationContextInitializer<ConfigurableApplicationContext> initializer;

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  @BeforeAll
  public static void initialise() {
    rabbitMQContainer.start();
  }

  @AfterAll
  public static void tearDown() {
    rabbitMQContainer.stop();
  }

  @Test
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenTaskCreatedEvent_shouldCreate() throws Exception {
    // given
    String taskEventResource = getResourceAsString("classpath:contract/mq/task-created-event.json");
    HashMap<String, Object> payload = new ObjectMapper().readValue(
        taskEventResource,
        new TypeReference<>() {
          @Override
          public Type getType() {
            return super.getType();
          }
        }
    );
    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TASK_CREATED_EVENT, payload);

    // then
    mockMqListener.latch.await(3, TimeUnit.SECONDS);
    Task persistedTask = taskRepository
        .findOne((root, criteriaQuery, criteriaBuilder) -> {
          root.fetch("owner", JoinType.LEFT);
          root.fetch("assignedTo", JoinType.LEFT);
          root.fetch("relatedTo", JoinType.LEFT);
          return criteriaBuilder.equal(root.get("id"),2405L);
        }).get();

    String persistedTaskJson = new ObjectMapper().writeValueAsString(persistedTask);
    String resourceAsString = getResourceAsString("classpath:contract/mq/task-created-expected-response.json");
    JSONAssert.assertEquals(resourceAsString, persistedTaskJson,new CustomComparator(JSONCompareMode.LENIENT,
        new Customization("relatedTo[*].id", (o1, o2) -> true)));
  }

  @Test
  @Sql("/test-scripts/insert-task-record.sql")
  public void givenTaskUpdatedEvent_shouldUpdate() throws Exception {
    // given
    String taskEventResource = getResourceAsString("classpath:contract/mq/create-or-update-task-event.json");
    HashMap<String, Object> payload = new ObjectMapper().readValue(
        taskEventResource,
        new TypeReference<>() {
          @Override
          public Type getType() {
            return super.getType();
          }
        }
    );
    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TASK_UPDATED_EVENT, payload);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    Task persistedTask = taskRepository
        .findOne((root, criteriaQuery, criteriaBuilder) -> {
          root.fetch("owner", JoinType.LEFT);
          root.fetch("assignedTo", JoinType.LEFT);
          root.fetch("relatedTo", JoinType.LEFT);
          return criteriaBuilder.equal(root.get("id"),2400L);
        }).get();

    String persistedTaskJson = new ObjectMapper().writeValueAsString(persistedTask);
    String resourceAsString = getResourceAsString("classpath:contract/mq/task-updated-expected-response.json");
    JSONAssert.assertEquals(resourceAsString, persistedTaskJson,new CustomComparator(JSONCompareMode.LENIENT,
        new Customization("relatedTo[*].id", (o1, o2) -> true)));
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = resourceLoader.getResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
  }

  @TestConfiguration
  public static class TestMqSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.rabbitmq.virtual-host=" + "/");
    }
  }
}
