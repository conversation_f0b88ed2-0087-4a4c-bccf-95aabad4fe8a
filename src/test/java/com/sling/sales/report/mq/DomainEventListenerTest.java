package com.sling.sales.report.mq;

import static com.sling.sales.report.mq.RabbitMqConfig.COMPANY_EXCHANGE;
import static com.sling.sales.report.mq.RabbitMqConfig.MEETING_EXCHANGE;
import static com.sling.sales.report.mq.RabbitMqConfig.PRODUCTIVITY_EXCHANGE;
import static com.sling.sales.report.mq.RabbitMqConfig.PRODUCT_EXCHANGE;
import static java.util.Collections.singletonList;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sling.sales.report.core.domain.call.CallFacade;
import com.sling.sales.report.core.domain.deal.DealFacade;
import com.sling.sales.report.core.domain.entity.EntityDimensionNameChangeFacade;
import com.sling.sales.report.core.domain.field.EntityType;
import com.sling.sales.report.core.domain.field.Field;
import com.sling.sales.report.core.domain.field.FieldFacade;
import com.sling.sales.report.core.domain.field.FieldType;
import com.sling.sales.report.core.domain.goal.GoalFacade;
import com.sling.sales.report.core.domain.lead.LeadEmail;
import com.sling.sales.report.core.domain.lead.LeadFacade;
import com.sling.sales.report.core.domain.meeting.MeetingInviteeFacade;
import com.sling.sales.report.core.domain.meeting.MeetingRelatedToFacade;
import com.sling.sales.report.core.domain.meeting.OrganizerFacade;
import com.sling.sales.report.core.domain.report.ReportFacade;
import com.sling.sales.report.core.domain.retry.ErrorMessageRecoveryFacade;
import com.sling.sales.report.core.domain.retry.EventPayloadError;
import com.sling.sales.report.core.domain.sharing.ShareRuleFacade;
import com.sling.sales.report.core.domain.sharing.ShareStakeholder;
import com.sling.sales.report.core.domain.sharing.SharedEntity;
import com.sling.sales.report.core.domain.task.TaskFacade;
import com.sling.sales.report.mq.DomainEventListenerTest.TestMqSetup;
import com.sling.sales.report.mq.event.CallDeletedEvent;
import com.sling.sales.report.mq.event.CallEvent;
import com.sling.sales.report.mq.event.CompanyNameUpdatedEvent;
import com.sling.sales.report.mq.event.ContactNameUpdatedEvent;
import com.sling.sales.report.mq.event.DealDeletedEvent;
import com.sling.sales.report.mq.event.DealEvent;
import com.sling.sales.report.mq.event.DealNameUpdatedEvent;
import com.sling.sales.report.mq.event.DealOwnerUpdatedEvent;
import com.sling.sales.report.mq.event.FieldEvent;
import com.sling.sales.report.mq.event.IdName;
import com.sling.sales.report.mq.event.LeadDeletedEventPayload;
import com.sling.sales.report.mq.event.LeadEvent;
import com.sling.sales.report.mq.event.LeadEventPayload;
import com.sling.sales.report.mq.event.LeadEventV2;
import com.sling.sales.report.mq.event.LeadNameUpdatedEvent;
import com.sling.sales.report.mq.event.LeadOwnerUpdateEvent;
import com.sling.sales.report.mq.event.Metadata;
import com.sling.sales.report.mq.event.PipelineUpdatedEvent;
import com.sling.sales.report.mq.event.PipelineUpdatedEvent.Stage;
import com.sling.sales.report.mq.event.PipelineUpdatedEvent.StageTypes;
import com.sling.sales.report.mq.event.ProductNameUpdatedEvent;
import com.sling.sales.report.mq.event.ShareRuleDeletedEvent;
import com.sling.sales.report.mq.event.ShareRuleEvent;
import com.sling.sales.report.mq.event.ShareRuleEventV2;
import com.sling.sales.report.mq.event.TaskDeletedEvent;
import com.sling.sales.report.mq.event.TaskEntityCreatedEvent;
import com.sling.sales.report.mq.event.TaskEvent;
import com.sling.sales.report.mq.event.TaskEvent.EventEntity;
import com.sling.sales.report.mq.event.TenantCreatedEvent;
import com.sling.sales.report.mq.event.UserEmailUpdatedEvent;
import com.sling.sales.report.mq.event.UserNameUpdatedEvent;
import com.sling.sales.report.security.domain.Action;
import com.sling.sales.report.security.domain.Permission;
import com.sling.sales.report.security.domain.User;
import com.sling.sales.report.security.domain.UserFacade;
import com.sling.sales.report.security.domain.UserRepository;
import com.sling.sales.report.setup.TestDatabaseInitializer;
import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.io.ResourceLoader;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.testcontainers.containers.RabbitMQContainer;

@SpringBootTest
@ExtendWith(SpringExtension.class)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
class DomainEventListenerTest {

  private static final String IAM_EXCHANGE = "ex.iam";
  private static final String SALES_EXCHANGE = "ex.sales";
  private static final String CALL_EXCHANGE = "ex.call";
  private static final String CONFIG_EXCHANGE = "ex.config";
  private static final String DEAL_EXCHANGE = "ex.deal";
  private static final String LEAD_CREATED_EVENT = "sales.lead.created";
  private static final String LEAD_UPDATED_EVENT = "sales.lead.updated";
  private static final String LEAD_METAINFO_UPDATED_EVENT = "sales.lead.metainfo.updated";
  private static final String LEAD_DELETED_EVENT = "sales.lead.deleted";
  private static final String CALL_CREATED_EVENT = "call.log.created";
  private static final String CALL_UPDATED_EVENT = "call.log.updated";
  private static final String CALL_DELETED_EVENT = "call.log.deleted";
  private static final String TASK_CREATED_EVENT = "task.created";
  private static final String TASK_UPDATED_EVENT = "task.updated";
  private static final String TASK_DELETED_EVENT = "task.deleted";
  private static final String DEAL_CREATED_EVENT = "deal.created";
  private static final String DEAL_UPDATED_EVENT = "deal.updated";
  private static final String DEAL_DELETED_EVENT = "deal.deleted";
  private static final String USER_NAME_UPDATED_EVENT = "user.name.updated";
  private static final String PRODUCT_NAME_UPDATED_EVENT = "product.name.updated";
  private static final String COMPANY_NAME_UPDATED_EVENT = "company.name.updated";
  private static final String PIPELINE_UPDATED_EVENT = "sales.pipeline.updated";
  private static final String SHARE_RULE_CREATED_EVENT = "sharerule.created";
  private static final String SHARE_RULE_UPDATED_EVENT = "sharerule.updated";
  private static final String SHARE_RULE_DELETED_EVENT = "sharerule.deleted";
  private static final String TENANT_CREATED_EVENT = "iam.tenant.created";
  private static final String TASK_CREATED_EVENT_FROM_CONFIG = "config.task.entity.created";

  private SimpleMessageListenerContainer container;
  private MockMqListener mockMqListener = new MockMqListener();

  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @MockBean
  private LeadFacade leadFacade;
  @MockBean
  private DealFacade dealFacade;
  @MockBean
  private ShareRuleFacade shareRuleFacade;
  @MockBean
  private EntityDimensionNameChangeFacade nameChangeFacade;
  @Autowired
  private UserRepository userRepository;
  @MockBean
  private ReportFacade reportFacade;
  @MockBean
  private CallFacade callFacade;
  @MockBean
  private TaskFacade taskFacade;
  @MockBean
  private FieldFacade fieldFacade;
  @Autowired
  private ResourceLoader resourceLoader;
  @MockBean
  private GoalFacade goalFacade;
  @MockBean
  private OrganizerFacade organizerFacade;
  @MockBean
  private MeetingRelatedToFacade meetingRelatedToFacade;
  @MockBean
  private MeetingInviteeFacade meetingInviteeFacade;

  @Autowired private ErrorMessageRecoveryFacade errorMessageRecoveryFacade;

  @Autowired
  private DomainEventListener domainEventListener;

  @Autowired private UserFacade userFacade;

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  @BeforeAll
  public static void initialise() {
    rabbitMQContainer.start();
  }

  @AfterAll
  public static void tearDown() {
    rabbitMQContainer.stop();
  }

  @Test
  public void givenLeadCreatedEvent_shouldCaptureIt() throws InterruptedException {
    // given
    var event = new LeadEvent();
    event.setId(666L);
    ArgumentCaptor<HashMap> hashMapArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);


    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_CREATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(leadFacade, times(1))
        .create(
            argThat(e -> e.getId().equals(666L)),
           hashMapArgumentCaptor.capture(), eq(false), eq(true));
    Assertions.assertThat(hashMapArgumentCaptor.getValue().get("id")).isEqualTo(666);
  }

  @Test
  public void givenLeadCreatedEvent_shouldPersistInDatabaseOnObjectOptimisticLockingFailure() throws InterruptedException {
    // given
    var event = new LeadEvent();
    event.setId(666L);
    doThrow(new ObjectOptimisticLockingFailureException("something went wrong", new RuntimeException("error"))).when(leadFacade)
        .create(any(LeadEvent.class), any(HashMap.class), eq(false), eq(true));
    ArgumentCaptor<HashMap> hashMapArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_CREATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);

    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("lead") && errorMessage.getEntityId().equals(666L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }
  @Test
  public void givenLeadCreatedEvent_shouldNotExceptionAndSaveEventToTable() throws InterruptedException {
    // given
    var event = new LeadEvent();
    event.setId(669L);
    doThrow(new RuntimeException("something went wrong", new RuntimeException("error thrown"))).when(leadFacade)
        .create(any(LeadEvent.class), any(HashMap.class), eq(false), eq(true));
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_CREATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);

    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("lead") && errorMessage.getEntityId().equals(669L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }
  @Test
  public void givenLeadCreatedEvent_shouldRetryOnWebClientResponseException() throws InterruptedException {
    // given
    var event = new LeadEvent();
    event.setId(666L);
    WebClientResponseException webClientResponseException = new WebClientResponseException(404, "404 Not Found from", null, null, null);
    doThrow(webClientResponseException).when(leadFacade)
        .create(any(LeadEvent.class), any(HashMap.class), eq(false), eq(true));
    ArgumentCaptor<HashMap> hashMapArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);

    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_CREATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);

    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("lead") && errorMessage.getEntityId().equals(666L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);

  }

  @Test
  public void givenLeadCreatedEvent_shouldRetryAndSaveRetriableEntityAfterLastAttempt() throws InterruptedException {
    // given
    var event = new LeadEvent();
    event.setId(666L);
    WebClientResponseException webClientResponseException = new WebClientResponseException(404, "404 Not Found from", null, null, null);
    doThrow(webClientResponseException).when(leadFacade)
        .create(any(LeadEvent.class), any(HashMap.class), eq(false), eq(true));
    ArgumentCaptor<HashMap> hashMapArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_CREATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);

    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("lead") && errorMessage.getEntityId().equals(666L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }

  @Test
  public void givenLeadUpdatedEvent_shouldCaptureIt() throws InterruptedException {
    // given

    var event = new LeadEvent();
    event.setLastName("last name");
    event.setId(777L);
    doNothing().when(callFacade).updateCallRelatedToOwnersByEntityIdAndEntity(anyLong(), anyLong(), anyString());
    doNothing().when(callFacade).updateCallAssociatedToOwnersByEntityIdAndEntity(anyLong(), anyLong(), anyString());
    ArgumentCaptor<HashMap> hashMapArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);

    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_UPDATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(leadFacade, times(1))
        .update(
            argThat(e -> e.getId().equals(777L)),
            hashMapArgumentCaptor.capture(), eq(false));
    HashMap value = hashMapArgumentCaptor.getValue();
    Assertions.assertThat(value.get("id")).isEqualTo(777);
  }

  @Test
  public void givenLeadMetaInfoUpdatedEvent_shouldCaptureIt() throws InterruptedException, ParseException {
    // given
    DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
    String taskDueOn = "2001-07-04T12:08:56.235-0700";
    String meetingScheduledOn = "2004-08-010T12:08:56.235-0700";
    String latestActivity = "2011-12-14T12:08:56.235-0700";
    var event = new LeadEvent();
    event.setLastName("last name");
    event.setId(777L);
    event.setTenantId(1000L);
    event.setLatestActivityCreatedAt(dateFormat.parse(latestActivity));
    event.setTaskDueOn(dateFormat.parse(taskDueOn));
    event.setMeetingScheduledOn(dateFormat.parse(meetingScheduledOn));
    ArgumentCaptor<HashMap> hashMapArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);
    ArgumentCaptor<LeadEvent> leadEventArgumentCaptor = ArgumentCaptor.forClass(LeadEvent.class);

    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_METAINFO_UPDATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(leadFacade, times(1))
        .updateMetaInfo(
            leadEventArgumentCaptor.capture(),
            hashMapArgumentCaptor.capture());

    HashMap value = hashMapArgumentCaptor.getValue();
    LeadEvent leadEvent = leadEventArgumentCaptor.getValue();
    Assertions.assertThat(value.get("id")).isEqualTo(777);
    Assertions.assertThat(value.get("latestActivityCreatedAt")).isEqualTo("2011-12-14T19:08:56.235Z");
    Assertions.assertThat(value.get("meetingScheduledOn")).isEqualTo("2004-08-10T19:08:56.235Z");
    Assertions.assertThat(value.get("taskDueOn")).isEqualTo("2001-07-04T19:08:56.235Z");
    Assertions.assertThat(leadEvent.getId()).isEqualTo(777L);
    Assertions.assertThat(leadEvent.getTenantId()).isEqualTo(1000L);
    Assertions.assertThat(leadEvent.getTaskDueOn().toString()).isEqualTo("Wed Jul 04 19:08:56 UTC 2001");
    Assertions.assertThat(leadEvent.getMeetingScheduledOn().toString()).isEqualTo("Tue Aug 10 19:08:56 UTC 2004");
    Assertions.assertThat(leadEvent.getLatestActivityCreatedAt().toString()).isEqualTo("Wed Dec 14 19:08:56 UTC 2011");
  }

  @Test
  public void givenDealMetaInfoUpdatedEvent_shouldCaptureIt() throws InterruptedException, ParseException {
    // given
    DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
    String taskDueOn = "2001-07-04T12:08:56.235-0700";
    String meetingScheduledOn = "2004-08-010T12:08:56.235-0700";
    String latestActivity = "2011-12-14T12:08:56.235-0700";
    var event = new DealEvent();
    event.setId(777L);
    event.setTenantId(1000L);
    event.setLatestActivityCreatedAt(dateFormat.parse(latestActivity));
    event.setTaskDueOn(dateFormat.parse(taskDueOn));
    event.setMeetingScheduledOn(dateFormat.parse(meetingScheduledOn));
    ArgumentCaptor<HashMap> hashMapArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);
    ArgumentCaptor<DealEvent> dealEventArgumentCaptor = ArgumentCaptor.forClass(DealEvent.class);

    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealEvent.getMetaInfoUpdatedEventName(), event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(dealFacade, times(1))
        .updateMetaInfo(
            dealEventArgumentCaptor.capture(),
            hashMapArgumentCaptor.capture());

    HashMap value = hashMapArgumentCaptor.getValue();
    DealEvent dealEvent = dealEventArgumentCaptor.getValue();
    Assertions.assertThat(value.get("id")).isEqualTo(777);
    Assertions.assertThat(value.get("latestActivityCreatedAt")).isEqualTo("2011-12-14T19:08:56.235Z");
    Assertions.assertThat(value.get("meetingScheduledOn")).isEqualTo("2004-08-10T19:08:56.235Z");
    Assertions.assertThat(value.get("taskDueOn")).isEqualTo("2001-07-04T19:08:56.235Z");
    Assertions.assertThat(dealEvent.getId()).isEqualTo(777L);
    Assertions.assertThat(dealEvent.getTenantId()).isEqualTo(1000L);
    Assertions.assertThat(dealEvent.getTaskDueOn().toString()).isEqualTo("Wed Jul 04 19:08:56 UTC 2001");
    Assertions.assertThat(dealEvent.getMeetingScheduledOn().toString()).isEqualTo("Tue Aug 10 19:08:56 UTC 2004");
    Assertions.assertThat(dealEvent.getLatestActivityCreatedAt().toString()).isEqualTo("Wed Dec 14 19:08:56 UTC 2011");
  }

  @Test
  public void givenLeadDeletedEvent_shouldCaptureIt() throws InterruptedException {
    // given

    LeadDeletedEventPayload leadDeletedEventPayload = new LeadDeletedEventPayload(500L, 222L, 999L);

    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_DELETED_EVENT, leadDeletedEventPayload);
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    // then
    verify(leadFacade, times(1)).delete(500L, 999L, 222L);
  }

  @Test
  public void givenCallCreatedEvent_shouldCaptureIt() throws InterruptedException {
    // given
    var event = new CallEvent();
    event.setId(666L);

    // when
    rabbitTemplate.convertAndSend(CALL_EXCHANGE, CALL_CREATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(callFacade, times(1))
        .create(argThat(e -> e.getId().equals(666L)), argThat(e -> e.get("id").equals(666)), eq(false));
  }

  @Test
  public void givenCallUpdatedEvent_shouldCaptureIt() throws InterruptedException {
    // given
    var event = new CallEvent();
    event.setId(777L);

    // when
    rabbitTemplate.convertAndSend(CALL_EXCHANGE, CALL_UPDATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(callFacade, times(1))
        .update(argThat(e -> e.getId().equals(777L)), argThat(e -> e.get("id").equals(777)), eq(false));
  }

  @Test
  public void givenCallDeletedEvent_shouldCaptureIt() throws InterruptedException {
    // given
    CallDeletedEvent callDeletedEventPayload = new CallDeletedEvent(500L, 222L);

    // when
    rabbitTemplate.convertAndSend(CALL_EXCHANGE, CALL_DELETED_EVENT, callDeletedEventPayload);
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    // then
    verify(callFacade, times(1))
        .delete(argThat(event -> event.getId() == 500L && event.getTenantId() == 222L));
  }

  @Test
  public void givenTaskCreatedEvent_shouldCaptureIt() throws InterruptedException {
    // given
    Date date = new Date();
    var event = new TaskEvent();
    EventEntity entity = new EventEntity();
    entity.setId(666L);
    entity.setOriginalDueDate(date);
    entity.setCompletedAt(null);
    event.setEntity(entity);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TASK_CREATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    ArgumentCaptor<TaskEvent> taskEventArgumentCaptor = ArgumentCaptor.forClass(TaskEvent.class);
    verify(taskFacade, times(1))
        .create(taskEventArgumentCaptor.capture(), argThat(e -> ((HashMap<?, ?>) e.get("entity")).get("id").equals(666)), eq(false));
    Assertions.assertThat(taskEventArgumentCaptor.getValue().getEntity().getOriginalDueDate()).isBeforeOrEqualTo(date);
    Assertions.assertThat(taskEventArgumentCaptor.getValue().getEntity().getOriginalDueDate()).isBeforeOrEqualTo(date);
  }

  @Test
  public void givenTaskUpdatedEvent_shouldCaptureIt() throws InterruptedException {
    // given
    Date date = new Date();
    var event = new TaskEvent();
    EventEntity entity = new EventEntity();
    entity.setId(777L);
    entity.setOriginalDueDate(date);
    entity.setCompletedAt(date);
    event.setEntity(entity);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TASK_UPDATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    ArgumentCaptor<TaskEvent> taskEventArgumentCaptor = ArgumentCaptor.forClass(TaskEvent.class);
    verify(taskFacade, times(1))
        .update(taskEventArgumentCaptor.capture(), argThat(e -> ((HashMap<?, ?>) e.get("entity")).get("id").equals(777)), eq(false));

    Assertions.assertThat(taskEventArgumentCaptor.getValue().getEntity().getOriginalDueDate()).isBeforeOrEqualTo(date);
    Assertions.assertThat(taskEventArgumentCaptor.getValue().getEntity().getOriginalDueDate()).isBeforeOrEqualTo(date);
  }

  @Test
  public void givenTaskDeletedEvent_shouldCaptureIt() throws InterruptedException {
    // given
    var taskDeletedEvent = new TaskDeletedEvent(500L, 222L);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TASK_DELETED_EVENT, taskDeletedEvent);
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    // then
    verify(taskFacade, times(1))
        .delete(argThat(event -> event.getId() == 500L && event.getTenantId() == 222L));
  }

  @Test
  public void givenShareRuleCreatedEvent_shouldCreateNewShareRule() throws InterruptedException {
    // given
    Date now = new Date();
    var event =
        new ShareRuleEvent(
            123L,
            "my share",
            "share all my leads",
            ShareStakeholder.USER,
            100L,
            ShareStakeholder.TEAM,
            101L,
            true,
            SharedEntity.LEAD,
            900L,
            new Action(),
            999L,
            100L, now, now);

    // when
    rabbitTemplate.convertAndSend(CONFIG_EXCHANGE, SHARE_RULE_CREATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(shareRuleFacade, times(1))
        .createOrUpdateShareRule(
            argThat(
                rule ->
                    rule.getId().equals(123L)
                        && rule.getName().equalsIgnoreCase("my share")
                        && rule.getDescription().equalsIgnoreCase("share all my leads")
                        && rule.getSharedByStakeholderType().equals(ShareStakeholder.USER)
                        && rule.getShareByStakeholderId().equals(100L)
                        && rule.getSharedWithStakeholderType().equals(ShareStakeholder.TEAM)
                        && rule.isShareAllRecords()
                        && rule.getEntityId().equals(900L)
                        && rule.getTenantId().equals(999L)
                        && rule.getOwnerId().equals(100L)
                        && rule.getCreatedAt().equals(now)
                        && rule.getUpdatedAt().equals(now)), eq(true));
  }

  @Test
  public void givenMeetingShareRuleCreatedEvent_shouldCreateNewShareRule() throws InterruptedException,
      IOException {
    // given
    Date now = new Date();
    String meetingShareRuleEvent = getResourceAsString("classpath:contract/mq/meeting-share-rule-created-event.json");
    ShareRuleEventV2 shareRuleEventV2 = new ObjectMapper().readValue(meetingShareRuleEvent, ShareRuleEventV2.class);

    // when
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE,ShareRuleEventV2.getMeetingCreatedEvent(),shareRuleEventV2);

    // then
    mockMqListener.latch.await(3, TimeUnit.SECONDS);
    verify(shareRuleFacade, times(1))
        .createOrUpdateShareRule(
            argThat(
                rule ->
                    rule.getId().equals(1L)
                        && rule.getName().equalsIgnoreCase("Share Rule")
                        && rule.getDescription().equalsIgnoreCase("Description")
                        && rule.getSharedByStakeholderType().equals(ShareStakeholder.USER)
                        && rule.getShareByStakeholderId().equals(2L)
                        && rule.getSharedWithStakeholderType().equals(ShareStakeholder.USER)
                        && !rule.isShareAllRecords()
                        && rule.getEntityId().equals(1L)
                        && rule.getTenantId().equals(23L)
                        && rule.getOwnerId().equals(1L)
                        && ObjectUtils.isNotEmpty(rule.getActions())), eq(true));
  }

  @Test
  public void givenMeetingShareRuleUpdatedEvent_shouldCreateNewShareRule() throws InterruptedException,
      IOException {
    // given
    String meetingShareRuleEvent = getResourceAsString("classpath:contract/mq/meeting-share-rule-updated-event.json");
    ShareRuleEventV2 shareRuleEventV2 = new ObjectMapper().readValue(meetingShareRuleEvent, ShareRuleEventV2.class);

    // when
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE,ShareRuleEventV2.getMeetingUpdatedEvent(),shareRuleEventV2);

    // then
    mockMqListener.latch.await(3, TimeUnit.SECONDS);
    verify(shareRuleFacade, times(1))
        .createOrUpdateShareRule(
            argThat(
                rule ->
                    rule.getId().equals(1L)
                        && rule.getName().equalsIgnoreCase("updated name")
                        && rule.getDescription().equalsIgnoreCase("Description")
                        && rule.getSharedByStakeholderType().equals(ShareStakeholder.USER)
                        && rule.getShareByStakeholderId().equals(2L)
                        && rule.getSharedWithStakeholderType().equals(ShareStakeholder.USER)
                        && !rule.isShareAllRecords()
                        && rule.getEntityId().equals(1L)
                        && rule.getTenantId().equals(33L)
                        && rule.getOwnerId().equals(1L)
                        && ObjectUtils.isNotEmpty(rule.getActions())), eq(false));
  }

  @Test
  public void givenMeetingShareRuleDeletedEvent_shouldDeleteShareRule() throws InterruptedException,
      IOException {
    // given
    Date now = new Date();
    String meetingShareRuleEvent = getResourceAsString("classpath:contract/mq/meeting-share-rule-updated-event.json");
    ShareRuleEventV2 shareRuleEventV2 = new ObjectMapper().readValue(meetingShareRuleEvent, ShareRuleEventV2.class);

    // when
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE,ShareRuleEventV2.getMeetingDeletedEvent(),shareRuleEventV2);

    // then
    mockMqListener.latch.await(3, TimeUnit.SECONDS);
    verify(shareRuleFacade, times(1))
        .deleteShareRule(
            argThat(
                rule ->
                    rule.getId().equals(1L)
                        && rule.getTenantId().equals(33L)
                        && rule.getOwnerId().equals(1L)
                        && rule.getSharedEntity().equals(SharedEntity.MEETING)
                        && rule.getSharedEntityId().equals(1L)));
  }

  @Test
  public void givenShareRuleDeletedEvent_shouldDeleteShareRule() throws InterruptedException {
    // given
    var deletedEvent =
        new ShareRuleDeletedEvent(123L, 900L, 999L, 100L, SharedEntity.LEAD);

    // when
    rabbitTemplate.convertAndSend(CONFIG_EXCHANGE, SHARE_RULE_DELETED_EVENT, deletedEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(shareRuleFacade, times(1))
        .deleteShareRule(
            argThat(
                event -> event.getId().equals(123L)
                    && event.getTenantId().equals(999L)
                    && event.getOwnerId().equals(100L)
                    && event.getSharedEntity().equals(SharedEntity.LEAD)
                    && event.getSharedEntityId().equals(900L)));
  }

  @Test
  public void givenShareRuleUpdatedEvent_shouldUpdateExistingShareRule()
      throws InterruptedException {
    // given
    Date now = new Date();
    var event =
        new ShareRuleEvent(
            123L,
            "my share",
            "share all my leads",
            ShareStakeholder.USER,
            100L,
            ShareStakeholder.TEAM,
            101L,
            true,
            SharedEntity.LEAD,
            900L,
            new Action(),
            999L,
            100L, now, now);

    // when
    rabbitTemplate.convertAndSend(CONFIG_EXCHANGE, SHARE_RULE_UPDATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(shareRuleFacade, times(1))
        .createOrUpdateShareRule(
            argThat(
                rule ->
                    rule.getId().equals(123L)
                        && rule.getName().equalsIgnoreCase("my share")
                        && rule.getDescription().equalsIgnoreCase("share all my leads")
                        && rule.getSharedByStakeholderType().equals(ShareStakeholder.USER)
                        && rule.getShareByStakeholderId().equals(100L)
                        && rule.getSharedWithStakeholderType().equals(ShareStakeholder.TEAM)
                        && rule.isShareAllRecords()
                        && rule.getEntityId().equals(900L)
                        && rule.getTenantId().equals(999L)
                        && rule.getOwnerId().equals(100L)
                        && rule.getCreatedAt().equals(now)
                        && rule.getUpdatedAt().equals(now)), eq(false));
  }

  @Test
  public void givenUserNameChangedEvent_shouldUpdateUser() throws InterruptedException {
    // given

    var event = new UserNameUpdatedEvent(
        10,
        1,
        "Tom",
        "Hanks"
    );

    // when
    rabbitTemplate.convertAndSend(IAM_EXCHANGE, USER_NAME_UPDATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(nameChangeFacade, times(1))
        .updateUserName(
            argThat(e ->
                e.getFirstName().equals("Tom")
                    && e.getLastName().equals("Hanks")
                    && e.getUserId() == 1
                    && e.getTenantId() == 10
            )
        );
    verify(goalFacade, times(1)).updateNameByUserId(1L, "Tom", "Hanks");
    Mockito.verify(organizerFacade, times(1)).updateOrganizerNameByEntityIdAndEntity(1L, "user", "Tom Hanks");
    Mockito.verify(meetingInviteeFacade, times(1)).updateMeetingInviteeNameByEntityIdAndEntity(1L, "user", "Tom Hanks");
  }

  @Test
  public void givenPipelineUpdatedEvent_shouldUpdate() throws InterruptedException {
    // given

    List<Stage> stages =
        singletonList(new Stage(1L, "Intro", 1, "Introduction", StageTypes.OPEN));
    var event =
        new PipelineUpdatedEvent(
            "DEAL",
            1L,
            10L,
            "Stark Expo Pipeline",
            stages,
            new String[]{"lost reason"},
            new String[]{"unqualified reason"});

    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, PIPELINE_UPDATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(nameChangeFacade, times(1))
        .updatePipeline(
            argThat(e ->
                e.getName().equals("Stark Expo Pipeline")
            )
        );
  }

  @Test
  public void givenProductNameChangedEvent_shouldUpdateProduct() throws InterruptedException {
    // given

    var event = new ProductNameUpdatedEvent(
        10,
        1,
        "New Product"
    );

    // when
    rabbitTemplate.convertAndSend(PRODUCT_EXCHANGE, PRODUCT_NAME_UPDATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(nameChangeFacade, times(1))
        .updateProductName(
            argThat(e ->
                e.getProductName().equals("New Product")
                    && e.getProductId() == 1
                    && e.getTenantId() == 10
            )
        );
  }

  @Test
  public void givenTenantCreatedEvent_shouldCreateUser() throws InterruptedException {
    // given
    Set<Permission> permissions = new HashSet<>();
    Action reportAction = new Action();
    reportAction.setRead(true);
    reportAction.setWrite(true);
    permissions.add(new Permission(1, "report", "report", reportAction));

    var event = new TenantCreatedEvent(
        10,
        100,
        "Tony",
        "Stark",
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.zc4qnro7F5z4xbNmEfNkriIuvo7NMMiRZ6jqvUnmmBw");

    // when
    rabbitTemplate.convertAndSend(IAM_EXCHANGE, TENANT_CREATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    User persistedUser = userRepository.findById(10L).get();
    Assertions.assertThat(persistedUser.getName()).isEqualTo("Tony Stark");
    Mockito.verify(reportFacade, times(1)).createDefaultReports(any(User.class));
  }

  @Test
  public void givenTenantCreatedEvent_shouldCreateDefaultTaskReport() throws InterruptedException {
    // given
    Set<Permission> permissions = new HashSet<>();
    Action reportAction = new Action();
    reportAction.setRead(true);
    reportAction.setWrite(true);
    permissions.add(new Permission(1, "report", "report", reportAction));
    doNothing().when(callFacade).updateCallRelatedToOwnersByEntityIdAndEntity(anyLong(), anyLong(), anyString());
    doNothing().when(callFacade).updateCallAssociatedToOwnersByEntityIdAndEntity(anyLong(), anyLong(), anyString());

    var event = new TaskEntityCreatedEvent(
        10,
        100,
        "Tony",
        "Stark");

    // when
    rabbitTemplate.convertAndSend(CONFIG_EXCHANGE, TASK_CREATED_EVENT_FROM_CONFIG, event);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    User persistedUser = userRepository.findById(10L).get();
    Assertions.assertThat(persistedUser.getName()).isEqualTo("Tony Stark");
    Mockito.verify(reportFacade, times(1)).createDefaultTaskReports(any(User.class));
  }


  @Test
  public void givenDealCreatedEvent_shouldCaptureIt() throws Exception {
    // given
    String dealEventPayload = getResourceAsString("classpath:contract/mq/deal-created-event.json");
    DealEvent dealEvent = new ObjectMapper().readValue(dealEventPayload,DealEvent.class);
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_CREATED_EVENT, dealEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    ArgumentCaptor<DealEvent> dealEventArgumentCaptor = ArgumentCaptor.forClass(DealEvent.class);
    verify(dealFacade, times(1))
        .create(
            dealEventArgumentCaptor.capture(),
            argThat(e -> e.size() == 37 && e.get("id").equals(1)), eq(false), eq(true));

    String dealEventResponse = getResourceAsString("classpath:contract/mq/deal-created-event-response.json");

    JSONAssert.assertEquals(dealEventResponse,new ObjectMapper().writeValueAsString(dealEventArgumentCaptor.getValue()), JSONCompareMode.STRICT);
  }

  @Test
  public void givenDealUpdatedEvent_shouldCaptureIt() throws InterruptedException {
    // given
    var event = new DealEvent();
    event.setOwnedBy(new IdName(1L, "user"));
    event.setId(666L);

    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_UPDATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(dealFacade, times(1))
        .update(
            argThat(e -> e.getId().equals(666L)),
            argThat(e -> e.size() == 37 && e.get("id").equals(666)), eq(false));
  }

  @Test
  public void givenDealDeletedEvent_shouldCaptureIt() throws InterruptedException {
    // given
    var event = new DealDeletedEvent(1, 10, 1);

    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_DELETED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(dealFacade, times(1))
        .delete(argThat(e -> e.getId() == 1 && e.getTenantId() == 10 && e.getUserId() == 1));
  }

  @Test
  public void givenCompanyNameChangedEvent_shouldUpdate() throws InterruptedException {
    // given

    var event = new CompanyNameUpdatedEvent(
        10L,
        1L,
        "New Company"
    );

    // when
    rabbitTemplate.convertAndSend(COMPANY_EXCHANGE, COMPANY_NAME_UPDATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    verify(nameChangeFacade, times(1))
        .updateAssociatedCompanyName(
            argThat(e ->
                e.getCompanyName().equals("New Company")
                    && e.getCompanyId() == 1L
                    && e.getTenantId() == 10L
            )
        );
    verify(nameChangeFacade, times(1))
        .updateContactCompanyName(
            argThat(e ->
                e.getCompanyName().equals("New Company")
                    && e.getCompanyId() == 1L
                    && e.getTenantId() == 10L
            )
        );
  }

  @Test
  public void givenLeadFieldCreatedEvent_shouldCreateField() throws InterruptedException {
    //given
    Field field = new Field(1L, 2L, "myCustomField", FieldType.TEXT_FIELD, EntityType.LEAD, true, true, null);
    FieldEvent fieldEvent = new FieldEvent(12L, Collections.singletonList(field));
    //when
    rabbitTemplate.convertAndSend(CONFIG_EXCHANGE, FieldEvent.getLeadFieldCreatedEventName(), fieldEvent);
    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    ArgumentCaptor<FieldEvent> fieldEventArgumentCaptor = ArgumentCaptor.forClass(FieldEvent.class);
    verify(fieldFacade, times(1)).createField(fieldEventArgumentCaptor.capture(), eq(EntityType.LEAD));
    Assertions.assertThat(fieldEventArgumentCaptor.getValue().getTenantId()).isEqualTo(12L);
    Assertions.assertThat(fieldEventArgumentCaptor.getValue().getFields().get(0)).isEqualToComparingFieldByField(field);
  }

  @Test
  public void givenContactNameUpdateEvent_shouldUpdateContactNameInDealContact() throws InterruptedException {
    //given
    ContactNameUpdatedEvent contactNameUpdatedEvent = new ContactNameUpdatedEvent(1L, "firstName", "lastName", 11L);
    //when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactNameUpdatedEvent.getEventName(), contactNameUpdatedEvent);
    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    ArgumentCaptor<ContactNameUpdatedEvent> contactNameUpdatedEventArgumentCaptor = ArgumentCaptor.forClass(ContactNameUpdatedEvent.class);
    verify(nameChangeFacade, times(1)).updateDealContactName(contactNameUpdatedEventArgumentCaptor.capture());
    ContactNameUpdatedEvent contactNameUpdated = contactNameUpdatedEventArgumentCaptor.getValue();
    Assertions.assertThat(contactNameUpdated.getContactId()).isEqualTo(1L);
    Assertions.assertThat(contactNameUpdated.getFirstName()).isEqualTo("firstName");
    Assertions.assertThat(contactNameUpdated.getLastName()).isEqualTo("lastName");
    verify(organizerFacade, times(1)).updateOrganizerNameByEntityIdAndEntity(1L, "contact", "firstName lastName");
    verify(meetingRelatedToFacade, times(1)).updateMeetingRelatedToNameByEntityIdAndEntity(1L, "contact", "firstName lastName");
    verify(meetingInviteeFacade, times(1)).updateMeetingInviteeNameByEntityIdAndEntity(1L, "contact", "firstName lastName");
    verify(callFacade, times(1)).updateCallAssociatedToEntityNameByEntityIdAndEntity("firstName lastName", 1L, "contact");
    verify(callFacade, times(1)).updateCallRelatedToEntityNameByEntityIdAndEntity("firstName lastName", 1L, "contact");
    verify(taskFacade, times(1)).updateTaskRelatedToNameByEntityIdAndEntity(11L, 1L, "CONTACT","firstName lastName");
  }

  @Test
  public void givenLeadNameUpdateEvent_shouldUpdateLeadNameInAssociatedEntities() throws InterruptedException {
    //given
    LeadNameUpdatedEvent leadNameUpdatedEvent = new LeadNameUpdatedEvent(101L, 1L, "firstName", "lastName");
    //when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadNameUpdatedEvent.getEventName(), leadNameUpdatedEvent);
    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    verify(organizerFacade, times(1)).updateOrganizerNameByEntityIdAndEntity(1L, "lead", "firstName lastName");
    verify(meetingRelatedToFacade, times(1)).updateMeetingRelatedToNameByEntityIdAndEntity(1L, "lead", "firstName lastName");
    verify(meetingInviteeFacade, times(1)).updateMeetingInviteeNameByEntityIdAndEntity(1L, "lead", "firstName lastName");
    verify(callFacade, times(1)).updateCallAssociatedToEntityNameByEntityIdAndEntity("firstName lastName", 1L, "lead");
    verify(callFacade, times(1)).updateCallRelatedToEntityNameByEntityIdAndEntity("firstName lastName", 1L, "lead");
    verify(taskFacade, times(1)).updateTaskRelatedToNameByEntityIdAndEntity(101L, 1L, "LEAD","firstName lastName");
  }

  @Test
  public void givenLeadFieldUpdatedEvent_shouldUpdatedField() throws InterruptedException {
    //given
    Field field = new Field(1L, 2L, "myCustomField", FieldType.TEXT_FIELD, EntityType.LEAD, false, true, null);
    FieldEvent fieldEvent = new FieldEvent(12L, Collections.singletonList(field));
    //when
    rabbitTemplate.convertAndSend(CONFIG_EXCHANGE, FieldEvent.getLeadFieldUpdatedEventName(), fieldEvent);
    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    ArgumentCaptor<FieldEvent> fieldEventArgumentCaptor = ArgumentCaptor.forClass(FieldEvent.class);
    verify(fieldFacade, times(1)).updateField(fieldEventArgumentCaptor.capture(), eq(EntityType.LEAD));
    Assertions.assertThat(fieldEventArgumentCaptor.getValue().getTenantId()).isEqualTo(12L);
    Assertions.assertThat(fieldEventArgumentCaptor.getValue().getFields().get(0)).isEqualToComparingFieldByField(field);
  }

  @Test
  public void givenDealFieldCreatedEvent_shouldCreateField() throws InterruptedException {
    //given
    Field field = new Field(1L, 2L, "myCustomField", FieldType.TEXT_FIELD, EntityType.DEAL, true, true, null);
    FieldEvent fieldEvent = new FieldEvent(12L, Collections.singletonList(field));
    //when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, FieldEvent.getDealFieldCreatedEventName(), fieldEvent);
    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    ArgumentCaptor<FieldEvent> fieldEventArgumentCaptor = ArgumentCaptor.forClass(FieldEvent.class);
    verify(fieldFacade, times(1)).createField(fieldEventArgumentCaptor.capture(), eq(EntityType.DEAL));
    Assertions.assertThat(fieldEventArgumentCaptor.getValue().getTenantId()).isEqualTo(12L);
    Assertions.assertThat(fieldEventArgumentCaptor.getValue().getFields().get(0)).isEqualToComparingFieldByField(field);
  }

  @Test
  public void givenDealFieldCreatedEvent_withMoneyField_shouldCallCreateField() throws InterruptedException {
    //given
    Field field = new Field(1L, 2L, "myCustomField", FieldType.MONEY, EntityType.DEAL, true, true, null);
    FieldEvent fieldEvent = new FieldEvent(12L, Collections.singletonList(field));
    //when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, FieldEvent.getDealFieldCreatedEventName(), fieldEvent);
    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    ArgumentCaptor<FieldEvent> fieldEventArgumentCaptor = ArgumentCaptor.forClass(FieldEvent.class);
    verify(fieldFacade, times(1)).createField(fieldEventArgumentCaptor.capture(), eq(EntityType.DEAL));
    Assertions.assertThat(fieldEventArgumentCaptor.getValue().getTenantId()).isEqualTo(12L);
    Assertions.assertThat(fieldEventArgumentCaptor.getValue().getFields().get(0)).isEqualToComparingFieldByField(field);
  }

  @Test
  public void givenDealFieldUpdatedEvent_shouldUpdatedField() throws InterruptedException {
    //given
    Field field = new Field(1L, 2L, "myCustomField", FieldType.TEXT_FIELD, EntityType.DEAL, false, true, null);
    FieldEvent fieldEvent = new FieldEvent(12L, Collections.singletonList(field));
    //when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, FieldEvent.getDealFieldUpdatedEventName(), fieldEvent);
    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    ArgumentCaptor<FieldEvent> fieldEventArgumentCaptor = ArgumentCaptor.forClass(FieldEvent.class);
    verify(fieldFacade, times(1)).updateField(fieldEventArgumentCaptor.capture(), eq(EntityType.DEAL));
    Assertions.assertThat(fieldEventArgumentCaptor.getValue().getTenantId()).isEqualTo(12L);
    Assertions.assertThat(fieldEventArgumentCaptor.getValue().getFields().get(0)).isEqualToComparingFieldByField(field);
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = resourceLoader.getResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  @Test
  public void givenDealNameUpdatedEvent_shouldUpdateContactDeal() throws InterruptedException {
    //given
    DealNameUpdatedEvent dealNameUpdatedEvent = new DealNameUpdatedEvent(101L, "updated name", 102L);
    //when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealNameUpdatedEvent.getEventName(), dealNameUpdatedEvent);
    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    ArgumentCaptor<DealNameUpdatedEvent> dealNameUpdatedEventArgumentCaptor = ArgumentCaptor.forClass(DealNameUpdatedEvent.class);
    verify(nameChangeFacade, times(1)).updateContactDealName(dealNameUpdatedEventArgumentCaptor.capture());
    DealNameUpdatedEvent event = dealNameUpdatedEventArgumentCaptor.getValue();
    Assertions.assertThat(event.getId()).isEqualTo(101L);
    Assertions.assertThat(event.getName()).isEqualTo("updated name");
    Assertions.assertThat(event.getTenantId()).isEqualTo(102L);
    verify(organizerFacade, times(1)).updateOrganizerNameByEntityIdAndEntity(101L, "deal", "updated name");
    verify(meetingRelatedToFacade, times(1)).updateMeetingRelatedToNameByEntityIdAndEntity(101L, "deal", "updated name");
    verify(meetingInviteeFacade, times(1)).updateMeetingInviteeNameByEntityIdAndEntity(101L, "deal", "updated name");
    verify(callFacade, times(1)).updateCallAssociatedToEntityNameByEntityIdAndEntity("updated name", 101L, "deal");
    verify(callFacade, times(1)).updateCallRelatedToEntityNameByEntityIdAndEntity("updated name", 101L, "deal");
    verify(taskFacade, times(1)).updateTaskRelatedToNameByEntityIdAndEntity(102L, 101L, "DEAL","updated name");
  }

  @Test
  public void givenLeadCreateOperationForRetry_shouldRetryIt() throws JsonProcessingException {
    //given
    Map<String, Object> leadCreatedEvent = new HashMap<>();
    leadCreatedEvent.put("id", 1);
    leadCreatedEvent.put("tenantId", 1);
    leadCreatedEvent.put("firstName", "test name");

    //when
    domainEventListener.retryEntityOperation(EntityType.LEAD, "sales.lead.created", (HashMap<String, Object>) leadCreatedEvent);

    //then
    ArgumentCaptor<LeadEvent> leadEventArgumentCaptor = ArgumentCaptor.forClass(LeadEvent.class);
    ArgumentCaptor<HashMap> payloadArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);
    verify(leadFacade, times(1)).create(leadEventArgumentCaptor.capture(), payloadArgumentCaptor.capture(),
        eq(false), eq(true));
    LeadEvent leadEvent = leadEventArgumentCaptor.getValue();
    Assertions.assertThat(leadEvent.getId()).isEqualTo(1);
    Assertions.assertThat(leadEvent.getTenantId()).isEqualTo(1);
    Assertions.assertThat(leadEvent.getFirstName()).isEqualTo("test name");
    Assertions.assertThat(payloadArgumentCaptor.getValue()).isEqualToComparingFieldByField(leadCreatedEvent);
  }

  @Test
  public void givenLeadUpdateOperationForRetry_shouldRetryIt() throws JsonProcessingException {
    //given
    Map<String, Object> leadUpdatedEvent = new HashMap<>();
    leadUpdatedEvent.put("id", 1);
    leadUpdatedEvent.put("tenantId", 1);
    leadUpdatedEvent.put("firstName", "test name");
    doNothing().when(callFacade).updateCallRelatedToOwnersByEntityIdAndEntity(anyLong(), anyLong(), anyString());
    doNothing().when(callFacade).updateCallAssociatedToOwnersByEntityIdAndEntity(anyLong(), anyLong(), anyString());

    //when
    domainEventListener.retryEntityOperation(EntityType.LEAD, "sales.lead.updated", (HashMap<String, Object>) leadUpdatedEvent);

    //then
    ArgumentCaptor<LeadEvent> leadEventArgumentCaptor = ArgumentCaptor.forClass(LeadEvent.class);
    ArgumentCaptor<HashMap> payloadArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);
    verify(leadFacade, times(1)).update(leadEventArgumentCaptor.capture(), payloadArgumentCaptor.capture(), eq(false));
    LeadEvent leadEvent = leadEventArgumentCaptor.getValue();
    Assertions.assertThat(leadEvent.getId()).isEqualTo(1);
    Assertions.assertThat(leadEvent.getTenantId()).isEqualTo(1);
    Assertions.assertThat(leadEvent.getFirstName()).isEqualTo("test name");
    Assertions.assertThat(payloadArgumentCaptor.getValue()).isEqualToComparingFieldByField(leadUpdatedEvent);
  }

  @Test
  public void givenDealCreatedEvent_shouldSaveEventToTableOnException() throws InterruptedException {
    // given
    var event = new DealEvent();
    event.setId(669L);
    doThrow(new RuntimeException("something went wrong", new RuntimeException("error thrown"))).when(dealFacade)
        .create(any(DealEvent.class), any(HashMap.class), eq(false), eq(true));

    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_CREATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("deal") && errorMessage.getEntityId().equals(669L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }

  @Test
  public void givenDealCreatedEvent_shouldRetryAndSaveRetriableEntityAfterLastAttempt() throws InterruptedException {
    // given
    var event = new DealEvent();
    event.setId(666L);
    WebClientResponseException webClientResponseException = new WebClientResponseException(404, "404 Not Found from", null, null, null);
    doThrow(webClientResponseException).when(dealFacade)
        .create(any(DealEvent.class), any(HashMap.class), eq(false), eq(true));

    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_CREATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);

    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("deal") && errorMessage.getEntityId().equals(666L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }

  @Test
  public void givenDealCreateOperationForRetry_shouldRetryIt() throws JsonProcessingException {
    //given
    Map<String, Object> dealCreatedEvent = new HashMap<>();
    dealCreatedEvent.put("id", 1);
    dealCreatedEvent.put("tenantId", 1);

    //when
    domainEventListener.retryEntityOperation(EntityType.DEAL, "deal.created", (HashMap<String, Object>) dealCreatedEvent);

    //then
    ArgumentCaptor<DealEvent> dealEventArgumentCaptor = ArgumentCaptor.forClass(DealEvent.class);
    ArgumentCaptor<HashMap> payloadArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);
    verify(dealFacade, times(1)).create(dealEventArgumentCaptor.capture(), payloadArgumentCaptor.capture(),
        eq(false), eq(true));
    DealEvent dealEvent = dealEventArgumentCaptor.getValue();
    Assertions.assertThat(dealEvent.getId()).isEqualTo(1);
    Assertions.assertThat(dealEvent.getTenantId()).isEqualTo(1);
    Assertions.assertThat(payloadArgumentCaptor.getValue()).isEqualToComparingFieldByField(dealCreatedEvent);
  }

  @Test
  public void givenDealUpdateOperationForRetry_shouldRetryIt() throws JsonProcessingException {
    //given
    Map<String, Object> dealUpdatedEvent = new HashMap<>();
    Map<String, Object> idName = new HashMap<>() {{
      put("id", 1);
      put("name", "user");
    }};
    dealUpdatedEvent.put("id", 1);
    dealUpdatedEvent.put("tenantId", 1);
    dealUpdatedEvent.put("ownedBy", idName);
    doNothing().when(callFacade).updateCallRelatedToOwnersByEntityIdAndEntity(eq(1L), eq(1L), eq("deal"));
    doNothing().when(callFacade).updateCallAssociatedToOwnersByEntityIdAndEntity(eq(1L), eq(1L), eq("deal"));

    //when
    domainEventListener.retryEntityOperation(EntityType.DEAL, "deal.updated", (HashMap<String, Object>) dealUpdatedEvent);

    //then
    ArgumentCaptor<DealEvent> dealEventArgumentCaptor = ArgumentCaptor.forClass(DealEvent.class);
    ArgumentCaptor<HashMap> payloadArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);
    verify(dealFacade, times(1)).update(dealEventArgumentCaptor.capture(), payloadArgumentCaptor.capture(),
        eq(false));
    DealEvent dealEvent = dealEventArgumentCaptor.getValue();
    Assertions.assertThat(dealEvent.getId()).isEqualTo(1);
    Assertions.assertThat(dealEvent.getTenantId()).isEqualTo(1);
    Assertions.assertThat(payloadArgumentCaptor.getValue()).isEqualToComparingFieldByField(dealUpdatedEvent);
  }

  @Test
  public void givenCallCreatedEvent_shouldSaveEventToTableOnException() throws InterruptedException {
    // given
    var event = new CallEvent();
    event.setId(669L);
    doThrow(new RuntimeException("something went wrong", new RuntimeException("error thrown"))).when(callFacade)
        .create(any(CallEvent.class), any(HashMap.class), eq(false));

    // when
    rabbitTemplate.convertAndSend(CALL_EXCHANGE, CALL_CREATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("call") && errorMessage.getEntityId().equals(669L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }

  @Test
  public void givenCallCreatedEvent_shouldRetryAndSaveRetriableEntityAfterLastAttempt() throws InterruptedException {
    // given
    var event = new CallEvent();
    event.setId(666L);
    WebClientResponseException webClientResponseException = new WebClientResponseException(404, "404 Not Found from", null, null, null);
    doThrow(webClientResponseException).when(callFacade)
        .create(any(CallEvent.class), any(HashMap.class), eq(false));

    // when
    rabbitTemplate.convertAndSend(CALL_EXCHANGE, CALL_CREATED_EVENT, event);


    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);

    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("call") && errorMessage.getEntityId().equals(666L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }

  @Test
  public void givenCallCreateOperationForRetry_shouldRetryIt() throws JsonProcessingException {
    //given
    Map<String, Object> callCreatedEvent = new HashMap<>();
    callCreatedEvent.put("id", 1);
    callCreatedEvent.put("tenantId", 1);

    //when
    domainEventListener.retryEntityOperation(EntityType.CALL, "call.log.created", (HashMap<String, Object>) callCreatedEvent);

    //then
    ArgumentCaptor<CallEvent> callEventArgumentCaptor = ArgumentCaptor.forClass(CallEvent.class);
    ArgumentCaptor<HashMap> payloadArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);
    verify(callFacade, times(1)).create(callEventArgumentCaptor.capture(), payloadArgumentCaptor.capture(),
        eq(false));
    CallEvent callEvent = callEventArgumentCaptor.getValue();
    Assertions.assertThat(callEvent.getId()).isEqualTo(1);
    Assertions.assertThat(callEvent.getTenantId()).isEqualTo(1);
    Assertions.assertThat(payloadArgumentCaptor.getValue()).isEqualToComparingFieldByField(callCreatedEvent);
  }

  @Test
  public void givenTaskCreatedEvent_shouldSaveEventToTableOnException() throws InterruptedException {
    // given
    var event = new TaskEvent();
    var entity = new EventEntity();
    entity.setId(669L);
    event.setEntity(entity);
    doThrow(new RuntimeException("something went wrong", new RuntimeException("error thrown"))).when(taskFacade)
        .create(any(TaskEvent.class), any(HashMap.class), eq(false));

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TASK_CREATED_EVENT, event);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("task") && errorMessage.getEntityId().equals(669L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }

  @Test
  public void givenTaskCreatedEvent_shouldPersistErrorIntoDatabase() throws InterruptedException {
    // given
    var event = new TaskEvent();
    var entity = new EventEntity();
    entity.setId(666L);
    event.setEntity(entity);
    WebClientResponseException webClientResponseException = new WebClientResponseException(404, "404 Not Found from", null, null, null);
    doThrow(webClientResponseException).when(taskFacade)
        .create(any(TaskEvent.class), any(HashMap.class), eq(false));

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TASK_CREATED_EVENT, event);


    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);

    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("task") && errorMessage.getEntityId().equals(666L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }

  @Test
  public void givenTaskCreateOperationForRetry_shouldRetryIt() throws JsonProcessingException {
    //given
    Map<String, Object> taskCreatedEvent = new HashMap<>();
    Map<String, Object> taskEntity = new HashMap<>();
    taskEntity.put("id", 1);
    taskEntity.put("tenantId", 1);
    taskCreatedEvent.put("entity", taskEntity);

    //when
    domainEventListener.retryEntityOperation(EntityType.TASK, "task.created", (HashMap<String, Object>) taskCreatedEvent);

    //then
    ArgumentCaptor<TaskEvent> taskEventArgumentCaptor = ArgumentCaptor.forClass(TaskEvent.class);
    ArgumentCaptor<HashMap> payloadArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);
    verify(taskFacade, times(1)).create(taskEventArgumentCaptor.capture(), payloadArgumentCaptor.capture(),
        eq(false));
    TaskEvent taskEvent = taskEventArgumentCaptor.getValue();
    Assertions.assertThat(taskEvent.getEntity().getId()).isEqualTo(1);
    Assertions.assertThat(taskEvent.getEntity().getTenantId()).isEqualTo(1);
    Assertions.assertThat(payloadArgumentCaptor.getValue()).isEqualToComparingFieldByField(taskCreatedEvent);
  }

  @Test
  public void givenShareRuleCreatedEvent_shouldSaveEventToTableOnException() throws InterruptedException {
    // given
    ShareRuleEvent shareRuleEvent = getShareRuleEvent(1L, 100L, 10L, 10L,
        2L, ShareStakeholder.USER);
    doThrow(new RuntimeException("something went wrong", new RuntimeException("error thrown"))).when(shareRuleFacade)
        .createOrUpdateShareRule(any(ShareRuleEvent.class), eq(true));

    // when
    rabbitTemplate.convertAndSend(CONFIG_EXCHANGE, SHARE_RULE_CREATED_EVENT, shareRuleEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("share_rule") && errorMessage.getEntityId().equals(1L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }

  @Test
  public void givenShareRuleCreatedEvent_shouldPersistErrorIntoDatabase() throws InterruptedException {
    // given
    ShareRuleEvent shareRuleEvent = getShareRuleEvent(2L, 100L, 10L, 10L,
        2L, ShareStakeholder.USER);
    WebClientResponseException webClientResponseException = new WebClientResponseException(404, "404 Not Found from", null, null, null);
    doThrow(webClientResponseException).when(shareRuleFacade)
        .createOrUpdateShareRule(any(ShareRuleEvent.class), eq(true));

    // when
    rabbitTemplate.convertAndSend(CONFIG_EXCHANGE, SHARE_RULE_CREATED_EVENT, shareRuleEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);

    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("share_rule") && errorMessage.getEntityId().equals(2L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }

  @Test
  public void givenShareRuleUpdatedEvent_shouldSaveEventToTableOnException() throws InterruptedException {
    // given
    ShareRuleEvent shareRuleEvent = getShareRuleEvent(3L, 100L, 10L, 10L,
        2L, ShareStakeholder.USER);
    doThrow(new RuntimeException("something went wrong", new RuntimeException("error thrown"))).when(shareRuleFacade)
        .createOrUpdateShareRule(any(ShareRuleEvent.class), eq(false));

    // when
    rabbitTemplate.convertAndSend(CONFIG_EXCHANGE, SHARE_RULE_UPDATED_EVENT, shareRuleEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("share_rule") && errorMessage.getEntityId().equals(3L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }

  @Test
  public void givenShareRuleUpdatedEvent_shouldPersistErrorIntoDatabase() throws InterruptedException {
    // given
    ShareRuleEvent shareRuleEvent = getShareRuleEvent(4L, 100L, 10L, 10L,
        2L, ShareStakeholder.USER);
    WebClientResponseException webClientResponseException = new WebClientResponseException(404, "404 Not Found from", null, null, null);
    doThrow(webClientResponseException).when(shareRuleFacade)
        .createOrUpdateShareRule(any(ShareRuleEvent.class), eq(false));

    // when
    rabbitTemplate.convertAndSend(CONFIG_EXCHANGE, SHARE_RULE_UPDATED_EVENT, shareRuleEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);

    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("share_rule") && errorMessage.getEntityId().equals(4L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }

  @Test
  public void givenShareRuleDeletedEvent_shouldSaveEventToTableOnException() throws InterruptedException {
    // given
    var deletedEvent =
        new ShareRuleDeletedEvent(123L, 900L, 999L, 100L, SharedEntity.LEAD);
    doThrow(new RuntimeException("something went wrong", new RuntimeException("error thrown"))).when(shareRuleFacade)
        .deleteShareRule(any(ShareRuleDeletedEvent.class));

    // when
    rabbitTemplate.convertAndSend(CONFIG_EXCHANGE, SHARE_RULE_DELETED_EVENT, deletedEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);
    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("share_rule") && errorMessage.getEntityId().equals(123L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }

  @Test
  public void givenShareRuleDeletedEvent_shouldRetryAndSaveRetriableEntityAfterLastAttempt() throws InterruptedException {
    // given
    var deletedEvent =
        new ShareRuleDeletedEvent(123L, 900L, 999L, 100L, SharedEntity.LEAD);
    WebClientResponseException webClientResponseException = new WebClientResponseException(404, "404 Not Found from", null, null, null);
    doThrow(webClientResponseException).when(shareRuleFacade)
        .deleteShareRule(any(ShareRuleDeletedEvent.class));

    // when
    rabbitTemplate.convertAndSend(CONFIG_EXCHANGE, SHARE_RULE_DELETED_EVENT, deletedEvent);

    // then
    mockMqListener.latch.await(1, TimeUnit.SECONDS);

    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("share_rule") && errorMessage.getEntityId().equals(123L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).hasSize(1);
  }

  @Test
  public void givenShareRuleCreateOperationForRetry_shouldRetryIt() throws JsonProcessingException {
    //given
    Map<String, Object> shareRuleCreatedEvent = new HashMap<>();
    shareRuleCreatedEvent.put("id", 1);
    shareRuleCreatedEvent.put("tenantId", 1);

    //when
    domainEventListener.retryEntityOperation(EntityType.SHARE_RULE, "sharerule.created", (HashMap<String, Object>) shareRuleCreatedEvent);

    //then
    ArgumentCaptor<ShareRuleEvent> shareRuleEventArgumentCaptor = ArgumentCaptor.forClass(ShareRuleEvent.class);
    verify(shareRuleFacade, times(1)).createOrUpdateShareRule(shareRuleEventArgumentCaptor.capture(), eq(true));
    ShareRuleEvent shareRuleEvent = shareRuleEventArgumentCaptor.getValue();
    Assertions.assertThat(shareRuleEvent.getId()).isEqualTo(1);
    Assertions.assertThat(shareRuleEvent.getTenantId()).isEqualTo(1);
  }

  @Test
  public void givenShareRuleUpdateOperationForRetry_shouldRetryIt() throws JsonProcessingException {
    //given
    Map<String, Object> shareRuleUpdatedEvent = new HashMap<>();
    shareRuleUpdatedEvent.put("id", 1);
    shareRuleUpdatedEvent.put("tenantId", 1);

    //when
    domainEventListener.retryEntityOperation(EntityType.SHARE_RULE, "sharerule.updated", (HashMap<String, Object>) shareRuleUpdatedEvent);

    //then
    ArgumentCaptor<ShareRuleEvent> shareRuleEventArgumentCaptor = ArgumentCaptor.forClass(ShareRuleEvent.class);
    verify(shareRuleFacade, times(1)).createOrUpdateShareRule(shareRuleEventArgumentCaptor.capture(), eq(false));
    ShareRuleEvent shareRuleEvent = shareRuleEventArgumentCaptor.getValue();
    Assertions.assertThat(shareRuleEvent.getId()).isEqualTo(1);
    Assertions.assertThat(shareRuleEvent.getTenantId()).isEqualTo(1);
  }

  @Test
  public void givenShareRuleDeleteOperationForRetry_shouldRetryIt() throws JsonProcessingException {
    //given
    Map<String, Object> shareRuleDeletedEvent = new HashMap<>();
    shareRuleDeletedEvent.put("id", 1);
    shareRuleDeletedEvent.put("tenantId", 1);

    //when
    domainEventListener.retryEntityOperation(EntityType.SHARE_RULE, "sharerule.deleted", (HashMap<String, Object>) shareRuleDeletedEvent);

    //then
    ArgumentCaptor<ShareRuleDeletedEvent> shareRuleDeletedEventArgumentCaptor = ArgumentCaptor.forClass(ShareRuleDeletedEvent.class);
    verify(shareRuleFacade, times(1)).deleteShareRule(shareRuleDeletedEventArgumentCaptor.capture());
    Assertions.assertThat(shareRuleDeletedEventArgumentCaptor.getValue().getId()).isEqualTo(1);
    Assertions.assertThat(shareRuleDeletedEventArgumentCaptor.getValue().getTenantId()).isEqualTo(1);
  }

  @Test
  @Sql("/test-scripts/retry-entity-error-message-for-delete.sql")
  public void givenDealDeleteEvent_shouldRemoveAllRetryEntriesOfEntityOnDeleteSuccess() throws InterruptedException {
    //given
    DealDeletedEvent dealDeletedEventPayload = new DealDeletedEvent(1L, 11L, 999L);

    //when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DEAL_DELETED_EVENT, dealDeletedEventPayload);

    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("deal") && errorMessage.getEntityId().equals(1L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).isEmpty();
  }

  @Test
  @Sql("/test-scripts/retry-entity-error-message-for-delete.sql")
  public void givenLeadDeleteEvent_shouldRemoveAllRetryEntriesOfEntityOnDeleteSuccess() throws InterruptedException {
    //given
    LeadDeletedEventPayload leadDeletedEventPayload = new LeadDeletedEventPayload(1L, 100L, 999L);

    //when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LEAD_DELETED_EVENT, leadDeletedEventPayload);

    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("lead") && errorMessage.getEntityId().equals(1L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).isEmpty();
  }

  @Test
  @Sql("/test-scripts/retry-entity-error-message-for-delete.sql")
  public void givenCallDeleteEvent_shouldRemoveAllRetryEntriesOfEntityOnDeleteSuccess() throws InterruptedException {
    //given
    CallDeletedEvent callDeletedEventPayload = new CallDeletedEvent(1L, 5L);

    //when
    rabbitTemplate.convertAndSend(CALL_EXCHANGE, CALL_DELETED_EVENT, callDeletedEventPayload);

    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("call") && errorMessage.getEntityId().equals(1L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).isEmpty();
  }

  @Test
  @Sql("/test-scripts/retry-entity-error-message-for-delete.sql")
  public void givenTaskDeleteEvent_shouldRemoveAllRetryEntriesOfEntityOnDeleteSuccess() throws InterruptedException {
    //given
    TaskDeletedEvent taskDeletedEvent = new TaskDeletedEvent(1L, 5L);

    //when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TASK_DELETED_EVENT, taskDeletedEvent);

    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("task") && errorMessage.getEntityId().equals(1L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).isEmpty();
  }

  @Test
  @Sql("/test-scripts/retry-entity-error-message-for-delete.sql")
  public void givenShareRuleDeleteEvent_shouldRemoveAllRetryEntriesOfEntityOnDeleteSuccess() throws InterruptedException {
    //given
    ShareRuleDeletedEvent shareRuleDeletedEvent = new ShareRuleDeletedEvent(11L, 222L, 1L, 1L, SharedEntity.LEAD);

    //when
    rabbitTemplate.convertAndSend(CONFIG_EXCHANGE, SHARE_RULE_DELETED_EVENT, shareRuleDeletedEvent);

    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    List<EventPayloadError> allEventPayloadErrors = errorMessageRecoveryFacade.findAll()
        .stream()
        .filter(errorMessage -> errorMessage.getEntityType().equalsIgnoreCase("share_rule") && errorMessage.getEntityId().equals(11L))
        .collect(Collectors.toList());
    Assertions.assertThat(allEventPayloadErrors).isEmpty();
  }

  @Test
  void givenUserCreatedEvent_shouldCreateUser() throws InterruptedException {
    //given
    long userId = 978045L;
    long tenantId = 78458545L;
    String userName = "Tony Stark";
    User userEvent = new User(userId, tenantId, userName);
    //when
    rabbitTemplate.convertAndSend(IAM_EXCHANGE, "iam.user.created", userEvent);
    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    User persistedUser = userFacade.getUserByIdAndTenantId(userId, tenantId).get();
    Assertions.assertThat(persistedUser.getName()).isEqualTo(userName);
  }

  @Test
  public void givenLeadOwnerUpdateEvent_shouldUpdateOwnerInAssociatedEntities() throws InterruptedException {
    //given
    LeadOwnerUpdateEvent leadOwnerUpdateEvent = new LeadOwnerUpdateEvent(1L, 100L, 99L , 101L);
    //when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadOwnerUpdateEvent.getEventName(), leadOwnerUpdateEvent);
    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    verify(organizerFacade, times(1)).updateOrganizerOwnerByEntityIdAndEntity(100L, 1L,"lead");
    verify(meetingRelatedToFacade, times(1)).updateMeetingRelatedToOwnerByEntityIdAndEntity(100L, 1L,"lead");
    verify(meetingInviteeFacade, times(1)).updateMeetingInviteeOwnerByEntityIdAndEntity(100L, 1L,"lead");
    verify(callFacade, times(1)).updateCallAssociatedToOwnersByEntityIdAndEntity(100L, 1L,"lead");
    verify(callFacade, times(1)).updateCallRelatedToOwnersByEntityIdAndEntity(100L, 1L,"lead");
    verify(taskFacade, times(1)).updateTaskRelatedToOwnerIdByEntityIdAndEntity(101L, 100L, 1L,"LEAD");
  }

  @Test
  public void givenDealOwnerUpdateEvent_shouldUpdateOwnerInAssociatedEntities() throws InterruptedException {
    //given
    DealEvent event = new DealEvent();
    event.setOwnedBy(new IdName(100L, "user"));
    event.setId(1L);
    event.setTenantId(11L);
    DealOwnerUpdatedEvent dealOwnerUpdatedEvent = new DealOwnerUpdatedEvent(1L, 99L , event);
    //when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealOwnerUpdatedEvent.getEventName(), dealOwnerUpdatedEvent);
    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    verify(organizerFacade, times(1)).updateOrganizerOwnerByEntityIdAndEntity(100L, 1L,"deal");
    verify(meetingRelatedToFacade, times(1)).updateMeetingRelatedToOwnerByEntityIdAndEntity(100L, 1L,"deal");
    verify(meetingInviteeFacade, times(1)).updateMeetingInviteeOwnerByEntityIdAndEntity(100L, 1L,"deal");
    verify(callFacade, times(1)).updateCallAssociatedToOwnersByEntityIdAndEntity(100L, 1L,"deal");
    verify(callFacade, times(1)).updateCallRelatedToOwnersByEntityIdAndEntity(100L, 1L,"deal");
    verify(taskFacade, times(1)).updateTaskRelatedToOwnerIdByEntityIdAndEntity(11L, 100L, 1L,"DEAL");
  }

  @Test
  public void givenUserEmailUpdatedEvent_shouldUpdateEmailInMeetingInvitee() throws InterruptedException {
    //given
    UserEmailUpdatedEvent event = new UserEmailUpdatedEvent(1L, 1L, "<EMAIL>");

    //when
    rabbitTemplate.convertAndSend(IAM_EXCHANGE, UserEmailUpdatedEvent.getEventName(), event);

    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    verify(meetingInviteeFacade, times(1)).updateMeetingInviteeEmailByEntityIdAndEntity(1L, EntityType.USER,"<EMAIL>");
  }

  @Test
  public void givenLeadUpdatedV2Event_shouldUpdateEmailInMeetingInvitee() throws InterruptedException {
    //given
    LeadEventV2 entity = new LeadEventV2();
    LeadEmail[] emails = new LeadEmail[]{
        new LeadEmail("HOME", "<EMAIL>", true),
    };
    entity.setId(1L);
    entity.setTenantId(1L);
    entity.setEmails(emails);
    LeadEventPayload event = new LeadEventPayload(entity, new LeadEventV2(), new Metadata(1L, 1L));

    //when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEventPayload.getUpdatedEventName(), event);

    //then
    mockMqListener.latch.await(500, TimeUnit.MILLISECONDS);
    verify(meetingInviteeFacade, times(1)).updateMeetingInviteeEmailIfChanged(1L, EntityType.LEAD,
        "<EMAIL>", null);
  }

  private ShareRuleEvent getShareRuleEvent(
      long id,
      long ownerId,
      long tenantId,
      long entityOwnerStakeholderId,
      long entitySharedToStakeholderId,
      ShareStakeholder sharedWithStakeholderType) {
    return new ShareRuleEvent(
        id,
        "my share",
        "share all my leads",
        ShareStakeholder.USER,
        entityOwnerStakeholderId,
        sharedWithStakeholderType,
        entitySharedToStakeholderId,
        true,
        SharedEntity.LEAD,
        900L,
        new Action(),
        tenantId,
        ownerId, new Date(), new Date());
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
  }

  @TestConfiguration
  public static class TestMqSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.rabbitmq.virtual-host=" + "/");
    }
  }
}
