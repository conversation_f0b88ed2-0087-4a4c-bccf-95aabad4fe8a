{"rabbit_version": "3.7", "users": [{"name": "test", "password": "test", "tags": "administrator"}], "vhosts": [{"name": "sling-sales"}], "permissions": [{"user": "test", "vhost": "sling-sales", "configure": ".*", "write": ".*", "read": ".*"}], "parameters": [], "policies": [], "queues": [], "exchanges": [{"name": "ex.iam", "vhost": "sling-sales", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "ex.config", "vhost": "sling-sales", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "ex.sales", "vhost": "sling-sales", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "ex.communication", "vhost": "sling-sales", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "ex.deal", "vhost": "sling-sales", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "ex.company", "vhost": "sling-sales", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "bindings": []}